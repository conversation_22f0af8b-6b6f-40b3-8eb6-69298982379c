# 协议与附加协议关系映射详解

## 1. 文档概述

本文档基于SAP返利系统源代码分析，详细说明协议(ZRET0006)与附加协议的关系，以及协议条款(ZRETA002)与附加协议条款(ZRETA003/ZRETA004)的映射关系和业务逻辑。

## 2. 核心数据表结构

### 2.1 协议主表 (ZRET0006)

<augment_code_snippet path="doc/fanliv1/SAP返利管理系统MySQL数据库设计文档.md" mode="EXCERPT">
````sql
CREATE TABLE zret0006 (
    zxy_id VARCHAR(20) NOT NULL COMMENT '协议ID',
    ztk_id VARCHAR(20) NOT NULL COMMENT '条款ID',
    zbukrs VARCHAR(4) NOT NULL COMMENT '公司代码',
    zflsqf VARCHAR(4) COMMENT '返利收取方',
    zxyzt VARCHAR(1) DEFAULT 'I' COMMENT '协议状态',
    zcjr VARCHAR(12) NOT NULL COMMENT '创建人',
    zcjrq DATE NOT NULL COMMENT '创建日期',
    zcjsj TIME NOT NULL COMMENT '创建时间'
)
````
</augment_code_snippet>

### 2.2 附加条款主表 (ZRETA003)

<augment_code_snippet path="doc/fanliv1/SAP返利管理系统MySQL数据库设计文档.md" mode="EXCERPT">
````sql
CREATE TABLE zreta003 (
    ztk_id VARCHAR(20) NOT NULL COMMENT '条款编号',
    zitems VARCHAR(6) NOT NULL COMMENT '行项目号',
    zatktp VARCHAR(4) NOT NULL COMMENT '附加条款类型',
    ztk_id_plus VARCHAR(20) COMMENT '附加条款编号',
    zrlid VARCHAR(20) COMMENT '关联条款编号'
)
````
</augment_code_snippet>

### 2.3 附加条款配置说明

**重要说明**: 经过代码验证，ZRETA004表在实际系统中并未被使用。附加条款的规则和条件配置直接在ZRETA003表中通过以下字段实现：

- **ZATKRL**: 附加条款规则
- **ZCTGR**: 条件组
- **ZRLID**: 关联条款/协议编号
- **ZATKPI**: 条件指示器

## 3. 附加条款类型 (ZATKTP) 详解

### 3.1 附加条款类型分类

基于代码分析，附加条款类型主要包括：

#### 3.1.1 P类型 - 前序条款 (Predecessor)
- **定义**: 表示当前条款依赖的前序条款
- **业务含义**: 当前条款的执行需要前序条款先执行完成
- **关联字段**: `ZRLID` 指向前序条款的条款编号
- **条件指示器**: `ZATKPI = 'C'` (Condition)

**重要说明**: 实际代码中只使用ZRETA003表存储附加条款信息，ZRETA004表并未在系统中实际使用。

<augment_code_snippet path="src/ZRED0008/ZRED0008F01" mode="EXCERPT">
````abap
SELECT zreta003~zrlid AS ztk_id
  FROM zreta003
  INNER JOIN zreta002 ON zreta003~zrlid = zreta002~ztk_id
  WHERE zreta003~ztk_id = @lt_sel_id-ztk_id
    AND zreta003~zatktp = 'P'    -- 附加条款类型
    AND zreta003~zatkpi = 'C'    -- 条件指示器
````
</augment_code_snippet>

#### 3.1.2 A类型 - 附加协议 (Additional Agreement)
- **定义**: 表示当前条款关联的附加协议
- **业务含义**: 当前条款需要关联到其他协议进行联合计算
- **关联字段**: `ZRLID` 指向关联协议的协议编号
- **条件指示器**: `ZATKPI = 'B'` (Business)

<augment_code_snippet path="src/ZRED0041/ZRED0041_F02_B" mode="EXCERPT">
````abap
IF ps_tk_add-zatktp = 'A'.
  ls_ta03-zatkpi = 'B'.
ELSE.
  ls_ta03-zatkpi = 'C'.
ENDIF.
````
</augment_code_snippet>

### 3.2 附加条款规则 (ZATKRL) 和条件组 (ZCTGR)

#### 3.2.1 P类型的规则配置
- **ZATKRL**: 附加条款规则，定义前序条款的执行规则
- **ZCTGR**: 行项目关联规则，定义如何关联行项目

#### 3.2.2 A类型的规则配置
- **ZATKRL**: 下级协议级别，定义协议层级关系
- **ZCTGR**: 上级协议级别，定义协议的上下级关系

<augment_code_snippet path="src/ZRED0041/ZRED0041_M01" mode="EXCERPT">
````abap
IF gs_tk_add-zatktp = 'P'.
  gs_tk_add-zatkrl_t = '附加条款规则'.
  gs_tk_add-zctgr_t = '行项目关联规则'.
ELSEIF gs_tk_add-zatktp = 'A'.
  gs_tk_add-zatkrl_t = '下级协议级别'.
  gs_tk_add-zctgr_t = '上级协议级别'.
ENDIF.
````
</augment_code_snippet>

## 4. 协议与附加协议的关联关系

### 4.1 数据关联逻辑

#### 4.1.1 主协议到附加协议的关联
```
ZRET0006 (主协议)
    ↓ (通过 ZTK_ID)
ZRETA002 (条款)
    ↓ (通过 ZTK_ID)
ZRETA003 (附加条款)
    ↓ (通过 ZRLID)
ZRET0006 (附加协议)
```

#### 4.1.2 关联查询逻辑

<augment_code_snippet path="src/ZRED0041/ZRED0041_F02_B" mode="EXCERPT">
````abap
" 获取附加条款对应的附加协议
SELECT a~*
  FROM zret0006 AS a
  WHERE a~ztk_id = @ps_tk_add-ztk_id_plus
    AND a~zxyzt NE 'D'
  INTO CORRESPONDING FIELDS OF TABLE @lt_t06.
````
</augment_code_snippet>

### 4.2 附加协议的保存逻辑

#### 4.2.1 A类型附加协议的处理

<augment_code_snippet path="src/ZRED0041/ZRED0041_F02" mode="EXCERPT">
````abap
" 在保存时 类型为A的附加条款会取本次新生成的协议
IF ls_ta04-zatktp = 'A' AND ls_ta04-zrlid IS INITIAL.
  CLEAR ls_tc02.
  READ TABLE pt_tc02 INTO ls_tc02 WITH KEY zitems_key = ls_ta04-zitems_key_zxy_id.
  IF sy-subrc EQ 0.
    ls_ta04-zrlid = ls_tc02-zxy_id.
  ENDIF.
ENDIF.
````
</augment_code_snippet>

## 5. 业务流程与状态控制

### 5.1 附加条款的审批依赖

#### 5.1.1 前序条款审批检查

<augment_code_snippet path="src/ZRED0041/ZRED0041_F03" mode="EXCERPT">
````abap
" 检查附加条款的审批状态
SELECT zrlid
  INTO TABLE @DATA(lt_list)
  FROM zreta003
  INNER JOIN zreta002 ON zreta002~ztk_id = zreta003~zrlid
  WHERE zreta003~ztk_id = @p_ztk_id
    AND zreta002~zxyzt <> 'A'.
IF sy-subrc = 0.
  " 附加条款未审批，不能审批主条款
ENDIF.
````
</augment_code_snippet>

#### 5.1.2 引用条款状态检查

<augment_code_snippet path="src/ZRED0041/ZRED0041_F03" mode="EXCERPT">
````abap
" 检查引用该条款的其他条款状态
SELECT zreta002~ztk_id
  INTO TABLE @DATA(lt_list)
  FROM zreta003
  INNER JOIN zreta002 ON zreta002~ztk_id = zreta003~ztk_id
  WHERE zreta003~zrlid = @p_ztk_id
    AND zreta002~zxyzt = 'A'
    AND zreta002~zleib <> 'R'.
````
</augment_code_snippet>

### 5.2 附加协议的删除逻辑

#### 5.2.1 P类型附加协议删除

<augment_code_snippet path="src/ZRED0041/ZRED0041_F02_B" mode="EXCERPT">
````abap
" P类型附加条款删除时，同时删除对应的附加协议
SELECT a~zxy_id
  FROM @pt_ta03 AS i JOIN zret0006 AS a
                      ON i~zrlid = a~ztk_id
                     AND i~sel = 'X'
                     AND i~zatktp = 'P'
  INTO TABLE @DATA(lt_t06_tmp).

LOOP AT pt_ta04 INTO DATA(ls_ta04).
  READ TABLE lt_t06_tmp TRANSPORTING NO FIELDS WITH KEY zxy_id = ls_ta04-zrlid.
  IF sy-subrc EQ 0.
    DELETE pt_ta04.
  ENDIF.
ENDLOOP.
````
</augment_code_snippet>

#### 5.2.2 A类型附加协议删除

<augment_code_snippet path="src/ZRED0041/ZRED0041_F02_B" mode="EXCERPT">
````abap
" A类型附加条款删除时的处理
LOOP AT pt_ta03 INTO DATA(ls_ta03) WHERE sel = 'X' AND zatktp = 'A'.
  DELETE pt_ta04 WHERE zitems = ls_ta03-zitems AND zatktp = 'A'.
ENDLOOP.
````
</augment_code_snippet>

## 6. 计算逻辑中的附加协议处理

### 6.1 附加条款的计算顺序

在返利计算过程中，系统会按照以下顺序处理：

1. **附加条款计算** (ZRED0008程序)
2. **正常条款计算**

<augment_code_snippet path="src/ZRED0008/ZRED0008F01" mode="EXCERPT">
````abap
" 先处理附加条款
LOOP AT gt_ztk_id_ats INTO DATA(ls_ztk_id).
  SELECT zret0006~zxy_id
    FROM zret0006
    INNER JOIN zreta002 ON zret0006~ztk_id = zreta002~ztk_id
    WHERE zret0006~ztk_id = ls_ztk_id-ztk_id
      AND zret0006~zbegin <= lv_yestdat
    INTO TABLE lt_zxyid.
ENDLOOP.
````
</augment_code_snippet>

### 6.2 前序条款的递归查询

系统支持多层级的前序条款依赖关系：

<augment_code_snippet path="src/ZRED0008/ZRED0008F01" mode="EXCERPT">
````abap
" 条款的前序条款递归查询
DO 10 TIMES.
  IF lt_sel_id IS INITIAL.
    EXIT.
  ENDIF.
  
  SELECT zreta003~zrlid AS ztk_id
    FROM zreta003
    INNER JOIN zreta002 ON zreta003~zrlid = zreta002~ztk_id
    FOR ALL ENTRIES IN @lt_sel_id
    WHERE zreta003~ztk_id = @lt_sel_id-ztk_id
      AND zreta003~zatktp = 'P'
      AND zreta003~zatkpi = 'C'
    INTO TABLE @lt_tk_id.
ENDDO.
````
</augment_code_snippet>

## 7. 数据一致性控制

### 7.1 重复添加检查

#### 7.1.1 P类型重复检查

<augment_code_snippet path="src/ZRED0041/ZRED0041_F02_B" mode="EXCERPT">
````abap
IF ps_tk_add-zatktp = 'P'.
  READ TABLE pt_ta03 TRANSPORTING NO FIELDS WITH KEY zrlid = ps_tk_add-ztk_id_plus.
  IF sy-subrc EQ 0.
    PERFORM frm_add_msg USING '该附加条款已经添加,不能重复添加'.
  ENDIF.
````
</augment_code_snippet>

#### 7.1.2 A类型重复检查

<augment_code_snippet path="src/ZRED0041/ZRED0041_F02_B" mode="EXCERPT">
````abap
ELSEIF ps_tk_add-zatktp = 'A'.
  READ TABLE pt_ta03 TRANSPORTING NO FIELDS 
    WITH KEY zatktp = ps_tk_add-zatktp 
             zatkrl = ps_tk_add-zatkrl 
             zctgr = ps_tk_add-zctgr.
  IF sy-subrc EQ 0.
    PERFORM frm_add_msg USING '该附加条款已经添加,不能重复添加'.
  ENDIF.
````
</augment_code_snippet>

### 7.2 业务规则限制

#### 7.2.1 计算方法限制

<augment_code_snippet path="src/ZRED0041/ZRED0041_F02_B" mode="EXCERPT">
````abap
IF ps_tk_add-zatktp = 'P'.
  IF ps_tc05-zjsff = 'R'.
    PERFORM frm_add_msg USING '若阶梯计算方法为"R-补差"，则附加条款不允许增加"附加条款类型为P"的附加条款'.
  ENDIF.
ENDIF.
````
</augment_code_snippet>

#### 7.2.2 条款类型限制

<augment_code_snippet path="src/ZRED0041/ZRED0041_F02_B" mode="EXCERPT">
````abap
SELECT SINGLE zleib INTO @DATA(lv_zleib) FROM zreta002 WHERE ztk_id = @ps_tk_add-ztk_id_plus.
IF sy-subrc = 0 AND lv_zleib = 'R'.
  PERFORM frm_add_msg USING '该条款为条款申请,不能附加'.
ENDIF.
````
</augment_code_snippet>

## 8. 总结

### 8.1 关键关系映射

1. **主协议 → 附加协议**: 通过ZRETA003.ZRLID字段关联
2. **条款 → 附加条款**: 通过ZRETA003.ZTK_ID字段关联  
3. **附加条款 → 附加协议**: 通过ZRETA003.ZTK_ID_PLUS字段关联

### 8.2 业务逻辑要点

1. **P类型**: 用于前序条款依赖，支持多层级递归
2. **A类型**: 用于协议间关联，支持联合计算
3. **状态控制**: 附加条款必须先审批，主条款才能审批
4. **计算顺序**: 附加条款先计算，再计算正常条款
5. **数据一致性**: 严格的重复检查和业务规则限制

这种设计实现了复杂的返利业务场景，支持条款间的依赖关系和协议间的关联计算。

## 9. 数据表关系ER图

### 9.1 完整ER关系图

```mermaid
erDiagram
    %% 合同层级
    ZRETA001 {
        string ZHT_ID PK "合同编号"
        string ZHTLX "合同类型"
        string ZBUKRS "公司代码"
        string ZHT_TXT "合同描述"
        string ZHTID "关联合同号"
        string ZBPTYPE "伙伴类型"
        string ZBPCODE "伙伴代码"
        string EKGRP "采购组"
        string ZHTYEAR "签署年度"
        date ZBEGIN "开始日期"
        date ZEND "结束日期"
        string ZTMPID "组织模板"
    }

    %% 条款层级
    ZRETA002 {
        string ZTK_ID PK "条款编号"
        string ZHT_ID FK "合同编号"
        string ZITEM_ID "行项目号"
        string ZFLLX "返利类型"
        string ZTKTYPE "条款类型"
        string ZTK_TXT "条款描述"
        string ZSPZ_ID "任务商品组编号"
        string ZFLSPZ_ID "返利商品组编号"
        date ZBEGIN "开始日期"
        date ZEND "结束日期"
        string ZTMPID "组织模板"
        string ZXYZT "协议状态"
        string ZCLRID "计算规则ID"
        string ZXYBSTYP "协议业务类型"
        string ZLEIB "类别"
    }

    %% 附加条款主表
    ZRETA003 {
        string ZTK_ID PK,FK "条款编号"
        string ZITEMS PK "行项目号"
        string ZATKTP "附加条款类型"
        string ZTK_ID_PLUS "附加条款编号"
        string ZRLID "关联条款/协议编号"
        string ZATKPI "条件指示器"
        string ZATKTXT "附加条款描述"
        string ZATKRL "附加条款规则"
        string ZCTGR "条件组"
        string ZCJR "创建人"
        date ZCJRQ "创建日期"
        time ZCJSJ "创建时间"
    }

    %% 注意: ZRETA004表在实际代码中未使用
    %% 附加条款的规则和条件直接在ZRETA003中配置

    %% 协议主表
    ZRET0006 {
        string ZXY_ID PK "协议编号"
        string ZTK_ID FK "条款编号"
        string ZBUKRS "公司代码"
        string ZFLLX "返利类型"
        string ZHTLX "合同类型"
        string ZFLZFF "返利支付方"
        string ZFLSQF "申请方"
        string ZXYZT "协议状态"
        string ZXYBSTYP "协议业务类型"
        date ZBEGIN "开始日期"
        date ZEND "结束日期"
        string ZJT_ID "阶梯编号"
        string ZXY_TXT "协议描述"
        string EKGRP "采购组"
        string ZHSTYPE "核算类型"
        string ZHSZQ "核算周期"
        string ZJSZQ "结算周期"
        string ZHSJZ "核算基准"
        string ZFLHSJZ "返利核算基准"
    }

    %% 动态协议明细表
    ZRET0007 {
        string ZXY_ID PK,FK "协议编号"
        int ZXY_ITEMID PK "行项目号"
        string ZSPWD "商品维度"
        string MATNR "物料编号"
        string ZSPZ_ID "商品组编号"
        decimal ZRWL "任务量"
        string MEINS "单位"
        decimal ZJG "价格"
        string WAERS "货币"
    }

    %% 固定协议明细表
    ZRET0008 {
        string ZXY_ID PK,FK "协议编号"
        int ZXY_ITEMID PK "行项目号"
        string MATNR "物料编号"
        string LIFNR "供应商"
        date ZDATE "日期"
        decimal ZJE "金额"
        string WAERS "货币"
        decimal ZSL "数量"
        string MEINS "单位"
    }

    %% 返利协议明细表
    ZRET0039 {
        string ZXY_ID PK,FK "协议编号"
        int ZXY_ITEMID PK "行项目号"
        string ZSPWD "商品维度"
        string MATNR "物料编号"
        string ZSPZ_ID "商品组编号"
        decimal ZFLRWL "返利任务量"
        string MEINS "单位"
        decimal ZJG "价格"
        string WAERS "货币"
    }

    %% 基本关联关系
    ZRETA001 ||--o{ ZRETA002 : "ZHT_ID"
    ZRETA002 ||--o{ ZRET0006 : "ZTK_ID"
    ZRETA002 ||--o{ ZRETA003 : "ZTK_ID"

    %% 协议明细关系
    ZRET0006 ||--o{ ZRET0007 : "ZXY_ID"
    ZRET0006 ||--o{ ZRET0008 : "ZXY_ID"
    ZRET0006 ||--o{ ZRET0039 : "ZXY_ID"

    %% 附加关系映射 (虚线表示逻辑关联)
    ZRETA003 }o--o| ZRETA002 : "ZRLID→ZTK_ID (P类型)"
    ZRETA003 }o--o| ZRET0006 : "ZRLID→ZXY_ID (A类型)"
```

### 9.2 关键链接键说明

#### 9.2.1 主要外键关系

| 子表 | 父表 | 外键字段 | 父表主键 | 关系类型 | 说明 |
|------|------|----------|----------|----------|------|
| **ZRETA002** | ZRETA001 | ZHT_ID | ZHT_ID | 1:N | 一个合同包含多个条款 |
| **ZRET0006** | ZRETA002 | ZTK_ID | ZTK_ID | 1:N | 一个条款包含多个协议 |
| **ZRETA003** | ZRETA002 | ZTK_ID | ZTK_ID | 1:N | 一个条款包含多个附加条款 |
| **ZRET0007** | ZRET0006 | ZXY_ID | ZXY_ID | 1:N | 一个协议包含多个动态明细 |
| **ZRET0008** | ZRET0006 | ZXY_ID | ZXY_ID | 1:N | 一个协议包含多个固定明细 |
| **ZRET0039** | ZRET0006 | ZXY_ID | ZXY_ID | 1:N | 一个协议包含多个返利明细 |

#### 9.2.2 逻辑关联关系

| 源表 | 目标表 | 关联字段 | 目标字段 | 条件 | 说明 |
|------|--------|----------|----------|------|------|
| **ZRETA003** | ZRETA002 | ZRLID | ZTK_ID | ZATKTP='P' | P类型指向前序条款 |
| **ZRETA003** | ZRET0006 | ZRLID | ZXY_ID | ZATKTP='A' | A类型指向附加协议 |

### 9.3 复合主键结构

#### 9.3.1 ZRETA003 (附加条款主表)
```sql
PRIMARY KEY (ZTK_ID, ZITEMS)
```
- **ZTK_ID**: 主条款编号
- **ZITEMS**: 行项目号

#### 9.3.2 说明
**ZRETA004表在实际系统中未使用**，附加条款的规则和条件配置直接在ZRETA003表中实现。

#### 9.3.3 ZRET0007/ZRET0008/ZRET0039 (协议明细表)
```sql
PRIMARY KEY (ZXY_ID, ZXY_ITEMID)
```
- **ZXY_ID**: 协议编号
- **ZXY_ITEMID**: 行项目号

### 9.4 索引设计建议

#### 9.4.1 性能优化索引
```sql
-- ZRETA003 附加条款主表
CREATE INDEX idx_zreta003_zatktp ON ZRETA003(ZATKTP);
CREATE INDEX idx_zreta003_zrlid ON ZRETA003(ZRLID);
CREATE INDEX idx_zreta003_zatkpi ON ZRETA003(ZATKPI);

-- 注意: ZRETA004表在实际系统中未使用

-- ZRET0006 协议主表
CREATE INDEX idx_zret0006_ztk_id ON ZRET0006(ZTK_ID);
CREATE INDEX idx_zret0006_zxyzt ON ZRET0006(ZXYZT);
CREATE INDEX idx_zret0006_zbukrs ON ZRET0006(ZBUKRS);
```

#### 9.4.2 业务查询索引
```sql
-- 支持附加条款类型查询
CREATE INDEX idx_zreta003_type_status ON ZRETA003(ZATKTP, ZATKPI);

-- 支持协议状态查询
CREATE INDEX idx_zret0006_status_date ON ZRET0006(ZXYZT, ZBEGIN, ZEND);

-- 支持条款状态查询
CREATE INDEX idx_zreta002_status_type ON ZRETA002(ZXYZT, ZXYBSTYP);
```

### 9.5 数据流向关系图

```mermaid
flowchart TD
    subgraph "合同管理层"
        A[ZRETA001<br/>合同主表]
    end

    subgraph "条款管理层"
        B[ZRETA002<br/>条款主表]
        C[ZRETA003<br/>附加条款主表]
    end

    subgraph "协议执行层"
        E[ZRET0006<br/>协议主表]
        F[ZRET0007<br/>动态协议明细]
        G[ZRET0008<br/>固定协议明细]
        H[ZRET0039<br/>返利协议明细]
    end

    %% 数据流向
    A -->|ZHT_ID| B
    B -->|ZTK_ID| C
    B -->|ZTK_ID| E
    E -->|ZXY_ID| F
    E -->|ZXY_ID| G
    E -->|ZXY_ID| H

    %% 逻辑关联
    C -.->|ZRLID<br/>P类型| B
    C -.->|ZRLID<br/>A类型| E

    %% 标注关键字段
    A1[主键: ZHT_ID<br/>合同编号]
    B1[主键: ZTK_ID<br/>外键: ZHT_ID]
    C1[主键: ZTK_ID+ZITEMS<br/>外键: ZTK_ID<br/>逻辑键: ZRLID]
    E1[主键: ZXY_ID<br/>外键: ZTK_ID]
    F1[主键: ZXY_ID+ZXY_ITEMID<br/>外键: ZXY_ID]

    A --- A1
    B --- B1
    C --- C1
    E --- E1
    F --- F1
```

## 10. 关键查询示例

### 10.1 基础关联查询

#### 10.1.1 查询条款的所有附加条款
```sql
-- 查询指定条款的所有附加条款信息
SELECT
    a.ZTK_ID as 主条款编号,
    a.ZTK_TXT as 主条款描述,
    b.ZITEMS as 附加条款行号,
    b.ZATKTP as 附加条款类型,
    b.ZRLID as 关联编号,
    b.ZATKPI as 条件指示器,
    CASE b.ZATKTP
        WHEN 'P' THEN '前序条款'
        WHEN 'A' THEN '附加协议'
        ELSE '其他'
    END as 类型说明
FROM ZRETA002 a
INNER JOIN ZRETA003 b ON a.ZTK_ID = b.ZTK_ID
WHERE a.ZTK_ID = 'TK202401001'
ORDER BY b.ZITEMS;
```

#### 10.1.2 查询附加协议的完整信息
```sql
-- 查询A类型附加条款对应的协议信息
SELECT
    a.ZTK_ID as 主条款编号,
    a.ZITEMS as 附加条款行号,
    a.ZRLID as 附加协议编号,
    b.ZXY_TXT as 附加协议描述,
    b.ZXYZT as 协议状态,
    b.ZBEGIN as 协议开始日期,
    b.ZEND as 协议结束日期
FROM ZRETA003 a
INNER JOIN ZRET0006 b ON a.ZRLID = b.ZXY_ID
WHERE a.ZATKTP = 'A'
  AND a.ZTK_ID = 'TK202401001';
```

### 10.2 复杂业务查询

#### 10.2.1 递归查询前序条款链
```sql
-- 查询条款的完整前序依赖链
WITH RECURSIVE clause_hierarchy AS (
    -- 起始条款
    SELECT
        ZTK_ID,
        ZTK_TXT,
        0 as level,
        ZTK_ID as root_clause
    FROM ZRETA002
    WHERE ZTK_ID = 'TK202401001'

    UNION ALL

    -- 递归查询前序条款
    SELECT
        p.ZRLID as ZTK_ID,
        c.ZTK_TXT,
        ch.level + 1,
        ch.root_clause
    FROM clause_hierarchy ch
    INNER JOIN ZRETA003 p ON ch.ZTK_ID = p.ZTK_ID
    INNER JOIN ZRETA002 c ON p.ZRLID = c.ZTK_ID
    WHERE p.ZATKTP = 'P'
      AND ch.level < 10  -- 防止无限递归
)
SELECT
    level as 层级,
    ZTK_ID as 条款编号,
    ZTK_TXT as 条款描述,
    root_clause as 根条款
FROM clause_hierarchy
ORDER BY level DESC;
```

#### 10.2.2 查询条款的所有关联协议
```sql
-- 查询条款直接和间接关联的所有协议
SELECT DISTINCT
    '直接协议' as 关联类型,
    a.ZXY_ID as 协议编号,
    a.ZXY_TXT as 协议描述,
    a.ZXYZT as 协议状态
FROM ZRET0006 a
WHERE a.ZTK_ID = 'TK202401001'

UNION ALL

SELECT DISTINCT
    '附加协议' as 关联类型,
    c.ZXY_ID as 协议编号,
    c.ZXY_TXT as 协议描述,
    c.ZXYZT as 协议状态
FROM ZRETA003 b
INNER JOIN ZRET0006 c ON b.ZRLID = c.ZXY_ID
WHERE b.ZTK_ID = 'TK202401001'
  AND b.ZATKTP = 'A'
ORDER BY 关联类型, 协议编号;
```

### 10.3 数据一致性检查查询

#### 10.3.1 检查孤立的附加条款
```sql
-- 检查ZRETA003中存在但对应条款不存在的记录
SELECT
    a.ZTK_ID,
    a.ZITEMS,
    a.ZATKTP,
    a.ZRLID,
    '主条款不存在' as 问题类型
FROM ZRETA003 a
LEFT JOIN ZRETA002 b ON a.ZTK_ID = b.ZTK_ID
WHERE b.ZTK_ID IS NULL

UNION ALL

-- 检查P类型附加条款指向的条款不存在
SELECT
    a.ZTK_ID,
    a.ZITEMS,
    a.ZATKTP,
    a.ZRLID,
    '前序条款不存在' as 问题类型
FROM ZRETA003 a
LEFT JOIN ZRETA002 c ON a.ZRLID = c.ZTK_ID
WHERE a.ZATKTP = 'P'
  AND c.ZTK_ID IS NULL

UNION ALL

-- 检查A类型附加条款指向的协议不存在
SELECT
    a.ZTK_ID,
    a.ZITEMS,
    a.ZATKTP,
    a.ZRLID,
    '附加协议不存在' as 问题类型
FROM ZRETA003 a
LEFT JOIN ZRET0006 d ON a.ZRLID = d.ZXY_ID
WHERE a.ZATKTP = 'A'
  AND d.ZXY_ID IS NULL;
```

#### 10.3.2 检查循环依赖
```sql
-- 检查条款间是否存在循环依赖
WITH RECURSIVE cycle_check AS (
    SELECT
        ZTK_ID,
        ZRLID,
        1 as depth,
        ZTK_ID as start_clause,
        ZTK_ID || '->' || ZRLID as path
    FROM ZRETA003
    WHERE ZATKTP = 'P'

    UNION ALL

    SELECT
        p.ZTK_ID,
        p.ZRLID,
        c.depth + 1,
        c.start_clause,
        c.path || '->' || p.ZRLID
    FROM ZRETA003 p
    INNER JOIN cycle_check c ON p.ZTK_ID = c.ZRLID
    WHERE p.ZATKTP = 'P'
      AND c.depth < 10
      AND p.ZRLID != c.start_clause  -- 检测循环
)
SELECT
    start_clause as 起始条款,
    path as 依赖路径,
    depth as 依赖深度
FROM cycle_check
WHERE ZRLID = start_clause  -- 发现循环
ORDER BY start_clause, depth;
```

## 11. 数据表关系总结

### 11.1 核心关系链路

#### 11.1.1 主要数据流向
```
合同(ZRETA001)
    ↓ [ZHT_ID]
条款(ZRETA002)
    ↓ [ZTK_ID]                    ↓ [ZTK_ID]
协议(ZRET0006)                附加条款(ZRETA003)
    ↓ [ZXY_ID]                    ↓ [ZTK_ID+ZITEMS]
协议明细(ZRET0007/0008/0039)   附加条款明细(ZRETA004)
```

#### 11.1.2 逻辑关联链路
```
附加条款(ZRETA003)
    ↓ [ZRLID] (当ZATKTP='P')
前序条款(ZRETA002)

附加条款(ZRETA003)
    ↓ [ZRLID] (当ZATKTP='A')
附加协议(ZRET0006)
```

### 11.2 关键设计特点

#### 11.2.1 灵活的关联机制
- **ZRLID字段**: 根据ZATKTP类型指向不同的目标表
- **条件指示器**: ZATKPI区分业务逻辑和条件逻辑
- **复合主键**: 支持一个条款下多个附加条款配置

#### 11.2.2 完整的约束体系
- **外键约束**: 保证数据引用完整性
- **业务约束**: 通过程序逻辑控制业务规则
- **状态约束**: 附加条款状态影响主条款审批

#### 11.2.3 高效的查询支持
- **索引设计**: 针对常用查询路径优化
- **递归查询**: 支持多层级前序条款依赖
- **数据一致性**: 提供完整的数据检查机制

### 11.3 业务价值

#### 11.3.1 支持复杂业务场景
- **条款依赖**: P类型支持前序条款依赖关系
- **协议关联**: A类型支持协议间联合计算
- **灵活配置**: 通过规则和条件组实现灵活配置

#### 11.3.2 保证数据一致性
- **级联控制**: 删除时的级联检查和处理
- **状态联动**: 附加条款状态影响主条款状态
- **循环检测**: 防止条款间形成循环依赖

#### 11.3.3 提升系统性能
- **计算优化**: 附加条款先计算，优化计算顺序
- **缓存支持**: 通过索引支持高效的数据访问
- **批量处理**: 支持批量的附加条款处理

### 11.4 最佳实践建议

#### 11.4.1 数据设计
1. **合理使用复合主键**: 确保数据唯一性的同时保持查询效率
2. **建立完整索引**: 针对业务查询模式建立合适的索引
3. **定期数据检查**: 使用一致性检查查询定期验证数据完整性

#### 11.4.2 业务实现
1. **严格状态控制**: 确保附加条款审批先于主条款审批
2. **防止循环依赖**: 在创建前序条款关系时检查循环依赖
3. **优化计算顺序**: 按照依赖关系正确安排计算顺序

#### 11.4.3 系统维护
1. **监控数据质量**: 定期检查孤立记录和无效关联
2. **性能调优**: 根据查询模式调整索引策略
3. **版本管理**: 对关键配置变更进行版本控制

这种精心设计的数据表关系结构，为SAP返利系统提供了强大而灵活的业务支撑能力，能够满足复杂的企业级返利管理需求。
```







```mermaid
graph TB
    subgraph "合同层级"
        A[ZRETA001<br/>合同主表<br/>ZHT_ID]
    end
    
    subgraph "条款层级"
        B[ZRETA002<br/>条款主表<br/>ZTK_ID]
        B1[ZRETA002<br/>附加条款<br/>ZTK_ID_PLUS]
    end
    
    subgraph "附加条款配置"
        C[ZRETA003<br/>附加条款主表]
        D[ZRETA004<br/>附加条款明细表]
    end
    
    subgraph "协议层级"
        E[ZRET0006<br/>主协议<br/>ZXY_ID]
        F[ZRET0006<br/>附加协议<br/>ZXY_ID_PLUS]
    end
    
    subgraph "协议明细"
        G[ZRET0007<br/>动态协议明细]
        H[ZRET0008<br/>固定协议明细]
        I[ZRET0039<br/>返利协议明细]
    end
    
    %% 基本关联关系
    A -->|1:N| B
    B -->|1:N| E
    B1 -->|1:N| F
    
    %% 附加条款关系
    B -->|1:N| C
    C -->|1:N| D
    
    %% 协议明细关系
    E -->|1:N| G
    E -->|1:N| H
    E -->|1:N| I
    F -->|1:N| G
    F -->|1:N| H
    F -->|1:N| I
    
    %% 附加关系映射
    C -.->|ZRLID<br/>P类型| B1
    C -.->|ZRLID<br/>A类型| F
    
    %% 字段说明
    C1[ZRETA003关键字段:<br/>• ZTK_ID: 主条款编号<br/>• ZITEMS: 行项目号<br/>• ZATKTP: 附加条款类型<br/>• ZRLID: 关联条款/协议编号<br/>• ZATKPI: 条件指示器]
    
    D1[ZRETA004关键字段:<br/>• ZATKRL: 附加条款规则<br/>• ZCTGR: 条件组<br/>• ZATKRL_VAL: 规则值<br/>• ZCTGR_VAL: 条件值]
    
    %% 类型说明
    T1[P类型 - 前序条款:<br/>• ZATKPI = 'C'<br/>• ZRLID → 前序条款编号<br/>• 用于条款依赖关系]
    
    T2[A类型 - 附加协议:<br/>• ZATKPI = 'B'<br/>• ZRLID → 附加协议编号<br/>• 用于协议关联计算]
    
    %% 样式定义
    classDef tableBox fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef configBox fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef agreementBox fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef detailBox fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef infoBox fill:#fafafa,stroke:#616161,stroke-width:1px
    
    class A,B,B1 tableBox
    class C,D configBox
    class E,F agreementBox
    class G,H,I detailBox
    class C1,D1,T1,T2 infoBox
```









```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as 条款维护界面
    participant Validator as 数据验证器
    participant ClauseManager as 条款管理器
    participant AgreementManager as 协议管理器
    participant DB as 数据库
    
    Note over User,DB: 附加条款创建流程
    
    User->>UI: 1. 选择主条款
    UI->>ClauseManager: 2. 加载主条款信息
    ClauseManager->>DB: 3. 查询ZRETA002主条款
    DB-->>ClauseManager: 4. 返回条款信息
    ClauseManager-->>UI: 5. 显示条款详情
    
    User->>UI: 6. 添加附加条款
    UI->>UI: 7. 显示附加条款类型选择
    Note over UI: P类型-前序条款 / A类型-附加协议
    
    User->>UI: 8. 选择附加条款类型(P/A)
    
    alt P类型 - 前序条款
        UI->>UI: 9. 显示前序条款选择界面
        User->>UI: 10. 选择前序条款编号
        UI->>Validator: 11. 验证前序条款
        Validator->>DB: 12. 检查条款状态和依赖
        DB-->>Validator: 13. 返回验证结果
        
        alt 验证通过
            Validator-->>UI: 14. 验证成功
            UI->>ClauseManager: 15. 创建P类型附加条款
            ClauseManager->>DB: 16. 插入ZRETA003记录
            Note over DB: ZATKTP='P', ZATKPI='C'<br/>ZRLID=前序条款编号
            ClauseManager->>DB: 17. 插入ZRETA004明细
            DB-->>ClauseManager: 18. 保存成功
        else 验证失败
            Validator-->>UI: 19. 返回错误信息
            UI-->>User: 20. 显示"该条款不能作为前序条款"
        end
        
    else A类型 - 附加协议
        UI->>UI: 21. 显示协议级别配置界面
        User->>UI: 22. 配置协议级别关系
        UI->>Validator: 23. 验证协议关联规则
        Validator->>DB: 24. 检查协议状态和规则
        DB-->>Validator: 25. 返回验证结果
        
        alt 验证通过
            Validator-->>UI: 26. 验证成功
            UI->>ClauseManager: 27. 创建A类型附加条款
            ClauseManager->>DB: 28. 插入ZRETA003记录
            Note over DB: ZATKTP='A', ZATKPI='B'<br/>ZRLID=关联协议编号
            ClauseManager->>DB: 29. 插入ZRETA004明细
            ClauseManager->>AgreementManager: 30. 创建协议关联
            AgreementManager->>DB: 31. 更新ZRET0006协议关联
            DB-->>AgreementManager: 32. 保存成功
        else 验证失败
            Validator-->>UI: 33. 返回错误信息
            UI-->>User: 34. 显示"协议关联规则不符合"
        end
    end
    
    Note over User,DB: 附加协议计算流程
    
    User->>UI: 35. 执行返利计算
    UI->>AgreementManager: 36. 启动计算引擎
    
    AgreementManager->>DB: 37. 查询附加条款(ZRETA003)
    DB-->>AgreementManager: 38. 返回附加条款列表
    
    loop 处理每个附加条款
        AgreementManager->>AgreementManager: 39. 判断附加条款类型
        
        alt P类型处理
            AgreementManager->>DB: 40. 递归查询前序条款
            Note over DB: 支持多层级前序依赖
            DB-->>AgreementManager: 41. 返回前序条款链
            AgreementManager->>AgreementManager: 42. 按依赖顺序计算
            
        else A类型处理
            AgreementManager->>DB: 43. 查询关联协议
            DB-->>AgreementManager: 44. 返回关联协议信息
            AgreementManager->>AgreementManager: 45. 执行联合计算
        end
        
        AgreementManager->>DB: 46. 保存计算结果
    end
    
    AgreementManager->>DB: 47. 查询正常条款
    DB-->>AgreementManager: 48. 返回正常条款列表
    AgreementManager->>AgreementManager: 49. 执行正常条款计算
    AgreementManager->>DB: 50. 保存最终结果
    
    AgreementManager-->>UI: 51. 返回计算完成
    UI-->>User: 52. 显示"返利计算完成"
    
    Note over User,DB: 状态控制和审批流程
    
    User->>UI: 53. 提交条款审批
    UI->>Validator: 54. 检查附加条款状态
    Validator->>DB: 55. 查询附加条款审批状态
    
    alt 附加条款未审批
        DB-->>Validator: 56. 存在未审批附加条款
        Validator-->>UI: 57. 返回审批阻止
        UI-->>User: 58. 显示"附加条款未审批，请先审批附加条款"
        
    else 附加条款已审批
        DB-->>Validator: 59. 所有附加条款已审批
        Validator-->>UI: 60. 允许审批
        UI->>ClauseManager: 61. 执行条款审批
        ClauseManager->>DB: 62. 更新条款状态为已审批
        DB-->>ClauseManager: 63. 更新成功
        ClauseManager-->>UI: 64. 审批完成
        UI-->>User: 65. 显示"条款审批成功"
    end
```

