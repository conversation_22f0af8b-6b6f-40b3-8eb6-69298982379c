# 协议与附加协议关系映射详解

## 1. 文档概述

本文档基于SAP返利系统源代码分析，详细说明协议(ZRET0006)与附加协议的关系，以及协议条款(ZRETA002)与附加协议条款(ZRETA003/ZRETA004)的映射关系和业务逻辑。

## 2. 核心数据表结构

### 2.1 协议主表 (ZRET0006)

<augment_code_snippet path="doc/fanliv1/SAP返利管理系统MySQL数据库设计文档.md" mode="EXCERPT">
````sql
CREATE TABLE zret0006 (
    zxy_id VARCHAR(20) NOT NULL COMMENT '协议ID',
    ztk_id VARCHAR(20) NOT NULL COMMENT '条款ID',
    zbukrs VARCHAR(4) NOT NULL COMMENT '公司代码',
    zflsqf VARCHAR(4) COMMENT '返利收取方',
    zxyzt VARCHAR(1) DEFAULT 'I' COMMENT '协议状态',
    zcjr VARCHAR(12) NOT NULL COMMENT '创建人',
    zcjrq DATE NOT NULL COMMENT '创建日期',
    zcjsj TIME NOT NULL COMMENT '创建时间'
)
````
</augment_code_snippet>

### 2.2 附加条款主表 (ZRETA003)

<augment_code_snippet path="doc/fanliv1/SAP返利管理系统MySQL数据库设计文档.md" mode="EXCERPT">
````sql
CREATE TABLE zreta003 (
    ztk_id VARCHAR(20) NOT NULL COMMENT '条款编号',
    zitems VARCHAR(6) NOT NULL COMMENT '行项目号',
    zatktp VARCHAR(4) NOT NULL COMMENT '附加条款类型',
    ztk_id_plus VARCHAR(20) COMMENT '附加条款编号',
    zrlid VARCHAR(20) COMMENT '关联条款编号'
)
````
</augment_code_snippet>

### 2.3 附加条款明细表 (ZRETA004)

<augment_code_snippet path="doc/fanliv1/SAP返利管理系统MySQL数据库设计文档.md" mode="EXCERPT">
````sql
CREATE TABLE zreta004 (
    ztk_id VARCHAR(20) NOT NULL COMMENT '条款编号',
    zitems VARCHAR(6) NOT NULL COMMENT '行项目号',
    zatkrl VARCHAR(10) NOT NULL COMMENT '附加条款规则',
    zctgr VARCHAR(10) NOT NULL COMMENT '条件组',
    zatkrl_val VARCHAR(50) COMMENT '规则值',
    zctgr_val VARCHAR(50) COMMENT '条件值'
)
````
</augment_code_snippet>

## 3. 附加条款类型 (ZATKTP) 详解

### 3.1 附加条款类型分类

基于代码分析，附加条款类型主要包括：

#### 3.1.1 P类型 - 前序条款 (Predecessor)
- **定义**: 表示当前条款依赖的前序条款
- **业务含义**: 当前条款的执行需要前序条款先执行完成
- **关联字段**: `ZRLID` 指向前序条款的条款编号
- **条件指示器**: `ZATKPI = 'C'` (Condition)

<augment_code_snippet path="src/ZRED0008/ZRED0008F01" mode="EXCERPT">
````abap
SELECT zreta003~zrlid AS ztk_id
  FROM zreta003
  INNER JOIN zreta002 ON zreta003~zrlid = zreta002~ztk_id
  WHERE zreta003~ztk_id = @lt_sel_id-ztk_id
    AND zreta003~zatktp = 'P'    -- 附加条款类型
    AND zreta003~zatkpi = 'C'    -- 条件指示器
````
</augment_code_snippet>

#### 3.1.2 A类型 - 附加协议 (Additional Agreement)
- **定义**: 表示当前条款关联的附加协议
- **业务含义**: 当前条款需要关联到其他协议进行联合计算
- **关联字段**: `ZRLID` 指向关联协议的协议编号
- **条件指示器**: `ZATKPI = 'B'` (Business)

<augment_code_snippet path="src/ZRED0041/ZRED0041_F02_B" mode="EXCERPT">
````abap
IF ps_tk_add-zatktp = 'A'.
  ls_ta03-zatkpi = 'B'.
ELSE.
  ls_ta03-zatkpi = 'C'.
ENDIF.
````
</augment_code_snippet>

### 3.2 附加条款规则 (ZATKRL) 和条件组 (ZCTGR)

#### 3.2.1 P类型的规则配置
- **ZATKRL**: 附加条款规则，定义前序条款的执行规则
- **ZCTGR**: 行项目关联规则，定义如何关联行项目

#### 3.2.2 A类型的规则配置
- **ZATKRL**: 下级协议级别，定义协议层级关系
- **ZCTGR**: 上级协议级别，定义协议的上下级关系

<augment_code_snippet path="src/ZRED0041/ZRED0041_M01" mode="EXCERPT">
````abap
IF gs_tk_add-zatktp = 'P'.
  gs_tk_add-zatkrl_t = '附加条款规则'.
  gs_tk_add-zctgr_t = '行项目关联规则'.
ELSEIF gs_tk_add-zatktp = 'A'.
  gs_tk_add-zatkrl_t = '下级协议级别'.
  gs_tk_add-zctgr_t = '上级协议级别'.
ENDIF.
````
</augment_code_snippet>

## 4. 协议与附加协议的关联关系

### 4.1 数据关联逻辑

#### 4.1.1 主协议到附加协议的关联
```
ZRET0006 (主协议)
    ↓ (通过 ZTK_ID)
ZRETA002 (条款)
    ↓ (通过 ZTK_ID)
ZRETA003 (附加条款)
    ↓ (通过 ZRLID)
ZRET0006 (附加协议)
```

#### 4.1.2 关联查询逻辑

<augment_code_snippet path="src/ZRED0041/ZRED0041_F02_B" mode="EXCERPT">
````abap
" 获取附加条款对应的附加协议
SELECT a~*
  FROM zret0006 AS a
  WHERE a~ztk_id = @ps_tk_add-ztk_id_plus
    AND a~zxyzt NE 'D'
  INTO CORRESPONDING FIELDS OF TABLE @lt_t06.
````
</augment_code_snippet>

### 4.2 附加协议的保存逻辑

#### 4.2.1 A类型附加协议的处理

<augment_code_snippet path="src/ZRED0041/ZRED0041_F02" mode="EXCERPT">
````abap
" 在保存时 类型为A的附加条款会取本次新生成的协议
IF ls_ta04-zatktp = 'A' AND ls_ta04-zrlid IS INITIAL.
  CLEAR ls_tc02.
  READ TABLE pt_tc02 INTO ls_tc02 WITH KEY zitems_key = ls_ta04-zitems_key_zxy_id.
  IF sy-subrc EQ 0.
    ls_ta04-zrlid = ls_tc02-zxy_id.
  ENDIF.
ENDIF.
````
</augment_code_snippet>

## 5. 业务流程与状态控制

### 5.1 附加条款的审批依赖

#### 5.1.1 前序条款审批检查

<augment_code_snippet path="src/ZRED0041/ZRED0041_F03" mode="EXCERPT">
````abap
" 检查附加条款的审批状态
SELECT zrlid
  INTO TABLE @DATA(lt_list)
  FROM zreta003
  INNER JOIN zreta002 ON zreta002~ztk_id = zreta003~zrlid
  WHERE zreta003~ztk_id = @p_ztk_id
    AND zreta002~zxyzt <> 'A'.
IF sy-subrc = 0.
  " 附加条款未审批，不能审批主条款
ENDIF.
````
</augment_code_snippet>

#### 5.1.2 引用条款状态检查

<augment_code_snippet path="src/ZRED0041/ZRED0041_F03" mode="EXCERPT">
````abap
" 检查引用该条款的其他条款状态
SELECT zreta002~ztk_id
  INTO TABLE @DATA(lt_list)
  FROM zreta003
  INNER JOIN zreta002 ON zreta002~ztk_id = zreta003~ztk_id
  WHERE zreta003~zrlid = @p_ztk_id
    AND zreta002~zxyzt = 'A'
    AND zreta002~zleib <> 'R'.
````
</augment_code_snippet>

### 5.2 附加协议的删除逻辑

#### 5.2.1 P类型附加协议删除

<augment_code_snippet path="src/ZRED0041/ZRED0041_F02_B" mode="EXCERPT">
````abap
" P类型附加条款删除时，同时删除对应的附加协议
SELECT a~zxy_id
  FROM @pt_ta03 AS i JOIN zret0006 AS a
                      ON i~zrlid = a~ztk_id
                     AND i~sel = 'X'
                     AND i~zatktp = 'P'
  INTO TABLE @DATA(lt_t06_tmp).

LOOP AT pt_ta04 INTO DATA(ls_ta04).
  READ TABLE lt_t06_tmp TRANSPORTING NO FIELDS WITH KEY zxy_id = ls_ta04-zrlid.
  IF sy-subrc EQ 0.
    DELETE pt_ta04.
  ENDIF.
ENDLOOP.
````
</augment_code_snippet>

#### 5.2.2 A类型附加协议删除

<augment_code_snippet path="src/ZRED0041/ZRED0041_F02_B" mode="EXCERPT">
````abap
" A类型附加条款删除时的处理
LOOP AT pt_ta03 INTO DATA(ls_ta03) WHERE sel = 'X' AND zatktp = 'A'.
  DELETE pt_ta04 WHERE zitems = ls_ta03-zitems AND zatktp = 'A'.
ENDLOOP.
````
</augment_code_snippet>

## 6. 计算逻辑中的附加协议处理

### 6.1 附加条款的计算顺序

在返利计算过程中，系统会按照以下顺序处理：

1. **附加条款计算** (ZRED0008程序)
2. **正常条款计算**

<augment_code_snippet path="src/ZRED0008/ZRED0008F01" mode="EXCERPT">
````abap
" 先处理附加条款
LOOP AT gt_ztk_id_ats INTO DATA(ls_ztk_id).
  SELECT zret0006~zxy_id
    FROM zret0006
    INNER JOIN zreta002 ON zret0006~ztk_id = zreta002~ztk_id
    WHERE zret0006~ztk_id = ls_ztk_id-ztk_id
      AND zret0006~zbegin <= lv_yestdat
    INTO TABLE lt_zxyid.
ENDLOOP.
````
</augment_code_snippet>

### 6.2 前序条款的递归查询

系统支持多层级的前序条款依赖关系：

<augment_code_snippet path="src/ZRED0008/ZRED0008F01" mode="EXCERPT">
````abap
" 条款的前序条款递归查询
DO 10 TIMES.
  IF lt_sel_id IS INITIAL.
    EXIT.
  ENDIF.
  
  SELECT zreta003~zrlid AS ztk_id
    FROM zreta003
    INNER JOIN zreta002 ON zreta003~zrlid = zreta002~ztk_id
    FOR ALL ENTRIES IN @lt_sel_id
    WHERE zreta003~ztk_id = @lt_sel_id-ztk_id
      AND zreta003~zatktp = 'P'
      AND zreta003~zatkpi = 'C'
    INTO TABLE @lt_tk_id.
ENDDO.
````
</augment_code_snippet>

## 7. 数据一致性控制

### 7.1 重复添加检查

#### 7.1.1 P类型重复检查

<augment_code_snippet path="src/ZRED0041/ZRED0041_F02_B" mode="EXCERPT">
````abap
IF ps_tk_add-zatktp = 'P'.
  READ TABLE pt_ta03 TRANSPORTING NO FIELDS WITH KEY zrlid = ps_tk_add-ztk_id_plus.
  IF sy-subrc EQ 0.
    PERFORM frm_add_msg USING '该附加条款已经添加,不能重复添加'.
  ENDIF.
````
</augment_code_snippet>

#### 7.1.2 A类型重复检查

<augment_code_snippet path="src/ZRED0041/ZRED0041_F02_B" mode="EXCERPT">
````abap
ELSEIF ps_tk_add-zatktp = 'A'.
  READ TABLE pt_ta03 TRANSPORTING NO FIELDS 
    WITH KEY zatktp = ps_tk_add-zatktp 
             zatkrl = ps_tk_add-zatkrl 
             zctgr = ps_tk_add-zctgr.
  IF sy-subrc EQ 0.
    PERFORM frm_add_msg USING '该附加条款已经添加,不能重复添加'.
  ENDIF.
````
</augment_code_snippet>

### 7.2 业务规则限制

#### 7.2.1 计算方法限制

<augment_code_snippet path="src/ZRED0041/ZRED0041_F02_B" mode="EXCERPT">
````abap
IF ps_tk_add-zatktp = 'P'.
  IF ps_tc05-zjsff = 'R'.
    PERFORM frm_add_msg USING '若阶梯计算方法为"R-补差"，则附加条款不允许增加"附加条款类型为P"的附加条款'.
  ENDIF.
ENDIF.
````
</augment_code_snippet>

#### 7.2.2 条款类型限制

<augment_code_snippet path="src/ZRED0041/ZRED0041_F02_B" mode="EXCERPT">
````abap
SELECT SINGLE zleib INTO @DATA(lv_zleib) FROM zreta002 WHERE ztk_id = @ps_tk_add-ztk_id_plus.
IF sy-subrc = 0 AND lv_zleib = 'R'.
  PERFORM frm_add_msg USING '该条款为条款申请,不能附加'.
ENDIF.
````
</augment_code_snippet>

## 8. 总结

### 8.1 关键关系映射

1. **主协议 → 附加协议**: 通过ZRETA003.ZRLID字段关联
2. **条款 → 附加条款**: 通过ZRETA003.ZTK_ID字段关联  
3. **附加条款 → 附加协议**: 通过ZRETA003.ZTK_ID_PLUS字段关联

### 8.2 业务逻辑要点

1. **P类型**: 用于前序条款依赖，支持多层级递归
2. **A类型**: 用于协议间关联，支持联合计算
3. **状态控制**: 附加条款必须先审批，主条款才能审批
4. **计算顺序**: 附加条款先计算，再计算正常条款
5. **数据一致性**: 严格的重复检查和业务规则限制

这种设计实现了复杂的返利业务场景，支持条款间的依赖关系和协议间的关联计算。
