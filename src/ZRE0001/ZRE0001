*&---------------------------------------------------------------------*
*& Subroutinenpool ZRE0001
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
PROGRAM zre0001.

*&---------------------------------------------------------------------*
*& Form FRM_CHECK_DATE
*&---------------------------------------------------------------------*
*& text 月结期间运行返利并支持非月结内条款审批和拒绝
*&---------------------------------------------------------------------*
*&      --> GS_TA02
*&      <-- LV_MTYPE_TK
*&      <-- LV_MSG_TK
*&---------------------------------------------------------------------*
FORM frm_get_turn_on_sign   CHANGING lv_sign TYPE c.
  lv_sign = 'X'.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_DATE
*&---------------------------------------------------------------------*
*& text 检查当前日期的上月是否在月结中
*&---------------------------------------------------------------------*
*&      --> GS_TA02
*&      <-- LV_MTYPE_TK
*&      <-- LV_MSG_TK
*&---------------------------------------------------------------------*
FORM frm_update_zmark_tran         USING pt_xy_no TYPE zre03_tt_xy_id
                                CHANGING pv_zmark TYPE c.

  DATA:lv_zxy_id    TYPE zret0065-zxy_id.
  DATA:lt_zxtid_sub TYPE zre03_tt_xy_id.
  DATA:lt_zretcm21  TYPE TABLE OF zretcm21.
  DATA:lv_datum     TYPE sy-datum.    "当前日期
  DATA:lv_zyear TYPE zretcm21-zyear,  "上月年份
       lv_zmont TYPE zretcm21-zmont.  "上月月份

  CLEAR:pv_zmark.

  lv_datum = sy-datum.

  IF lv_datum+4(2) = '01'.
    lv_zmont = '12'.
    lv_zyear = lv_datum+0(4) - 1.
  ELSE.
    lv_zmont = lv_datum+4(2) - 1.
    lv_zyear = lv_datum+0(4).
  ENDIF.

  PERFORM frm_get_zmark      USING lv_zyear
                                   lv_zmont
                                   lv_datum
                          CHANGING pv_zmark.


  SELECT SINGLE zmark INTO @DATA(lv_zmark_tab) FROM zretcm21 WHERE zyear = @lv_zyear AND zmont = @lv_zmont .

  IF sy-subrc <> 0.
    CLEAR lv_zmark_tab.
  ENDIF.

  IF pv_zmark <> lv_zmark_tab .

    CASE pv_zmark.
      WHEN '' .

        UPDATE zretcm21 SET zmark = '' WHERE  zyear = @lv_zyear  AND zmont = @lv_zmont .
        COMMIT WORK.

*数据清除**************************************************
*  SELECT SINGLE zxy_id INTO lv_zxy_id FROM zreta017 .
*  IF sy-subrc = 0.DELETE FROM zreta017. ENDIF.
*
*  SELECT SINGLE zxy_id INTO lv_zxy_id FROM zreta017_dp .
*  IF sy-subrc = 0.DELETE FROM zreta017_dp. ENDIF.
*
*  SELECT SINGLE zxy_id INTO lv_zxy_id FROM zreta026 .
*  IF sy-subrc = 0.DELETE FROM zreta026. ENDIF.
*
*  SELECT SINGLE zxy_id INTO lv_zxy_id FROM zreta057 .
*  IF sy-subrc = 0.DELETE FROM zreta057. ENDIF.
*
*  SELECT SINGLE zxy_id INTO lv_zxy_id FROM zreta079 .
*  IF sy-subrc = 0.DELETE FROM zreta079. ENDIF.
*
*  SELECT SINGLE zxy_id INTO lv_zxy_id FROM zreta099 .
*  IF sy-subrc = 0.DELETE FROM zreta099. ENDIF.
*
*  SELECT SINGLE zxy_id INTO lv_zxy_id FROM zreta118 .
*  IF sy-subrc = 0.DELETE FROM zreta118. ENDIF.
*
*  SELECT SINGLE zxy_id INTO lv_zxy_id FROM zreta119 .
*  IF sy-subrc = 0.DELETE FROM zreta119. ENDIF.
*
*  SELECT SINGLE zxy_id INTO lv_zxy_id FROM zreta120 .
*  IF sy-subrc = 0.DELETE FROM zreta120. ENDIF.

        CALL FUNCTION 'ZREFMAAA1'."数据清除

      WHEN 'X'.

        lt_zretcm21 = VALUE #( ( zyear = lv_zyear zmont = lv_zmont  zmark = 'X'
                                 erdat = sy-datum ertim = sy-uzeit  ernam = sy-uname
                                 aedat = sy-datum aetim = sy-uzeit  aenam = sy-uname
                              ) ).

        MODIFY  zretcm21  FROM TABLE lt_zretcm21.
        COMMIT WORK.

*  "拆组转移
        LOOP AT pt_xy_no INTO DATA(ls_zxyid).
          APPEND ls_zxyid  TO lt_zxtid_sub.

          IF lines( lt_zxtid_sub ) = 10000 .
            PERFORM frm_data_transfer USING lt_zxtid_sub.
            REFRESH lt_zxtid_sub.
          ENDIF.
        ENDLOOP.

        IF lines( lt_zxtid_sub ) > 0 .
          PERFORM frm_data_transfer USING lt_zxtid_sub.
        ENDIF.

*        SELECT * FROM zret0017    INNER JOIN  @pt_xy_no AS xy ON zret0017~zxy_id    = xy~zxy_id INTO TABLE @DATA(lt_zreta017)    .
*        SELECT * FROM zret0017_dp INNER JOIN  @pt_xy_no AS xy ON zret0017_dp~zxy_id = xy~zxy_id INTO TABLE @DATA(lt_zreta017_dp) .
*        SELECT * FROM zret0026    INNER JOIN  @pt_xy_no AS xy ON zret0026~zxy_id    = xy~zxy_id INTO TABLE @DATA(lt_zreta026)    .
*        SELECT * FROM zret0057    INNER JOIN  @pt_xy_no AS xy ON zret0057~zxy_id    = xy~zxy_id INTO TABLE @DATA(lt_zreta057)    .   .
*        SELECT * FROM zret0099    INNER JOIN  @pt_xy_no AS xy ON zret0099~zxy_id    = xy~zxy_id INTO TABLE @DATA(lt_zreta099)    .
*        SELECT * FROM zret0118    INNER JOIN  @pt_xy_no AS xy ON zret0118~zxy_id    = xy~zxy_id INTO TABLE @DATA(lt_zreta118)    .
*        SELECT * FROM zret0119    INNER JOIN  @pt_xy_no AS xy ON zret0119~zxy_id    = xy~zxy_id INTO TABLE @DATA(lt_zreta119)    .
*        SELECT * FROM zret0120    INNER JOIN  @pt_xy_no AS xy ON zret0120~zxy_id    = xy~zxy_id INTO TABLE @DATA(lt_zreta120)    .
*
*        CALL FUNCTION 'ZREFMAAA2' "数据转移
*          TABLES
*            lt_zreta017 = lt_zreta017
*            lt_za017_dp = lt_zreta017_dp
*            lt_zreta026 = lt_zreta026
*            lt_zreta057 = lt_zreta057
*            lt_zreta099 = lt_zreta099
*            lt_zreta118 = lt_zreta118
*            lt_zreta119 = lt_zreta119
*            lt_zreta120 = lt_zreta120.

      WHEN OTHERS.
    ENDCASE.

  ENDIF.


ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_DATE
*&---------------------------------------------------------------------*
*& text 检查当前日期的上月是否在月结中
*&---------------------------------------------------------------------*
*&      --> GS_TA02
*&      <-- LV_MTYPE_TK
*&      <-- LV_MSG_TK
*&---------------------------------------------------------------------*
FORM frm_add_tran         USING pt_xy_no TYPE zre03_tt_xy_id.

  DATA:lt_zxtid_sub TYPE zre03_tt_xy_id.



  LOOP AT pt_xy_no INTO DATA(ls_zxyid).

    "先删除后覆盖
    DELETE FROM zreta017    WHERE zxy_id = ls_zxyid-zxy_id.
    DELETE FROM zreta017_dp WHERE zxy_id = ls_zxyid-zxy_id.
    DELETE FROM zreta026    WHERE zxy_id = ls_zxyid-zxy_id.
    DELETE FROM zreta057    WHERE zxy_id = ls_zxyid-zxy_id.
    DELETE FROM zreta079    WHERE zxy_id = ls_zxyid-zxy_id.
    DELETE FROM zreta099    WHERE zxy_id = ls_zxyid-zxy_id.
    DELETE FROM zreta118    WHERE zxy_id = ls_zxyid-zxy_id.
    DELETE FROM zreta119    WHERE zxy_id = ls_zxyid-zxy_id.
    DELETE FROM zreta120    WHERE zxy_id = ls_zxyid-zxy_id.
    DELETE FROM zreta120_q  WHERE zxy_id = ls_zxyid-zxy_id.
    DELETE FROM zreta132    WHERE zxy_id = ls_zxyid-zxy_id.

    APPEND ls_zxyid  TO lt_zxtid_sub.

    IF lines( lt_zxtid_sub ) = 30000 .
      PERFORM frm_data_transfer USING lt_zxtid_sub.
      REFRESH lt_zxtid_sub.
    ENDIF.
  ENDLOOP.

  IF lines( lt_zxtid_sub ) > 0 .
    PERFORM frm_data_transfer USING lt_zxtid_sub.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_DATE
*&---------------------------------------------------------------------*
*& text 检查当前日期的上月是否在月结中
*&---------------------------------------------------------------------*
*&      --> GS_TA02
*&      <-- LV_MTYPE_TK
*&      <-- LV_MSG_TK
*&---------------------------------------------------------------------*
FORM frm_update_zmark_yjqj   CHANGING pv_zmark TYPE c.

  DATA:lv_zxy_id    TYPE zret0065-zxy_id.
  DATA:lt_zxtid_sub TYPE zre03_tt_xy_id.
  DATA:lt_zretcm20  TYPE TABLE OF zretcm20.
  DATA:lt_zretcm21  TYPE TABLE OF zretcm21.
  DATA:lv_datum     TYPE sy-datum.    "当前日期
  DATA:lv_zyear TYPE zretcm21-zyear,  "上月年份
       lv_zmont TYPE zretcm21-zmont.  "上月月份

  CLEAR:pv_zmark.

  lv_datum = sy-datum.

  IF lv_datum+4(2) = '01'.
    lv_zmont = '12'.
    lv_zyear = lv_datum+0(4) - 1.
  ELSE.
    lv_zmont = lv_datum+4(2) - 1.
    lv_zyear = lv_datum+0(4).
  ENDIF.

  PERFORM frm_get_zmark      USING lv_zyear
                                   lv_zmont
                                   lv_datum
                          CHANGING pv_zmark.

  IF  pv_zmark = ''.
    lt_zretcm20 = VALUE #(
                           ( zyear = lv_zyear zmont = lv_zmont  tabname = 'ZRET0065'    zmark = 'X'  erdat = sy-datum ertim = sy-uzeit  ernam = sy-uname  aedat = sy-datum aetim = sy-uzeit  aenam = sy-uname )
                           ( zyear = lv_zyear zmont = lv_zmont  tabname = 'ZRET0118_YJ' zmark = 'X'  erdat = sy-datum ertim = sy-uzeit  ernam = sy-uname  aedat = sy-datum aetim = sy-uzeit  aenam = sy-uname )
                           ( zyear = lv_zyear zmont = lv_zmont  tabname = 'ZRET0119_YJ' zmark = 'X'  erdat = sy-datum ertim = sy-uzeit  ernam = sy-uname  aedat = sy-datum aetim = sy-uzeit  aenam = sy-uname )
                           ( zyear = lv_zyear zmont = lv_zmont  tabname = 'ZRET0120_YJ' zmark = 'X'  erdat = sy-datum ertim = sy-uzeit  ernam = sy-uname  aedat = sy-datum aetim = sy-uzeit  aenam = sy-uname )
                           ( zyear = lv_zyear zmont = lv_zmont  tabname = 'ZRET0100'    zmark = 'X'  erdat = sy-datum ertim = sy-uzeit  ernam = sy-uname  aedat = sy-datum aetim = sy-uzeit  aenam = sy-uname )
                         ).

    MODIFY zretcm20 FROM TABLE lt_zretcm20.
    UPDATE zretcm21 SET zmark = '' WHERE  zyear = @lv_zyear  AND zmont = @lv_zmont .

    COMMIT WORK.

    CALL FUNCTION 'ZREFMAAA1'."数据清除
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_DATE
*&---------------------------------------------------------------------*
*& text 根据月结数据和锁定状态判断月结是否完成标识
*&---------------------------------------------------------------------*
*&      --> GS_TA02
*&      <-- LV_MTYPE_TK
*&      <-- LV_MSG_TK
*&---------------------------------------------------------------------*
FORM frm_get_zmark    USING  lv_zyear "当前日期上月年份
                             lv_zmont "当前日期上月月份
                             pv_datum "当前日期
                    CHANGING pv_zmark TYPE c.

  DATA:lv_moste TYPE zree_moste,
       lv_meg   TYPE bapi_msg.


  DATA:lv_0065 TYPE c,
       lv_0026 TYPE c,
       lv_lock TYPE c.

  CLEAR:pv_zmark,lv_0065,lv_lock.

*1.检查26表是否有当月计算返利数据
  SELECT SINGLE zxy_id  FROM zret0026 WHERE left( zbudat, 6 ) = @pv_datum+(6) INTO @DATA(lv_26_xy).
  IF sy-subrc = 0.
    lv_0026 = 'Y'.
  ELSE.
    lv_0026 = 'N'.
  ENDIF.
***
*2.检查zret0065是否有传入月月结数据

  SELECT SINGLE zmont INTO @DATA(lv_zmont1) FROM zret0065 WHERE zyear = @lv_zyear  AND zmont = @lv_zmont.

  IF sy-subrc = 0.
    lv_0065 = 'Y'.
  ELSE.
    lv_0065 = 'N'.
  ENDIF.

***
* 4.检查月结锁定标识是否锁定1

  CALL FUNCTION 'ZREFM0049'
    EXPORTING
      iv_bukrs = 'ALL'
      iv_datum = pv_datum
    IMPORTING
      ev_moste = lv_moste
      ev_meg   = lv_meg.

  IF lv_moste = 'E'.
    lv_lock = 'Y'.
  ELSE.
    lv_lock = 'N'.
  ENDIF.
**
* 3.检查月结锁定标识是否锁定2
  DATA rt_datum  TYPE RANGE OF sy-datum.
  SELECT SINGLE MAX( zloen ) INTO @DATA(lv_zloen) FROM zretc017 WHERE zlock = 'X'   .
  IF sy-subrc = 0 AND  lv_zloen IS NOT INITIAL  .
    rt_datum = VALUE #(  ( sign = 'I' option = 'BT'  low = |{ pv_datum+0(6) }01|    high = |{ pv_datum+0(6) }{ lv_zloen }| ) ).
    IF pv_datum IN rt_datum .
      lv_lock = 'Y'.
    ENDIF.
  ENDIF.


* 返利计算已经运行过 且 65上月有数 且 月结锁定打开
  IF lv_0026 = 'Y' AND lv_0065 = 'Y' AND lv_lock = 'N'  .
    pv_zmark = ''.
  ELSE.
    pv_zmark = 'X'.
  ENDIF.

** 返利计算已经运行过
*  IF lv_0026 = 'Y' .
*    pv_zmark = ''.
** 既有上月月结数据,也无月结锁定标识
*  ELSEIF lv_0065 = 'Y' AND lv_lock = 'N' .
*    pv_zmark = ''.
** 无上月月结数据业务或有月结锁定标识
*  ELSE.
*    pv_zmark = 'X'.
*  ENDIF.


ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_DATE
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_TA02
*&      <-- LV_MTYPE_TK
*&      <-- LV_MSG_TK
*&---------------------------------------------------------------------*
FORM frm_get_zlock  CHANGING lv_zlock TYPE c.

  DATA:lv_moste TYPE zree_moste,
       lv_meg   TYPE bapi_msg.
  DATA:lv_datum TYPE datum.
*  是否锁定
  lv_datum = sy-datum.

  CALL FUNCTION 'ZREFM0049'
    EXPORTING
      iv_bukrs = 'ALL'
      iv_datum = lv_datum
    IMPORTING
      ev_moste = lv_moste
      ev_meg   = lv_meg.

  IF lv_moste = 'E'.
    lv_zlock = 'Y'.
  ELSE.
    lv_zlock = 'N'.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_GET_LASDY
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- LV_LASDY
*&      <-- IF
*&      <-- FOUND
*&---------------------------------------------------------------------*
FORM frm_get_lasdy  CHANGING pv_sylsd.

  DATA:lv_byfsd TYPE sy-datum.

  lv_byfsd = sy-datum+0(6) && '01'.
  pv_sylsd = lv_byfsd - 1.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_DATA_TRANSFER
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LT_ZXTID_SUB
*&---------------------------------------------------------------------*
FORM frm_data_transfer  USING pt_zxtid TYPE zre03_tt_xy_id .

  CHECK pt_zxtid[] IS NOT INITIAL.

  SELECT * INTO TABLE @DATA(lt_zreta017) FROM zret0017   FOR ALL ENTRIES IN  @pt_zxtid WHERE zxy_id = @pt_zxtid-zxy_id.
  MODIFY zreta017   FROM TABLE lt_zreta017.
  REFRESH lt_zreta017.

  SELECT * INTO TABLE @DATA(lt_zreta017_dp) FROM zret0017_dp FOR ALL ENTRIES IN  @pt_zxtid WHERE zxy_id = @pt_zxtid-zxy_id.
  MODIFY zreta017_dp FROM  TABLE lt_zreta017_dp.
  REFRESH lt_zreta017_dp.

  SELECT * INTO TABLE @DATA(lt_zreta026)    FROM zret0026 FOR ALL ENTRIES IN  @pt_zxtid WHERE zxy_id = @pt_zxtid-zxy_id.
  MODIFY zreta026    FROM  TABLE lt_zreta026.
  REFRESH lt_zreta026.

  SELECT * INTO TABLE @DATA(lt_zreta057)    FROM zret0057 FOR ALL ENTRIES IN  @pt_zxtid WHERE zxy_id = @pt_zxtid-zxy_id.
  MODIFY zreta057    FROM  TABLE lt_zreta057.
  REFRESH lt_zreta057.

  SELECT * INTO TABLE @DATA(lt_zreta099)    FROM zret0099 FOR ALL ENTRIES IN  @pt_zxtid WHERE zxy_id = @pt_zxtid-zxy_id.
  MODIFY zreta099    FROM  TABLE lt_zreta099.
  REFRESH lt_zreta099.

  SELECT * INTO TABLE @DATA(lt_zreta118)    FROM zret0118 FOR ALL ENTRIES IN  @pt_zxtid WHERE zxy_id = @pt_zxtid-zxy_id.
  MODIFY zreta118    FROM  TABLE lt_zreta118.
  REFRESH lt_zreta118.

  SELECT * INTO TABLE @DATA(lt_zreta119)    FROM zret0119 FOR ALL ENTRIES IN  @pt_zxtid WHERE zxy_id = @pt_zxtid-zxy_id.
  MODIFY zreta119 FROM  TABLE lt_zreta119.
  REFRESH lt_zreta119[].

  SELECT * INTO TABLE @DATA(lt_zreta120)    FROM zret0120 FOR ALL ENTRIES IN  @pt_zxtid WHERE zxy_id = @pt_zxtid-zxy_id.
  MODIFY zreta120    FROM  TABLE lt_zreta120.
  REFRESH lt_zreta120[].

  SELECT * INTO TABLE @DATA(lt_zreta120_q)    FROM zret0120_q FOR ALL ENTRIES IN  @pt_zxtid WHERE zxy_id = @pt_zxtid-zxy_id.
  MODIFY zreta120_q    FROM  TABLE lt_zreta120_q.
  REFRESH lt_zreta120_q[].

  SELECT * INTO TABLE @DATA(lt_zreta132)    FROM zret0132 FOR ALL ENTRIES IN  @pt_zxtid WHERE zxy_id = @pt_zxtid-zxy_id.
  MODIFY zreta132    FROM  TABLE lt_zreta132.
  REFRESH lt_zreta132[].

  COMMIT WORK.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_DATA_TRANSFER
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LT_ZXTID_SUB
*&---------------------------------------------------------------------*
FORM frm_get_zretcm21_zmark   CHANGING pv_zmark
                                   .
  DATA:lv_zyear TYPE zretcm21-zyear,  "上月年份
       lv_zmont TYPE zretcm21-zmont.  "上月月份

  CLEAR:pv_zmark.

  IF sy-datum+4(2) = '01'.
    lv_zmont = '12'.
    lv_zyear = sy-datum+0(4) - 1.
  ELSE.
    lv_zmont = sy-datum+4(2) - 1.
    lv_zyear = sy-datum+0(4).
  ENDIF.

  SELECT SINGLE zmark
    INTO @DATA(lv_zmark_tab)
    FROM zretcm21
   WHERE zyear = @lv_zyear
     AND zmont = @lv_zmont.

  pv_zmark = lv_zmark_tab.

ENDFORM.
*&---------------------------------------------------------------------*
*&      --> LS_DATA_ZXY_ID
*&---------------------------------------------------------------------*
FORM frm_del_old_data_yjqj  USING    pv_zxy_id
                                     pv_taskid.

  DELETE FROM zreta118    WHERE zxy_id =  pv_zxy_id AND zjslx = '1'.
  DELETE FROM zreta119    WHERE zxy_id =  pv_zxy_id AND zjslx = '1'.
  DELETE FROM zreta120    WHERE zxy_id =  pv_zxy_id AND zjslx = '1'.
  COMMIT WORK.
ENDFORM.
*&---------------------------------------------------------------------*
*&      --> LS_ZXY_JSD_ZJSD_ID
*&---------------------------------------------------------------------*
FORM frm_dele_zjsd_id_old_yjqj  USING    pv_zjsd_id
                                         pv_taskid.

  DELETE FROM zreta118 WHERE zjsd_id =  pv_zjsd_id AND zjslx = '2'.
  DELETE FROM zreta119 WHERE zjsd_id =  pv_zjsd_id AND zjslx = '2'.
  DELETE FROM zreta120 WHERE zjsd_id =  pv_zjsd_id AND zjslx = '2'.

  COMMIT WORK.
ENDFORM.