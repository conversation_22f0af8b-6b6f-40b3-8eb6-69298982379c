*&---------------------------------------------------------------------*
*& 包含               ZRED0001_F03
*&---------------------------------------------------------------------*
*&---------------------------------------------------------------------*
*& Form FRM_SET_SCREEN_9915
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_set_screen_9915 .

  PERFORM frm_set_screen_all.

ENDFORM.

FORM frm_set_screen_9134 .

  CASE p_zfllx.
    WHEN 'RB05'.
      LOOP AT SCREEN.
        IF screen-group2 = '18' .
          screen-active = 0.
        ENDIF.
        MODIFY SCREEN.
      ENDLOOP.
    WHEN OTHERS.
  ENDCASE.

  LOOP AT SCREEN.
    CASE gv_zxybstyp.
      WHEN 'A'.
        IF screen-group2 = '19' .
          screen-input = 0.
        ENDIF.
    ENDCASE.
    MODIFY SCREEN.
  ENDLOOP.


  PERFORM frm_set_screen_all.

ENDFORM.
FORM frm_set_screen_9916 .
  PERFORM frm_set_screen_all.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_BT_DTL_JT_COMMAND
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> P_
*&      --> GS_T10
*&      --> GT_T15
*&      <-- GT_T11_HS
*&      <-- GT_T11_HS_SUB
*&---------------------------------------------------------------------*
FORM frm_bt_dtl_jt_command  USING   pv_dynnr TYPE sy-dynnr
                                     pv_tc    TYPE dynfnam
                                     pv_cursor_line TYPE i
                                     ps_t10   TYPE ty_t10
                            CHANGING
                                     pt_t15   TYPE tt_t15
                                     pt_t11   TYPE tt_t11
                                     pt_t11_all TYPE tt_t11
                                     pt_t11_sub TYPE tt_t11.


  DATA:
        lv_index_line TYPE i.
  DATA:
        ls_t11_data TYPE LINE OF tt_t11.

*  检查阶梯抬头 返利形式和计算方法 不能为空 否则子阶梯不允许维护
*  IF pv_tc NE 'TC_HSZJT' OR pv_tc NE 'TC_ZJT'.
*    IF ps_t10-zjsff NE 'T'.
  IF NOT ( pv_tc = 'TC_ZHSQJ' AND ps_t10-zjsff = 'T' ).
    IF ps_t10-zflxs IS INITIAL OR ps_t10-zjsff IS INITIAL.
      MESSAGE s888(sabapdocu) WITH '阶梯【返利形式】和【计算方法】不能为空' DISPLAY LIKE 'E'.
      RETURN.
    ENDIF.
  ENDIF.
*    ENDIF.
*  ENDIF.


  PERFORM frm_get_index_line USING      pv_tc
                                        pv_cursor_line
                             CHANGING   lv_index_line.

  IF pv_dynnr = '9914'.
    READ TABLE pt_t15 INTO DATA(ls_t15) INDEX lv_index_line.
    IF sy-subrc EQ 0.

*    从全部子阶梯数据中分离出当前行对应的子阶梯数据
      PERFORM frm_pro_data_hszjt CHANGING   ls_t15
                                            pt_t11_all
                                            pt_t11_sub.

      CALL SCREEN pv_dynnr.


*  阶梯数据处理 防止因未回车导致的错误
      PERFORM frm_set_data_zjt  USING    ps_t10
                                CHANGING pt_t11_sub.

*   更新子阶梯ID
      CLEAR ls_t11_data.
      ls_t11_data-zjt_id = ls_t15-zjt_id.
      MODIFY pt_t11_sub FROM ls_t11_data TRANSPORTING zjt_id WHERE zjt_id IS INITIAL.

*    将屏幕上子阶梯数据合并到阶梯表中 后面一起更新到数据库中
      APPEND LINES OF pt_t11_sub TO pt_t11_all.
      MODIFY pt_t15 FROM ls_t15 INDEX lv_index_line.
    ENDIF.
  ELSEIF pv_dynnr = '9915' OR pv_dynnr = '9916'.
    READ TABLE pt_t11 INTO DATA(ls_t11) INDEX lv_index_line.
    IF sy-subrc EQ 0.

*    从全部子阶梯数据中分离出当前行对应的子阶梯数据
      PERFORM frm_pro_data_jszjt CHANGING   ls_t11
                                            pt_t11_all
                                            pt_t11_sub.

      CALL SCREEN pv_dynnr.


*  阶梯数据处理 防止因未回车导致的错误
      PERFORM frm_set_data_zjt  USING    ps_t10
                                CHANGING pt_t11_sub.

*   更新子阶梯ID
      CLEAR ls_t11_data.
      ls_t11_data-zjt_id = ls_t11-zfljt_id.
      MODIFY pt_t11_sub FROM ls_t11_data TRANSPORTING zjt_id WHERE zjt_id IS INITIAL.

*    将屏幕上子阶梯数据合并到阶梯表中 后面一起更新到数据库中
      APPEND LINES OF pt_t11_sub TO pt_t11_all.
      MODIFY pt_t11 FROM ls_t11 INDEX lv_index_line.
    ENDIF.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_PROCESS_ZJT
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PS_T10
*&      <-- PT_T11_HS
*&---------------------------------------------------------------------*
FORM frm_process_zjt  USING    ps_t10 TYPE ty_t10
                      CHANGING pt_t10 TYPE tt_t10
                               pt_t11 TYPE tt_t11.

  DATA:
    ls_t10 TYPE LINE OF tt_t10,
    ls_t11 TYPE LINE OF tt_t11.

  DATA:
        lv_num TYPE i.

  CLEAR pt_t10.

*  核算期间子阶梯明细表
  CLEAR lv_num.
  SORT pt_t11 BY zjt_id.

*  将阶梯表的MANDT置为空，防止 AT NEW 语句BUG
  ls_t11-mandt = ''.
  MODIFY pt_t11 FROM ls_t11 TRANSPORTING mandt WHERE mandt NE ''.
  CLEAR ls_t11.

  LOOP AT pt_t11 INTO ls_t11.
    AT NEW zjt_id.
*      阶梯号码更改后，行项目重新编号
      CLEAR lv_num.
    ENDAT.
    lv_num                = lv_num + 1.
    ls_t11-zjt_itemid  = lv_num.
    MODIFY pt_t11 FROM ls_t11.

*   根据明细表组建抬头表 因为核算期间子阶梯没有抬头数据
*   取协议主阶梯中的数据
    CLEAR: ls_t10.
    ls_t10-zjt_id = ls_t11-zjt_id.
    ls_t10-zlhwd = ps_t10-zlhwd.
    ls_t10-zjths = ps_t10-zjths.
    ls_t10-zjtlx = ps_t10-zjtlx.
    ls_t10-zflxs = ps_t10-zflxs.
    ls_t10-zjsff = ps_t10-zjsff.
    ls_t10-zjgwd = ps_t10-zjgwd.
    ls_t10-zfljgwd = ps_t10-zfljgwd.
    ls_t10-zflhs = ps_t10-zflhs.
    APPEND  ls_t10 TO pt_t10 .
  ENDLOOP.
*  去重
  SORT pt_t10 BY zjt_id.
  DELETE ADJACENT DUPLICATES FROM pt_t10 COMPARING zjt_id.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_PROCESS_T10
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PS_T10
*&---------------------------------------------------------------------*
FORM frm_process_t10  USING    ps_t10 TYPE LINE OF tt_t10.
  IF ps_t10 IS NOT INITIAL.
    IF ps_t10-zjsff = 'T'.
      CLEAR ps_t10-zjtlx.
      CLEAR ps_t10-zflxs.
      CLEAR ps_t10-zfljgwd.
      CLEAR ps_t10-zflhs.
    ENDIF.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_ADD_DATA_ZJT
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PS_T10
*&      <-- PT_T10_NULL
*&---------------------------------------------------------------------*
FORM frm_add_data_zjt  USING    ps_t10  TYPE ty_t10
                                pt_t11 TYPE tt_t11
                       CHANGING pt_t10_null TYPE tt_t10.
  DATA:
        ls_t10_null TYPE LINE OF tt_t10.

  LOOP AT pt_t11 INTO DATA(ls_t11) WHERE zfljt_id IS INITIAL.
*    获取虚拟的阶梯号码
    PERFORM frm_get_zjt_id CHANGING ls_t11-zfljt_id.

    CLEAR ls_t10_null.
    ls_t10_null-zjt_id = ls_t11-zfljt_id.
    ls_t10_null-zlhwd = ps_t10-zlhwd.
    ls_t10_null-zjths = ps_t10-zjths.
    ls_t10_null-zjtlx = ps_t10-zjtlx.
    ls_t10_null-zflxs = ps_t10-zflxs.
    ls_t10_null-zjsff = ps_t10-zjsff.
    ls_t10_null-zjgwd = ps_t10-zjgwd.
    ls_t10_null-zfljgwd = ps_t10-zfljgwd.
    ls_t10_null-zflhs = ps_t10-zflhs.
    APPEND  ls_t10_null TO pt_t10_null .

    MODIFY pt_t11 FROM ls_t11 TRANSPORTING zfljt_id.
  ENDLOOP.

*  去重
  SORT pt_t10_null BY zjt_id.
  DELETE ADJACENT DUPLICATES FROM pt_t10_null COMPARING zjt_id.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_DOUBLE
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PT_T12
*&      --> PT_T13
*&      --> PT_T38
*&      --> PT_T21
*&      --> PT_BUKRS
*&      --> PT_DCWRK
*&      --> PT_WERKS
*&      --> PT_EKORG
*&      --> PT_BUKRS_N
*&      --> PT_DCWRK_N
*&      --> PT_BUKRS_N
*&      --> PT_EKORG_N
*&      <-- PV_FLG_ERR
*&      <-- LT_MSGLIST
*&---------------------------------------------------------------------*
FORM frm_check_double  USING
                                pt_t12        TYPE tt_t12
                                pt_t13        TYPE tt_t13
                                pt_t38        TYPE tt_t38
                                pt_t21        TYPE tt_t21
                                pt_bukrs      TYPE tt_bukrs
                                pt_dcwrk      TYPE tt_dcwrk
                                pt_werks      TYPE tt_werks
                                pt_ekorg      TYPE tt_ekorg
                                pt_bukrs_n    TYPE tt_bukrs
                                pt_dcwrk_n    TYPE tt_dcwrk
                                pt_werks_n    TYPE tt_werks
                                pt_ekorg_n    TYPE tt_ekorg
                       CHANGING pv_flg_err    TYPE char1
                                pt_msglist    TYPE scp1_general_errors.

  DATA:
        ls_msglist TYPE LINE OF scp1_general_errors.


  DATA(lt_t12) = pt_t12[].
  DATA(lt_t13) = pt_t13[].
  DATA(lt_t38) = pt_t38[].
  DATA(lt_t21) = pt_t21[].
  DATA(lt_bukrs) = pt_bukrs[].
  DATA(lt_dcwrk) = pt_dcwrk[].
  DATA(lt_werks) = pt_werks[].
  DATA(lt_ekorg) = pt_ekorg[].
  DATA(lt_bukrs_n) = pt_bukrs_n[].
  DATA(lt_dcwrk_n) = pt_dcwrk_n[].
  DATA(lt_werks_n) = pt_werks_n[].
  DATA(lt_ekorg_n) = pt_ekorg_n[].


  SORT lt_t12     BY lifnr. DELETE ADJACENT DUPLICATES FROM lt_t12     COMPARING lifnr  .
  SORT lt_t13     BY zghf . DELETE ADJACENT DUPLICATES FROM lt_t13     COMPARING zghf   .
  SORT lt_t38     BY zghf . DELETE ADJACENT DUPLICATES FROM lt_t38     COMPARING zghf   .
  SORT lt_t21     BY zcj  . DELETE ADJACENT DUPLICATES FROM lt_t21     COMPARING zcj    .
  SORT lt_bukrs   BY bukrs. DELETE ADJACENT DUPLICATES FROM lt_bukrs   COMPARING bukrs.
  SORT lt_dcwrk   BY dcwrk. DELETE ADJACENT DUPLICATES FROM lt_dcwrk   COMPARING dcwrk.
  SORT lt_werks   BY werks. DELETE ADJACENT DUPLICATES FROM lt_werks   COMPARING werks.
  SORT lt_ekorg   BY ekorg. DELETE ADJACENT DUPLICATES FROM lt_ekorg   COMPARING ekorg.
  SORT lt_bukrs_n BY bukrs. DELETE ADJACENT DUPLICATES FROM lt_bukrs_n COMPARING bukrs.
  SORT lt_dcwrk_n BY dcwrk. DELETE ADJACENT DUPLICATES FROM lt_dcwrk_n COMPARING dcwrk.
  SORT lt_werks_n BY werks. DELETE ADJACENT DUPLICATES FROM lt_werks_n COMPARING werks.
  SORT lt_ekorg_n BY ekorg. DELETE ADJACENT DUPLICATES FROM lt_ekorg_n COMPARING ekorg.

  IF lines( lt_t12     ) NE lines( pt_t12     ) . pv_flg_err = 'E'. ls_msglist-msgv1 = '渠道供应商不能重复'.  APPEND ls_msglist TO pt_msglist.  ENDIF.
  IF lines( lt_t13     ) NE lines( pt_t13     ) . pv_flg_err = 'E'. ls_msglist-msgv1 = '任务供货方不能重复'.  APPEND ls_msglist TO pt_msglist.  ENDIF.
  IF lines( lt_t38     ) NE lines( pt_t38     ) . pv_flg_err = 'E'. ls_msglist-msgv1 = '返利供货方不能重复'.  APPEND ls_msglist TO pt_msglist.  ENDIF.
  IF lines( lt_t21     ) NE lines( pt_t21     ) . pv_flg_err = 'E'. ls_msglist-msgv1 = '生产商不能重复'.  APPEND ls_msglist TO pt_msglist.  ENDIF.
  IF lines( lt_bukrs   ) NE lines( pt_bukrs   ) . pv_flg_err = 'E'. ls_msglist-msgv1 = '公司代码（任务）不能重复'.  APPEND ls_msglist TO pt_msglist.  ENDIF.
  IF lines( lt_dcwrk   ) NE lines( pt_dcwrk   ) . pv_flg_err = 'E'. ls_msglist-msgv1 = 'DC代码（任务）不能重复'.  APPEND ls_msglist TO pt_msglist.  ENDIF.
  IF lines( lt_werks   ) NE lines( pt_werks   ) . pv_flg_err = 'E'. ls_msglist-msgv1 = '门店清单（任务）不能重复'.  APPEND ls_msglist TO pt_msglist.  ENDIF.
  IF lines( lt_ekorg   ) NE lines( pt_ekorg   ) . pv_flg_err = 'E'. ls_msglist-msgv1 = '采购组织（任务）不能重复'.  APPEND ls_msglist TO pt_msglist.  ENDIF.
  IF lines( lt_bukrs_n ) NE lines( pt_bukrs_n ) . pv_flg_err = 'E'. ls_msglist-msgv1 = '公司代码（返利）不能重复'.  APPEND ls_msglist TO pt_msglist.  ENDIF.
  IF lines( lt_dcwrk_n ) NE lines( pt_dcwrk_n ) . pv_flg_err = 'E'. ls_msglist-msgv1 = 'DC代码（返利）不能重复'.  APPEND ls_msglist TO pt_msglist.  ENDIF.
  IF lines( lt_werks_n ) NE lines( pt_werks_n ) . pv_flg_err = 'E'. ls_msglist-msgv1 = '门店清单（返利）不能重复'.  APPEND ls_msglist TO pt_msglist.  ENDIF.
  IF lines( lt_ekorg_n ) NE lines( pt_ekorg_n ) . pv_flg_err = 'E'. ls_msglist-msgv1 = '采购组织（返利）不能重复'.  APPEND ls_msglist TO pt_msglist.  ENDIF.



ENDFORM.