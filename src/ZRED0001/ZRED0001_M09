*&---------------------------------------------------------------------*
*& Include ZRED0001_M09
*&---------------------------------------------------------------------*

*&SPWIZARD: FUNCTION CODES FOR TABSTRIP 'TG_ITEM'
CONSTANTS: BEGIN OF c_tg_item,
             tab1 LIKE sy-ucomm VALUE 'TG_ITEM_FC1',
             tab2 LIKE sy-ucomm VALUE 'TG_ITEM_FC2',
             tab3 LIKE sy-ucomm VALUE 'TG_ITEM_FC3',
             tab4 LIKE sy-ucomm VALUE 'TG_ITEM_FC4',
           END OF c_tg_item.
*&SPWIZARD: DATA FOR TABSTRIP 'TG_ITEM'
CONTROLS:  tg_item TYPE TABSTRIP.
DATA: BEGIN OF g_tg_item,
        subscreen   LIKE sy-dynnr,
        prog        LIKE sy-repid VALUE 'ZRED0001',
*        pressed_tab LIKE sy-ucomm VALUE c_tg_item-tab2,
        pressed_tab LIKE sy-ucomm ,  "默认加载页签
      END OF g_tg_item.
*DATA:      OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TS 'TG_ITEM'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: SETS ACTIVE TAB
MODULE tg_item_active_tab_set OUTPUT.

  IF g_tg_item-pressed_tab IS INITIAL.
    IF gv_zxybstyp = 'T'.
      g_tg_item-pressed_tab = c_tg_item-tab2.
    ELSE.
      g_tg_item-pressed_tab = c_tg_item-tab1.
    ENDIF.
  ENDIF.


  tg_item-activetab = g_tg_item-pressed_tab.
  CASE g_tg_item-pressed_tab.
    WHEN c_tg_item-tab1.
      g_tg_item-subscreen = '9131'.
    WHEN c_tg_item-tab2.
      g_tg_item-subscreen = '9132'.
    WHEN c_tg_item-tab3.
      g_tg_item-subscreen = '9133'.
    WHEN c_tg_item-tab4.
      g_tg_item-subscreen = '9134'.
    WHEN OTHERS.
*&SPWIZARD:      DO NOTHING
  ENDCASE.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TS 'TG_ITEM'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GETS ACTIVE TAB
MODULE tg_item_active_tab_get INPUT.
  ok_code = sy-ucomm.
  CASE ok_code.
    WHEN c_tg_item-tab1.
      g_tg_item-pressed_tab = c_tg_item-tab1.
    WHEN c_tg_item-tab2.
      g_tg_item-pressed_tab = c_tg_item-tab2.
    WHEN c_tg_item-tab3.
      g_tg_item-pressed_tab = c_tg_item-tab3.
    WHEN c_tg_item-tab4.
      g_tg_item-pressed_tab = c_tg_item-tab4.
    WHEN OTHERS.
*&SPWIZARD:      DO NOTHING
  ENDCASE.
ENDMODULE.
*&---------------------------------------------------------------------*
*& Module STATUS_9130 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_9130 OUTPUT.
  IF cb_split = ''.

    LOOP AT SCREEN.
      IF screen-group3 = '301' .
        screen-active = 0.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.

  ENDIF.

  LOOP AT SCREEN.
    IF screen-group3 = '310'.
      IF gs_t10-zjsff = 'T' .
        screen-active = 1.
      ELSE.
        screen-active = 0.
      ENDIF.
    ENDIF.

    IF gv_zxybstyp = 'T' AND screen-group3 = '330'..
      screen-active = 0.
    ENDIF.



    MODIFY SCREEN.
  ENDLOOP.

ENDMODULE.