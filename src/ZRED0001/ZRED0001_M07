*&---------------------------------------------------------------------*
*& Include ZRED0001_M07
*&---------------------------------------------------------------------*

*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_WERKS' ITSELF
CONTROLS: tc_werks TYPE TABLEVIEW USING SCREEN 9905.

*&SPWIZARD: LINES OF TABLECONTROL 'TC_WERKS'
DATA:     g_tc_werks_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_WERKS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: UPDATE LINES FOR EQUIVALENT SCROLLBAR
MODULE tc_werks_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_werks LINES tc_werks-lines.
ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_WERKS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL
MODULE tc_werks_get_lines OUTPUT.
  g_tc_werks_lines = sy-loopc.

  PERFORM frm_set_screen_all.
  IF  gt_werks[] IS INITIAL.
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
*  ELSE.
*    READ TABLE gt_werks TRANSPORTING NO FIELDS INDEX tc_werks-current_line.
*    IF sy-subrc NE 0.
*      LOOP AT SCREEN.
*        screen-input = 0.
*        MODIFY SCREEN.
*      ENDLOOP.
*    ENDIF.
  ENDIF.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_WERKS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE
MODULE tc_werks_modify INPUT.
  MODIFY gt_werks
    FROM gs_werks
    INDEX tc_werks-current_line.
ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_WERKS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE
MODULE tc_werks_mark INPUT.
  DATA: g_tc_werks_wa2 LIKE LINE OF gt_werks.
  IF tc_werks-line_sel_mode = 1
  AND gs_werks-sel = 'X'.
    LOOP AT gt_werks INTO g_tc_werks_wa2
      WHERE sel = 'X'.
      g_tc_werks_wa2-sel = ''.
      MODIFY gt_werks
        FROM g_tc_werks_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_werks
    FROM gs_werks
    INDEX tc_werks-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_WERKS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND
MODULE tc_werks_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_WERKS'
                              'GT_WERKS'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.


*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_WERKS' ITSELF
CONTROLS: tc_werks_n TYPE TABLEVIEW USING SCREEN 9919.

*&SPWIZARD: LINES OF TABLECONTROL 'TC_WERKS'
DATA:     g_tc_werks_n_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_WERKS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: UPDATE LINES FOR EQUIVALENT SCROLLBAR
MODULE tc_werks_n_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_werks_n LINES tc_werks_n-lines.
ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_WERKS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL
MODULE tc_werks_n_get_lines OUTPUT.
  g_tc_werks_n_lines = sy-loopc.

  PERFORM frm_set_screen_all.
  IF  gt_werks_n[] IS INITIAL.
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
*  ELSE.
*    READ TABLE gt_werks TRANSPORTING NO FIELDS INDEX tc_werks-current_line.
*    IF sy-subrc NE 0.
*      LOOP AT SCREEN.
*        screen-input = 0.
*        MODIFY SCREEN.
*      ENDLOOP.
*    ENDIF.
  ENDIF.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_WERKS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE
MODULE tc_werks_n_modify INPUT.
  MODIFY gt_werks_n
    FROM gs_werks_n
    INDEX tc_werks_n-current_line.
ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_WERKS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE
MODULE tc_werks_n_mark INPUT.
  DATA: g_tc_werks_n_wa2 LIKE LINE OF gt_werks_n.
  IF tc_werks_n-line_sel_mode = 1
  AND gs_werks_n-sel = 'X'.
    LOOP AT gt_werks_n INTO g_tc_werks_n_wa2
      WHERE sel = 'X'.
      g_tc_werks_n_wa2-sel = ''.
      MODIFY gt_werks_n
        FROM g_tc_werks_n_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_werks_n
    FROM gs_werks_n
    INDEX tc_werks_n-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_WERKS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND
MODULE tc_werks_n_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_WERKS_N'
                              'GT_WERKS_N'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.
*----------------------------------------------------------------------*
*   INCLUDE TABLECONTROL_FORMS                                         *
*----------------------------------------------------------------------*
***
****&---------------------------------------------------------------------*
****&      Form  USER_OK_TC                                               *
****&---------------------------------------------------------------------*
*** FORM USER_OK_TC USING    P_TC_NAME TYPE DYNFNAM
***                          P_TABLE_NAME
***                          P_MARK_NAME
***                 CHANGING P_OK      LIKE SY-UCOMM.
***
****&SPWIZARD: BEGIN OF LOCAL DATA----------------------------------------*
***   DATA: L_OK              TYPE SY-UCOMM,
***         L_OFFSET          TYPE I.
****&SPWIZARD: END OF LOCAL DATA------------------------------------------*
***
****&SPWIZARD: Table control specific operations                          *
****&SPWIZARD: evaluate TC name and operations                            *
***   SEARCH P_OK FOR P_TC_NAME.
***   IF SY-SUBRC <> 0.
***     EXIT.
***   ENDIF.
***   L_OFFSET = STRLEN( P_TC_NAME ) + 1.
***   L_OK = P_OK+L_OFFSET.
****&SPWIZARD: execute general and TC specific operations                 *
***   CASE L_OK.
***     WHEN 'INSR'.                      "insert row
***       PERFORM FCODE_INSERT_ROW USING    P_TC_NAME
***                                         P_TABLE_NAME.
***       CLEAR P_OK.
***
***     WHEN 'DELE'.                      "delete row
***       PERFORM FCODE_DELETE_ROW USING    P_TC_NAME
***                                         P_TABLE_NAME
***                                         P_MARK_NAME.
***       CLEAR P_OK.
***
***     WHEN 'P--' OR                     "top of list
***          'P-'  OR                     "previous page
***          'P+'  OR                     "next page
***          'P++'.                       "bottom of list
***       PERFORM COMPUTE_SCROLLING_IN_TC USING P_TC_NAME
***                                             L_OK.
***       CLEAR P_OK.
****     WHEN 'L--'.                       "total left
****       PERFORM FCODE_TOTAL_LEFT USING P_TC_NAME.
****
****     WHEN 'L-'.                        "column left
****       PERFORM FCODE_COLUMN_LEFT USING P_TC_NAME.
****
****     WHEN 'R+'.                        "column right
****       PERFORM FCODE_COLUMN_RIGHT USING P_TC_NAME.
****
****     WHEN 'R++'.                       "total right
****       PERFORM FCODE_TOTAL_RIGHT USING P_TC_NAME.
****
***     WHEN 'MARK'.                      "mark all filled lines
***       PERFORM FCODE_TC_MARK_LINES USING P_TC_NAME
***                                         P_TABLE_NAME
***                                         P_MARK_NAME   .
***       CLEAR P_OK.
***
***     WHEN 'DMRK'.                      "demark all filled lines
***       PERFORM FCODE_TC_DEMARK_LINES USING P_TC_NAME
***                                           P_TABLE_NAME
***                                           P_MARK_NAME .
***       CLEAR P_OK.
***
****     WHEN 'SASCEND'   OR
****          'SDESCEND'.                  "sort column
****       PERFORM FCODE_SORT_TC USING P_TC_NAME
****                                   l_ok.
***
***   ENDCASE.
***
*** ENDFORM.                              " USER_OK_TC
***
****&---------------------------------------------------------------------*
****&      Form  FCODE_INSERT_ROW                                         *
****&---------------------------------------------------------------------*
*** FORM fcode_insert_row
***               USING    P_TC_NAME           TYPE DYNFNAM
***                        P_TABLE_NAME             .
***
****&SPWIZARD: BEGIN OF LOCAL DATA----------------------------------------*
***   DATA L_LINES_NAME       LIKE FELD-NAME.
***   DATA L_SELLINE          LIKE SY-STEPL.
***   DATA L_LASTLINE         TYPE I.
***   DATA L_LINE             TYPE I.
***   DATA L_TABLE_NAME       LIKE FELD-NAME.
***   FIELD-SYMBOLS <TC>                 TYPE CXTAB_CONTROL.
***   FIELD-SYMBOLS <TABLE>              TYPE STANDARD TABLE.
***   FIELD-SYMBOLS <LINES>              TYPE I.
****&SPWIZARD: END OF LOCAL DATA------------------------------------------*
***
***   ASSIGN (P_TC_NAME) TO <TC>.
***
****&SPWIZARD: get the table, which belongs to the tc                     *
***   CONCATENATE P_TABLE_NAME '[]' INTO L_TABLE_NAME. "table body
***   ASSIGN (L_TABLE_NAME) TO <TABLE>.                "not headerline
***
****&SPWIZARD: get looplines of TableControl                              *
***   CONCATENATE 'G_' P_TC_NAME '_LINES' INTO L_LINES_NAME.
***   ASSIGN (L_LINES_NAME) TO <LINES>.
***
****&SPWIZARD: get current line                                           *
***   GET CURSOR LINE L_SELLINE.
***   IF SY-SUBRC <> 0.                   " append line to table
***     L_SELLINE = <TC>-LINES + 1.
****&SPWIZARD: set top line                                               *
***     IF L_SELLINE > <LINES>.
***       <TC>-TOP_LINE = L_SELLINE - <LINES> + 1 .
***     ELSE.
***       <TC>-TOP_LINE = 1.
***     ENDIF.
***   ELSE.                               " insert line into table
***     L_SELLINE = <TC>-TOP_LINE + L_SELLINE - 1.
***     L_LASTLINE = <TC>-TOP_LINE + <LINES> - 1.
***   ENDIF.
****&SPWIZARD: set new cursor line                                        *
***   L_LINE = L_SELLINE - <TC>-TOP_LINE + 1.
***
****&SPWIZARD: insert initial line                                        *
***   INSERT INITIAL LINE INTO <TABLE> INDEX L_SELLINE.
***   <TC>-LINES = <TC>-LINES + 1.
****&SPWIZARD: set cursor                                                 *
***   SET CURSOR LINE L_LINE.
***
*** ENDFORM.                              " FCODE_INSERT_ROW
***
****&---------------------------------------------------------------------*
****&      Form  FCODE_DELETE_ROW                                         *
****&---------------------------------------------------------------------*
*** FORM fcode_delete_row
***               USING    P_TC_NAME           TYPE DYNFNAM
***                        P_TABLE_NAME
***                        P_MARK_NAME   .
***
****&SPWIZARD: BEGIN OF LOCAL DATA----------------------------------------*
***   DATA L_TABLE_NAME       LIKE FELD-NAME.
***
***   FIELD-SYMBOLS <TC>         TYPE cxtab_control.
***   FIELD-SYMBOLS <TABLE>      TYPE STANDARD TABLE.
***   FIELD-SYMBOLS <WA>.
***   FIELD-SYMBOLS <MARK_FIELD>.
****&SPWIZARD: END OF LOCAL DATA------------------------------------------*
***
***   ASSIGN (P_TC_NAME) TO <TC>.
***
****&SPWIZARD: get the table, which belongs to the tc                     *
***   CONCATENATE P_TABLE_NAME '[]' INTO L_TABLE_NAME. "table body
***   ASSIGN (L_TABLE_NAME) TO <TABLE>.                "not headerline
***
****&SPWIZARD: delete marked lines                                        *
***   DESCRIBE TABLE <TABLE> LINES <TC>-LINES.
***
***   LOOP AT <TABLE> ASSIGNING <WA>.
***
****&SPWIZARD: access to the component 'FLAG' of the table header         *
***     ASSIGN COMPONENT P_MARK_NAME OF STRUCTURE <WA> TO <MARK_FIELD>.
***
***     IF <MARK_FIELD> = 'X'.
***       DELETE <TABLE> INDEX SYST-TABIX.
***       IF SY-SUBRC = 0.
***         <TC>-LINES = <TC>-LINES - 1.
***       ENDIF.
***     ENDIF.
***   ENDLOOP.
***
*** ENDFORM.                              " FCODE_DELETE_ROW
***
****&---------------------------------------------------------------------*
****&      Form  COMPUTE_SCROLLING_IN_TC
****&---------------------------------------------------------------------*
****       text
****----------------------------------------------------------------------*
****      -->P_TC_NAME  name of tablecontrol
****      -->P_OK       ok code
****----------------------------------------------------------------------*
*** FORM COMPUTE_SCROLLING_IN_TC USING    P_TC_NAME
***                                       P_OK.
****&SPWIZARD: BEGIN OF LOCAL DATA----------------------------------------*
***   DATA L_TC_NEW_TOP_LINE     TYPE I.
***   DATA L_TC_NAME             LIKE FELD-NAME.
***   DATA L_TC_LINES_NAME       LIKE FELD-NAME.
***   DATA L_TC_FIELD_NAME       LIKE FELD-NAME.
***
***   FIELD-SYMBOLS <TC>         TYPE cxtab_control.
***   FIELD-SYMBOLS <LINES>      TYPE I.
****&SPWIZARD: END OF LOCAL DATA------------------------------------------*
***
***   ASSIGN (P_TC_NAME) TO <TC>.
****&SPWIZARD: get looplines of TableControl                              *
***   CONCATENATE 'G_' P_TC_NAME '_LINES' INTO L_TC_LINES_NAME.
***   ASSIGN (L_TC_LINES_NAME) TO <LINES>.
***
***
****&SPWIZARD: is no line filled?                                         *
***   IF <TC>-LINES = 0.
****&SPWIZARD: yes, ...                                                   *
***     L_TC_NEW_TOP_LINE = 1.
***   ELSE.
****&SPWIZARD: no, ...                                                    *
***     CALL FUNCTION 'SCROLLING_IN_TABLE'
***          EXPORTING
***               ENTRY_ACT             = <TC>-TOP_LINE
***               ENTRY_FROM            = 1
***               ENTRY_TO              = <TC>-LINES
***               LAST_PAGE_FULL        = 'X'
***               LOOPS                 = <LINES>
***               OK_CODE               = P_OK
***               OVERLAPPING           = 'X'
***          IMPORTING
***               ENTRY_NEW             = L_TC_NEW_TOP_LINE
***          EXCEPTIONS
****              NO_ENTRY_OR_PAGE_ACT  = 01
****              NO_ENTRY_TO           = 02
****              NO_OK_CODE_OR_PAGE_GO = 03
***               OTHERS                = 0.
***   ENDIF.
***
****&SPWIZARD: get actual tc and column                                   *
***   GET CURSOR FIELD L_TC_FIELD_NAME
***              AREA  L_TC_NAME.
***
***   IF SYST-SUBRC = 0.
***     IF L_TC_NAME = P_TC_NAME.
****&SPWIZARD: et actual column                                           *
***       SET CURSOR FIELD L_TC_FIELD_NAME LINE 1.
***     ENDIF.
***   ENDIF.
***
****&SPWIZARD: set the new top line                                       *
***   <TC>-TOP_LINE = L_TC_NEW_TOP_LINE.
***
***
*** ENDFORM.                              " COMPUTE_SCROLLING_IN_TC
***
****&---------------------------------------------------------------------*
****&      Form  FCODE_TC_MARK_LINES
****&---------------------------------------------------------------------*
****       marks all TableControl lines
****----------------------------------------------------------------------*
****      -->P_TC_NAME  name of tablecontrol
****----------------------------------------------------------------------*
***FORM FCODE_TC_MARK_LINES USING P_TC_NAME
***                               P_TABLE_NAME
***                               P_MARK_NAME.
****&SPWIZARD: EGIN OF LOCAL DATA-----------------------------------------*
***  DATA L_TABLE_NAME       LIKE FELD-NAME.
***
***  FIELD-SYMBOLS <TC>         TYPE cxtab_control.
***  FIELD-SYMBOLS <TABLE>      TYPE STANDARD TABLE.
***  FIELD-SYMBOLS <WA>.
***  FIELD-SYMBOLS <MARK_FIELD>.
****&SPWIZARD: END OF LOCAL DATA------------------------------------------*
***
***  ASSIGN (P_TC_NAME) TO <TC>.
***
****&SPWIZARD: get the table, which belongs to the tc                     *
***   CONCATENATE P_TABLE_NAME '[]' INTO L_TABLE_NAME. "table body
***   ASSIGN (L_TABLE_NAME) TO <TABLE>.                "not headerline
***
****&SPWIZARD: mark all filled lines                                      *
***  LOOP AT <TABLE> ASSIGNING <WA>.
***
****&SPWIZARD: access to the component 'FLAG' of the table header         *
***     ASSIGN COMPONENT P_MARK_NAME OF STRUCTURE <WA> TO <MARK_FIELD>.
***
***     <MARK_FIELD> = 'X'.
***  ENDLOOP.
***ENDFORM.                                          "fcode_tc_mark_lines
***
****&---------------------------------------------------------------------*
****&      Form  FCODE_TC_DEMARK_LINES
****&---------------------------------------------------------------------*
****       demarks all TableControl lines
****----------------------------------------------------------------------*
****      -->P_TC_NAME  name of tablecontrol
****----------------------------------------------------------------------*
***FORM FCODE_TC_DEMARK_LINES USING P_TC_NAME
***                                 P_TABLE_NAME
***                                 P_MARK_NAME .
****&SPWIZARD: BEGIN OF LOCAL DATA----------------------------------------*
***  DATA L_TABLE_NAME       LIKE FELD-NAME.
***
***  FIELD-SYMBOLS <TC>         TYPE cxtab_control.
***  FIELD-SYMBOLS <TABLE>      TYPE STANDARD TABLE.
***  FIELD-SYMBOLS <WA>.
***  FIELD-SYMBOLS <MARK_FIELD>.
****&SPWIZARD: END OF LOCAL DATA------------------------------------------*
***
***  ASSIGN (P_TC_NAME) TO <TC>.
***
****&SPWIZARD: get the table, which belongs to the tc                     *
***   CONCATENATE P_TABLE_NAME '[]' INTO L_TABLE_NAME. "table body
***   ASSIGN (L_TABLE_NAME) TO <TABLE>.                "not headerline
***
****&SPWIZARD: demark all filled lines                                    *
***  LOOP AT <TABLE> ASSIGNING <WA>.
***
****&SPWIZARD: access to the component 'FLAG' of the table header         *
***     ASSIGN COMPONENT P_MARK_NAME OF STRUCTURE <WA> TO <MARK_FIELD>.
***
***     <MARK_FIELD> = SPACE.
***  ENDLOOP.
***ENDFORM.                                          "fcode_tc_mark_lines
*&---------------------------------------------------------------------*
*& Module MDL_SET_TC_DATA_9905 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE mdl_set_tc_data_9905 OUTPUT.
  PERFORM frm_set_data_9005 CHANGING gs_werks.
ENDMODULE.
MODULE mdl_set_tc_data_9919 OUTPUT.
  PERFORM frm_set_data_9005 CHANGING gs_werks_n.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_CHECK_WERKS  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_check_werks INPUT.
  PERFORM frm_check_werks USING gs_werks
                                gt_bukrs
                                gt_dcwrk.
ENDMODULE.
MODULE mdl_check_werks_n INPUT.
  PERFORM frm_check_werks USING gs_werks_n
                                gt_bukrs_n
                                gt_dcwrk_n.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_CHECK_WERKS_ZZZPC  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_check_werks_zzzpc INPUT.

  IF ( gs_werks-werks = 'ALL' OR gs_werks-werks IS INITIAL ) AND
      gs_werks-exclude = 'X'.
    MESSAGE e888(sabapdocu) WITH '门店编码为ALL或空时不允许进行排除！'.
  ENDIF.

ENDMODULE.
MODULE mdl_check_werks_N_zzzpc INPUT.

  IF ( gs_werks_n-werks = 'ALL' OR gs_werks_n-werks IS INITIAL ) AND
      gs_werks_n-exclude = 'X'.
    MESSAGE e888(sabapdocu) WITH '门店编码为ALL或空时不允许进行排除！'.
  ENDIF.

ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_SET_DATA_WERKS  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_set_data_werks INPUT.
*  若存在非ALL的数据，则自动删除ALL的数据
  LOOP AT gt_werks TRANSPORTING NO FIELDS WHERE werks IS NOT INITIAL AND werks NE 'ALL'.
    EXIT .
  ENDLOOP.
  IF sy-subrc EQ 0.
    DELETE gt_werks WHERE werks = 'ALL'.
  ENDIF.
ENDMODULE.
MODULE mdl_set_data_werks_n INPUT.
*  若存在非ALL的数据，则自动删除ALL的数据
  LOOP AT gt_werks_n TRANSPORTING NO FIELDS WHERE werks IS NOT INITIAL AND werks NE 'ALL'.
    EXIT .
  ENDLOOP.
  IF sy-subrc EQ 0.
    DELETE gt_werks_n WHERE werks = 'ALL'.
  ENDIF.
ENDMODULE.
*&---------------------------------------------------------------------*
*& Module STATUS_9905 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_9905 OUTPUT.
  PERFORM frm_set_screen_9905.
ENDMODULE.
MODULE status_9919 OUTPUT.
  PERFORM frm_set_screen_9919.
ENDMODULE.