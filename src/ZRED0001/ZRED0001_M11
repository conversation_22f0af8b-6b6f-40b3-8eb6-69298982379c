*&---------------------------------------------------------------------*
*& Include ZRED0001_M11
*&---------------------------------------------------------------------*

*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_ITEM_D' ITSELF
CONTROLS: tc_item_d TYPE TABLEVIEW USING SCREEN 9132.

*&SPWIZARD: LINES OF TABLECONTROL 'TC_ITEM_D'
DATA:     g_tc_item_d_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_ITEM_D'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: UPDATE LINES FOR EQUIVALENT SCROLLBAR
MODULE tc_item_d_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_t07 LINES tc_item_d-lines.
ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_ITEM_D'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL
MODULE tc_item_d_get_lines OUTPUT.
  g_tc_item_d_lines = sy-loopc.

  PERFORM frm_set_screen_all.


*  READ TABLE gt_t07 TRANSPORTING NO FIELDS INDEX tc_item_d-current_line.
*  IF sy-subrc NE 0.
*    LOOP AT SCREEN.
*      screen-input = 0.
*      MODIFY SCREEN.
*    ENDLOOP.
*  ENDIF.

  LOOP AT SCREEN.
    IF screen-group1 = '11' OR
       screen-group1 = '12' OR
       screen-group1 = '10' .
      screen-input = 0.
      MODIFY SCREEN.
    ENDIF.
  ENDLOOP.

  IF NOT (
    rb_dis = 'X' OR
    rb_rles = 'X' OR
    gv_flg_comm = 'COMT').

    IF gs_t07-zspwd = 'C'.
      LOOP AT SCREEN.
        IF screen-group1 = '12'.
          screen-input = 1.
          MODIFY SCREEN.
        ENDIF.
      ENDLOOP.
      CLEAR: gs_t07-matnr,
             gs_t07-maktx,
             gs_t07-meins,
             gs_t07-zacpr,
             gs_t07-zpeinh.
    ELSEIF gs_t07-zspwd = 'S'.
      LOOP AT SCREEN.
        IF screen-group1 = '11'.
          screen-input = 1.
          MODIFY SCREEN.
        ENDIF.
      ENDLOOP.
      CLEAR: gs_t07-zspz_id,
             gs_t07-zspzid_txt.
    ENDIF.

    LOOP AT SCREEN.
      IF screen-group1 = '10'.
        screen-input = 1.
        MODIFY SCREEN.
      ENDIF.
    ENDLOOP.

  ENDIF.

  IF gs_t07-zspwd = 'C'.
    LOOP AT SCREEN.
      IF screen-group1 = '13'.
        screen-input = 1.
        MODIFY SCREEN.
      ENDIF.
    ENDLOOP.
  ELSE.
    LOOP AT SCREEN.
      IF screen-group1 = '13'.
        screen-input = 0.
        MODIFY SCREEN.
      ENDIF.
    ENDLOOP.
  ENDIF.


  IF  gt_t07[] IS INITIAL .
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.

ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_ITEM_D'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE
MODULE tc_item_d_modify INPUT.
  MODIFY gt_t07
    FROM gs_t07
    INDEX tc_item_d-current_line.
ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_ITEM_D'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE
MODULE tc_item_d_mark INPUT.
  DATA: g_tc_item_d_wa2 LIKE LINE OF gt_t07.
  IF tc_item_d-line_sel_mode = 1
  AND gs_t07-sel = 'X'.
    LOOP AT gt_t07 INTO g_tc_item_d_wa2
      WHERE sel = 'X'.
      g_tc_item_d_wa2-sel = ''.
      MODIFY gt_t07
        FROM g_tc_item_d_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_t07
    FROM gs_t07
    INDEX tc_item_d-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_ITEM_D'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND
MODULE tc_item_d_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_ITEM_D'
                              'GT_T07'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.


*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_ITEM_D' ITSELF
CONTROLS: tc_item_d_n TYPE TABLEVIEW USING SCREEN 9133.

*&SPWIZARD: LINES OF TABLECONTROL 'TC_ITEM_D'
DATA:     g_tc_item_d_n_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_ITEM_D'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: UPDATE LINES FOR EQUIVALENT SCROLLBAR
MODULE tc_item_d_n_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_t39 LINES tc_item_d_n-lines.
ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_ITEM_D'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL
MODULE tc_item_d_n_get_lines OUTPUT.
  g_tc_item_d_n_lines = sy-loopc.

  PERFORM frm_set_screen_all.


*  READ TABLE gt_t07 TRANSPORTING NO FIELDS INDEX tc_item_d-current_line.
*  IF sy-subrc NE 0.
*    LOOP AT SCREEN.
*      screen-input = 0.
*      MODIFY SCREEN.
*    ENDLOOP.
*  ENDIF.

  LOOP AT SCREEN.
    IF screen-group1 = '11' OR
       screen-group1 = '12' OR
       screen-group1 = '10' .
      screen-input = 0.
      MODIFY SCREEN.
    ENDIF.
  ENDLOOP.

  IF NOT (
    rb_dis = 'X' OR
    rb_rles = 'X' OR
    gv_flg_comm = 'COMT').

    IF gs_t39-zspwd = 'C'.
      LOOP AT SCREEN.
        IF screen-group1 = '12'.
          screen-input = 1.
          MODIFY SCREEN.
        ENDIF.
      ENDLOOP.
      CLEAR: gs_t39-matnr,
             gs_t39-maktx,
             gs_t39-meins,
             gs_t39-zacpr,
             gs_t39-zpeinh.
    ELSEIF gs_t39-zspwd = 'S'.
      LOOP AT SCREEN.
        IF screen-group1 = '11'.
          screen-input = 1.
          MODIFY SCREEN.
        ENDIF.
      ENDLOOP.
      CLEAR: gs_t39-zspz_id,
             gs_t39-zspzid_txt.
    ENDIF.

    LOOP AT SCREEN.
      IF screen-group1 = '10'.
        screen-input = 1.
        MODIFY SCREEN.
      ENDIF.
    ENDLOOP.

  ENDIF.

  IF gs_t39-zspwd = 'C'.
    LOOP AT SCREEN.
      IF screen-group1 = '13'.
        screen-input = 1.
        MODIFY SCREEN.
      ENDIF.
    ENDLOOP.
  ELSE.
    LOOP AT SCREEN.
      IF screen-group1 = '13'.
        screen-input = 0.
        MODIFY SCREEN.
      ENDIF.
    ENDLOOP.
  ENDIF.


  IF  gt_t39[] IS INITIAL .
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.

ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_ITEM_D'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE
MODULE tc_item_d_n_modify INPUT.
  MODIFY gt_t39
    FROM gs_t39
    INDEX tc_item_d_n-current_line.
ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_ITEM_D'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE
MODULE tc_item_d_n_mark INPUT.
  DATA: g_tc_item_d_n_wa2 LIKE LINE OF gt_t39.
  IF tc_item_d_n-line_sel_mode = 1
  AND gs_t39-sel = 'X'.
    LOOP AT gt_t39 INTO g_tc_item_d_n_wa2
      WHERE sel = 'X'.
      g_tc_item_d_n_wa2-sel = ''.
      MODIFY gt_t39
        FROM g_tc_item_d_n_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_t39
    FROM gs_t39
    INDEX tc_item_d_n-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_ITEM_D'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND
MODULE tc_item_d_n_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_ITEM_D_N'
                              'GT_T39'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.


*&---------------------------------------------------------------------*
*&      Module  USER_COMMAND_9132  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE user_command_9132 INPUT.
  CLEAR:
        lv_code.
  lv_code = ok_code.
  CLEAR ok_code.

  CASE lv_code.
    WHEN 'BACK' OR 'EXIT' OR 'CANCEL'.
      LEAVE TO SCREEN 0.
    WHEN 'BT_DTL'.
      CALL SCREEN 9910.
    WHEN OTHERS.
  ENDCASE.
ENDMODULE.
*&---------------------------------------------------------------------*
*& Module MDL_SET_TC_DATA_9132 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE mdl_set_tc_data_9132 OUTPUT.
  PERFORM frm_set_data_9132 USING gt_t09
                            CHANGING gs_t07.
ENDMODULE.
MODULE mdl_set_tc_data_9133 OUTPUT.
  PERFORM frm_set_data_9132 USING gt_t09
                            CHANGING gs_t39.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_CHECK_MATNR  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_check_matnr INPUT.
  IF gs_t07-zspwd = 'S' .
    PERFORM frm_check_matnr USING gs_t07-matnr.
  ENDIF.
ENDMODULE.
MODULE mdl_check_matnr_n INPUT.
  IF gs_t07-zspwd = 'S' .
    PERFORM frm_check_matnr USING gs_t39-matnr.
  ENDIF.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_CHECK_ZSPZ_ID  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_check_zspz_id INPUT.
  PERFORM frm_check_zspz_id USING gs_t07-zspz_id
                                  gt_t09.

ENDMODULE.
MODULE mdl_check_zspz_id_n INPUT.
  PERFORM frm_check_zspz_id USING gs_t39-zspz_id
                                  gt_t09.

ENDMODULE.
*&---------------------------------------------------------------------*
*& Module STATUS_9132 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_9132 OUTPUT.
  PERFORM frm_set_screen_9132.
ENDMODULE.
MODULE status_9133 OUTPUT.
  PERFORM frm_set_screen_9133.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_CHECK_ZSPWD  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_check_zspwd INPUT.
  IF gs_t07-zspwd = 'C'.
    CLEAR gs_t07-zspz_id.
    CLEAR gs_t07-zspzid_txt.
  ENDIF.
ENDMODULE.
MODULE mdl_check_zspwd_n INPUT.
  IF gs_t39-zspwd = 'C'.
    CLEAR gs_t39-zspz_id.
    CLEAR gs_t39-zspzid_txt.
  ENDIF.
ENDMODULE.