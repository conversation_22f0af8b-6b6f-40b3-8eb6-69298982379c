*&---------------------------------------------------------------------*
*& Include ZRED0001_M17
*&---------------------------------------------------------------------*

*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_GHF' ITSELF
CONTROLS: tc_ghf TYPE TABLEVIEW USING SCREEN 9912.

*&SPWIZARD: LINES OF TABLECONTROL 'TC_GHF'
DATA:     g_tc_ghf_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: UPDATE LINES FOR EQUIVALENT SCROLLBAR
MODULE tc_ghf_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_t13 LINES tc_ghf-lines.

*  创建时供货方取值来源于渠道供应商
*  IF rb_add = 'X' .
*    DATA(lt_t13) = gt_t13[].
*    DELETE lt_t13 WHERE zghf IS INITIAL.
*    IF lt_t13[] IS INITIAL.
*      CLEAR gt_t13[].
*      APPEND LINES OF gt_t12 TO gt_t13.
*    ENDIF.
*  ENDIF.


ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL
MODULE tc_ghf_get_lines OUTPUT.
  g_tc_ghf_lines = sy-loopc.

  PERFORM frm_set_screen_all.
  IF gt_t13[] IS INITIAL.
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
*  ELSE.
*    READ TABLE gt_t13 TRANSPORTING NO FIELDS INDEX tc_ghf-current_line.
*    IF sy-subrc NE 0.
*      LOOP AT SCREEN.
*        screen-input = 0.
*        MODIFY SCREEN.
*      ENDLOOP.
*    ENDIF.
  ENDIF.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE
MODULE tc_ghf_modify INPUT.
  MODIFY gt_t13
    FROM gs_t13
    INDEX tc_ghf-current_line.
ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE
MODULE tc_ghf_mark INPUT.
  DATA: g_tc_ghf_wa2 LIKE LINE OF gt_t13.
  IF tc_ghf-line_sel_mode = 1
  AND gs_t13-sel = 'X'.
    LOOP AT gt_t13 INTO g_tc_ghf_wa2
      WHERE sel = 'X'.
      g_tc_ghf_wa2-sel = ''.
      MODIFY gt_t13
        FROM g_tc_ghf_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_t13
    FROM gs_t13
    INDEX tc_ghf-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND
MODULE tc_ghf_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_GHF'
                              'GT_T13'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.


*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_GHF' ITSELF
CONTROLS: tc_ghf_n TYPE TABLEVIEW USING SCREEN 9921.
CONTROLS: tc_zff TYPE TABLEVIEW USING SCREEN 9922.


*&SPWIZARD: LINES OF TABLECONTROL 'TC_GHF'
DATA:     g_tc_ghf_n_lines  LIKE sy-loopc.
DATA:     g_tc_zff_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: UPDATE LINES FOR EQUIVALENT SCROLLBAR
MODULE tc_ghf_n_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_t38 LINES tc_ghf_n-lines.

*  创建时供货方取值来源于渠道供应商
*  IF rb_add = 'X' .
*    DATA(lt_t13) = gt_t13[].
*    DELETE lt_t13 WHERE zghf IS INITIAL.
*    IF lt_t13[] IS INITIAL.
*      CLEAR gt_t13[].
*      APPEND LINES OF gt_t12 TO gt_t13.
*    ENDIF.
*  ENDIF.


ENDMODULE.
MODULE tc_zff_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_t44 LINES tc_zff-lines.


ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL
MODULE tc_ghf_n_get_lines OUTPUT.
  g_tc_ghf_n_lines = sy-loopc.

  PERFORM frm_set_screen_all.
  IF gt_t38[] IS INITIAL.
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
*  ELSE.
*    READ TABLE gt_t13 TRANSPORTING NO FIELDS INDEX tc_ghf-current_line.
*    IF sy-subrc NE 0.
*      LOOP AT SCREEN.
*        screen-input = 0.
*        MODIFY SCREEN.
*      ENDLOOP.
*    ENDIF.
  ENDIF.
ENDMODULE.
MODULE tc_zff_get_lines OUTPUT.
  g_tc_zff_lines = sy-loopc.

  PERFORM frm_set_screen_all.
  IF gt_t44[] IS INITIAL.
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE
MODULE tc_ghf_n_modify INPUT.
  MODIFY gt_t38
    FROM gs_t38
    INDEX tc_ghf_n-current_line.
ENDMODULE.
MODULE tc_zff_modify INPUT.
  MODIFY gt_t44
    FROM gs_t44
    INDEX tc_zff-current_line.
ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE
MODULE tc_ghf_n_mark INPUT.
  DATA: g_tc_ghf_n_wa2 LIKE LINE OF gt_t38.
  IF tc_ghf_n-line_sel_mode = 1
  AND gs_t38-sel = 'X'.
    LOOP AT gt_t38 INTO g_tc_ghf_n_wa2
      WHERE sel = 'X'.
      g_tc_ghf_n_wa2-sel = ''.
      MODIFY gt_t38
        FROM g_tc_ghf_n_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_t38
    FROM gs_t38
    INDEX tc_ghf_n-current_line
    TRANSPORTING sel.
ENDMODULE.

MODULE tc_zff_mark INPUT.
  DATA: g_tc_zff_wa2 LIKE LINE OF gt_t44.
  IF tc_zff-line_sel_mode = 1
  AND gs_t44-sel = 'X'.
    LOOP AT gt_t44 INTO g_tc_zff_wa2
      WHERE sel = 'X'.
      g_tc_zff_wa2-sel = ''.
      MODIFY gt_t44
        FROM g_tc_zff_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_t44
    FROM gs_t44
    INDEX tc_zff-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND
MODULE tc_ghf_n_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_GHF_N'
                              'gt_t38'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.

MODULE tc_zff_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_ZFF'
                              'gt_t44'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.

*&---------------------------------------------------------------------*
*& Module MDL_SET_TC_DATA_9912 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE mdl_set_tc_data_9912 OUTPUT.
  PERFORM frm_set_data_9012 CHANGING gs_t13.
ENDMODULE.
MODULE mdl_set_tc_data_9921 OUTPUT.
  PERFORM frm_set_data_9012 CHANGING gs_t38.
ENDMODULE.
MODULE mdl_set_tc_data_9922 OUTPUT.
  PERFORM frm_set_data_9922 CHANGING gs_t44.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_CHECK_ZGHF_ZZZPC  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_check_zghf_zzzpc INPUT.
  IF ( gs_t13-zghf = 'ALL' OR gs_t13-zghf IS INITIAL ) AND
      gs_t13-zzzpc = 'X'.
    MESSAGE e888(sabapdocu) WITH '供货方为ALL或空时不允许进行排除！'.
  ENDIF.
ENDMODULE.
MODULE mdl_check_zff INPUT.
*  IF ( gS_t38-ZGHF = 'ALL' OR gS_t38-ZGHF IS INITIAL ) AND
*      gS_t38-ZZZPC = 'X'.
*    MESSAGE E888(SABAPDOCU) WITH '供货方为ALL或空时不允许进行排除！'.
*  ENDIF.
  gs_t44-zflzff = |{ gs_t44-zflzff ALPHA = IN }|.
  SELECT SINGLE COUNT(*) FROM lfa1 WHERE lifnr = gs_t44-zflzff.
  IF sy-subrc NE 0.
    MESSAGE e888(sabapdocu) WITH '供应商不存在！'.
  ENDIF.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_SET_DATA_ZGHF  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_set_data_zghf INPUT.

*  若存在非ALL的数据，则自动删除ALL的数据
  LOOP AT gt_t13 TRANSPORTING NO FIELDS WHERE zghf IS NOT INITIAL AND zghf NE 'ALL'.
    EXIT.
  ENDLOOP.
  IF sy-subrc EQ 0.
    DELETE gt_t13 WHERE zghf = 'ALL'.
  ENDIF.
ENDMODULE.
MODULE mdl_set_data_zghf_n INPUT.

*  若存在非ALL的数据，则自动删除ALL的数据
  LOOP AT gt_t38 TRANSPORTING NO FIELDS WHERE zghf IS NOT INITIAL AND zghf NE 'ALL'.
    EXIT.
  ENDLOOP.
  IF sy-subrc EQ 0.
    DELETE gt_t38 WHERE zghf = 'ALL'.
  ENDIF.
ENDMODULE.

MODULE mdl_set_data_zff INPUT.


ENDMODULE.
*&---------------------------------------------------------------------*
*& Module STATUS_9912 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_9912 OUTPUT.
  PERFORM frm_set_screen_9912.
ENDMODULE.

MODULE status_9921 OUTPUT.
  PERFORM frm_set_screen_9921.
ENDMODULE.

MODULE status_9922 OUTPUT.
  PERFORM frm_set_screen_9922.
ENDMODULE.