*&---------------------------------------------------------------------*
*& 包含               ZRED0001_F01
*&---------------------------------------------------------------------*

*&---------------------------------------------------------------------*
*& FORM FRM_SCREEN_INIT
*&---------------------------------------------------------------------*
*& TEXT Q
*&---------------------------------------------------------------------*
*& -->  P1        TEXT
*& <--  P2        TEXT
*&---------------------------------------------------------------------*
FORM frm_screen_init .

  CLEAR:
        rb_add,
        rb_edit,
        rb_dis.

*  根据事务码控制进入后的界面显示
  CASE sy-tcode.
    WHEN 'ZRED0001A'.
      rb_add = 'X'.
      gv_title = '返利平台：返利协议创建'.
    WHEN 'ZRED0001B'.
      rb_edit = 'X'.
      gv_title = '返利平台：返利协议修改'.
    WHEN 'ZRED0001C'.
      rb_dis = 'X'.
      gv_title = '返利平台：返利协议显示'.
    WHEN 'ZRED0001D'.
      rb_rles = 'X'.
      gv_title = '返利平台：返利协议审批'.
    WHEN OTHERS.
      rb_add = 'X'.
      gv_title = '返利平台：返利协议维护'.
  ENDCASE.

  PERFORM frm_set_title  USING gv_title.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_SCREEN
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*& -->  P1        TEXT
*& <--  P2        TEXT
*&---------------------------------------------------------------------*
FORM frm_set_screen .

*  M01--新增
*  M02--修改，显示，审批

  CASE 'X'.
    WHEN rb_add.
      LOOP AT SCREEN.
        IF screen-group1 = 'M02' .
          screen-active = '0'.
        ELSE.
          screen-active = '1'.
        ENDIF.
        IF  screen-group1 = 'M04'.
          IF rb_ref = 'X'.
            screen-active = '1'.
          ELSE.
            screen-active = '0'.
          ENDIF.
        ENDIF.

        IF  screen-group1 = 'M05'.
          SELECT SINGLE zxybstyp INTO @DATA(lv_zxybstyp) FROM zret0002 WHERE zfllx = @p_zfllx.
          IF lv_zxybstyp = 'V' OR lv_zxybstyp = 'T'.
            screen-active = '1'.
          ELSE.
            screen-active = '0'.
          ENDIF.
          CLEAR lv_zxybstyp.
        ENDIF.

        MODIFY SCREEN.
      ENDLOOP.
    WHEN rb_edit.
      LOOP AT SCREEN.
        IF screen-group1 = 'M01' OR screen-group1 = 'M04' OR screen-group1 = 'M05'.
          screen-active = '0'.
        ELSE.
          screen-active = '1'.
        ENDIF.
        MODIFY SCREEN.
      ENDLOOP.
    WHEN rb_dis.
      LOOP AT SCREEN.
        IF screen-group1 = 'M01' OR screen-group1 = 'M04' OR screen-group1 = 'M05'..
          screen-active = '0'.
        ELSE.
          screen-active = '1'.
        ENDIF.
        MODIFY SCREEN.
      ENDLOOP.
    WHEN rb_rles.
      LOOP AT SCREEN.
        IF screen-group1 = 'M01' OR screen-group1 = 'M04' OR screen-group1 = 'M05'..
          screen-active = '0'.
        ELSE.
          screen-active = '1'.
        ENDIF.
        MODIFY SCREEN.
      ENDLOOP.
    WHEN OTHERS.
  ENDCASE.

*  通过事务码进入后隐藏单选按钮
  IF sy-tcode NE 'ZRED0001' AND
     sy-tcode NE 'SE38'.
    LOOP AT SCREEN.
      IF screen-group1 = 'M03'.
        screen-active = '0'.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_SCREEN
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*& -->  P1        TEXT
*& <--  P2        TEXT
*&---------------------------------------------------------------------*
FORM frm_check_screen .
  CASE 'X'.
    WHEN rb_add.

      IF p_zfllx IS INITIAL.

        MESSAGE s888(sabapdocu) WITH '返利类型必填' DISPLAY LIKE 'E'.
        LEAVE LIST-PROCESSING.

      ENDIF.

      IF p_zhtlx IS INITIAL.

        MESSAGE s888(sabapdocu) WITH '合同类型必填' DISPLAY LIKE 'E'.
        LEAVE LIST-PROCESSING.

      ENDIF.

*      PERFORM frm_check_zflsqf USING p_zflsqf.
      PERFORM frm_check_zflsqf USING p_zbukrs.

      PERFORM frm_check_zflzff USING p_zflzff.

      IF rb_ref  = 'X'.
        PERFORM frm_check_ref  USING p_zxyref.
      ENDIF.

    WHEN rb_edit OR rb_dis OR rb_rles.

      IF p_zxy_id IS INITIAL.

        MESSAGE s888(sabapdocu) WITH '返利协议号必填' DISPLAY LIKE 'E'.
        LEAVE LIST-PROCESSING.
      ENDIF.

    WHEN OTHERS.
  ENDCASE.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_MAIN
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*& -->  P1        TEXT
*& <--  P2        TEXT
*&---------------------------------------------------------------------*
FORM frm_main .

  PERFORM frm_init_data.

  CASE 'X'.
    WHEN rb_add.
      gv_title_02 = '创建'.
    WHEN rb_edit.
      gv_title_02 = '修改'.
    WHEN rb_dis.
      gv_title_02 = '显示'.
    WHEN rb_rles.
      gv_title_02 = '审批'.
    WHEN OTHERS.
  ENDCASE.


  IF rb_edit    = 'X' OR
     rb_rles    = 'X' .

*    锁定协议号
    PERFORM frm_pro_lock_zxy_id               USING p_zxy_id
                                                    ''.
*    协议状态检查
    PERFORM frm_check_zxyzt                   USING p_zxy_id.

  ENDIF.



  IF rb_edit    = 'X' OR
     rb_dis     = 'X' OR
     rb_rles    = 'X' OR rb_ref = 'X'.

*    获取已有数据
    PERFORM frm_get_data                      CHANGING  gs_t06
                                                        gt_t06
                                                        gt_t07
                                                        gt_t39
                                                        gt_t08
                                                        gs_t09
                                                        gt_t09
                                                        gs_t10
                                                        gt_t10
                                                        gt_t10_hs
                                                        gt_t11
                                                        gt_t11_hs
                                                        gt_t10_js
                                                        gt_t10_jsh
                                                        gt_t11_js
                                                        gt_t11_jsh
                                                        gt_t12
                                                        gt_t13
                                                        gt_t38
                                                        gt_t14
                                                        gt_t37
                                                        gt_t15
                                                        gt_t16
                                                        gt_t20
                                                        gt_t21
                                                        gt_t44
                                                        gt_bukrs
                                                        gt_dcwrk
                                                        gt_werks
                                                        gt_ekorg
                                                        gt_bukrs_n
                                                        gt_dcwrk_n
                                                        gt_werks_n
                                                        gt_ekorg_n
                                                        gt_data_9125.
    IF rb_ref = 'X'..
      PERFORM frm_pro_data_ref.
    ENDIF.
  ELSEIF rb_add = 'X'.

*   增加空白行
    PERFORM frm_add_init_line                 CHANGING  gt_t06
                                                        gt_t07
                                                        gt_t39
                                                        gt_t08
                                                        gt_t11
                                                        gt_t12
                                                        gt_t13
                                                        gt_t38
                                                        gt_t14
                                                        gt_t37
                                                        gt_t15
                                                        gt_t16
                                                        gt_t20
                                                        gt_t21
                                                        gt_bukrs
                                                        gt_dcwrk
                                                        gt_werks
                                                        gt_ekorg
                                                        gt_bukrs_n
                                                        gt_dcwrk_n
                                                        gt_werks_n
                                                        gt_ekorg_n.
  ENDIF.



*  获取协议类型
  CASE 'X'.
    WHEN rb_add.

      SELECT SINGLE zxybstyp
        FROM    zret0002
        WHERE   zfllx = @p_zfllx
        INTO    @gv_zxybstyp.

    WHEN rb_edit OR rb_dis OR rb_rles.

      gv_zxybstyp = gs_t06-zxybstyp.

*      SELECT SINGLE zxybstyp
*        FROM    zret0002
*        WHERE   zfllx = @gs_t06-zfllx
*        INTO    @gv_zxybstyp.

    WHEN OTHERS.
  ENDCASE.

*  动态设置核算基准下拉框
  PERFORM frm_set_zhsjz                 USING    gv_zxybstyp
                                        CHANGING gt_vls_zhsjz.

*    初始默认值
  IF rb_add = 'X' .
    IF rb_ref NE 'X'.

      gs_t06-zfllx        = p_zfllx.
      gs_t06-zflzff       = p_zflzff.
*    gs_t06-zflsqf       = p_zflsqf.
      gs_t06-zflsqf       = p_zbukrs.
      gs_t06-zbukrs       = p_zbukrs.
      gs_t06-zhtlx        = p_zhtlx.

      gs_t06-zxyzt        = 'N'.
      gs_t06-zhstype      = 'A'.
      gs_t06-zsplit       = cb_split.

      gs_t10-zjths        = 'X'.
      gs_t10-zflhs        = 'X'.

      CASE gv_zxybstyp.
        WHEN 'A'.
          gs_t10-zlhwd = 'M'.
          gs_t10-zflxs = 'M'.
        WHEN 'F'.
          gs_t06-zdffs = 'C'.
        WHEN 'Q'.
          gs_t06-zdffs = 'M'.
        WHEN 'V' OR 'T'.
          gs_t44-zflzff = gs_t06-zflzff.
          APPEND gs_t44 TO gt_t44.
        WHEN OTHERS.
      ENDCASE.

      IF gv_zxybstyp = 'T'.
        gs_t06-zhstype = 'A'.
        gs_t06-zhszq = '12M'.
      ENDIF.


      gs_t10_js = gs_t10.
    ENDIF.

    IF gv_zxybstyp = 'T' AND cb_split = 'X'.
      MESSAGE s888(sabapdocu) WITH '促销返利不能分拆任务量!' DISPLAY LIKE 'E'.
      LEAVE LIST-PROCESSING.
    ENDIF.
  ENDIF.

  IF cb_split = 'X'.
    gv_ght_t = '任务供货方'.
  ELSE.
    gv_ght_t = '供货方'.
  ENDIF.

  IF gv_zxybstyp = 'T'.
    gv_lifnr_t = '最终付款方'.
  ELSE.
    gv_lifnr_t = '渠道供应商'.
  ENDIF.
  CONDENSE gv_ght_t.


*  调用主屏幕
  PERFORM frm_call_screen .
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CALL_SCREEN
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*& -->  P1        TEXT
*& <--  P2        TEXT
*&---------------------------------------------------------------------*
FORM frm_call_screen .


*  动态调用屏幕
  CASE gv_zxybstyp.
    WHEN 'V' OR 'T'.
      gv_title_01 = '动态返利协议'.

      CALL SCREEN 9100.

    WHEN 'F'.
      gv_title_01 = '固定金额返利协议'.

      CALL SCREEN 9200.

    WHEN 'Q'.
      gv_title_01 = '固定数量返利协议'.

      CALL SCREEN 9300.

    WHEN 'A'.
      gv_title_01 = '付款返利协议'.

      CALL SCREEN 9400.

    WHEN OTHERS.

      MESSAGE s888(sabapdocu) WITH '返利协议类型错误' DISPLAY LIKE 'E'.
      LEAVE LIST-PROCESSING.

  ENDCASE.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_GET_DATA
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- GT_T06
*&      <-- GT_T12
*&      <-- GT_T13
*&      <-- GT_T07
*&      <-- GT_T08
*&---------------------------------------------------------------------*
FORM frm_get_data                  CHANGING   ps_t06       TYPE ty_t06
                                               pt_t06       TYPE tt_t06
                                               pt_t07       TYPE tt_t07
                                               pt_t39       TYPE tt_t39
                                               pt_t08       TYPE tt_t08
                                               ps_t09       TYPE ty_t09
                                               pt_t09       TYPE tt_t09
                                               ps_t10       TYPE ty_t10
                                               pt_t10       TYPE tt_t10
                                               pt_t10_hs    TYPE tt_t10
                                               pt_t11       TYPE tt_t11
                                               pt_t11_hs    TYPE tt_t11
                                               pt_t10_js    TYPE tt_t10
                                               pt_t10_jsh   TYPE tt_t10
                                               pt_t11_js    TYPE tt_t11
                                               pt_t11_jsh   TYPE tt_t11
                                               pt_t12       TYPE tt_t12
                                               pt_t13       TYPE tt_t13
                                               pt_t38       TYPE tt_t38
                                               pt_t14       TYPE tt_t14
                                               pt_t37       TYPE tt_t37
                                               pt_t15       TYPE tt_t15
                                               pt_t16       TYPE tt_t16
                                               pt_t20       TYPE tt_t20
                                               pt_t21       TYPE tt_t21
                                               pt_t44       TYPE tt_t44
                                               pt_bukrs     TYPE tt_bukrs
                                               pt_dcwrk     TYPE tt_dcwrk
                                               pt_werks     TYPE tt_werks
                                               pt_ekorg     TYPE tt_ekorg
                                               pt_bukrs_n   TYPE tt_bukrs
                                               pt_dcwrk_n   TYPE tt_dcwrk
                                               pt_werks_n   TYPE tt_werks
                                               pt_ekorg_n   TYPE tt_ekorg
                                               pt_data_9125 TYPE tt_data_9125.

*  获取协议抬头
  IF rb_ref = 'X'.
    p_zxy_id = p_zxyref.
  ENDIF.

  SELECT
    *
    INTO CORRESPONDING FIELDS OF TABLE pt_t06
    FROM zret0006
    WHERE zxy_id = p_zxy_id.

  IF pt_t06[] IS INITIAL.
    MESSAGE s888(sabapdocu) WITH '没有对应的协议存在！' DISPLAY LIKE 'E'.
    LEAVE LIST-PROCESSING.
  ENDIF.

*  获取动态协议行项目
  SELECT
    a~*
    FROM @pt_t06 AS m JOIN zret0007 AS a
                      ON m~zxy_id = a~zxy_id
    WHERE m~zxy_id = @p_zxy_id
    INTO CORRESPONDING FIELDS OF TABLE @pt_t07.

*  获取动态协议行项目
  SELECT
    a~*
    FROM @pt_t06 AS m JOIN zret0039 AS a
                      ON m~zxy_id = a~zxy_id
    WHERE m~zxy_id = @p_zxy_id
    INTO CORRESPONDING FIELDS OF TABLE @pt_t39.


*  获取固定协议行项目
  SELECT
    a~*
    FROM @pt_t06 AS m JOIN zret0008 AS a
                      ON m~zxy_id = a~zxy_id
    WHERE m~zxy_id = @p_zxy_id
    INTO CORRESPONDING FIELDS OF TABLE @pt_t08.


*  获取商品组信息
*   商品组根据屏幕处理实时从数据库中获取

  IF rb_ref = 'X'.
    IF pt_t07[] IS NOT INITIAL.
      SELECT
        a~*
        FROM @pt_t07 AS m JOIN zret0009 AS a
                          ON   m~zspwd = 'C'
                          AND  m~zspz_id = a~zspz_id
        INTO CORRESPONDING FIELDS OF TABLE @pt_t09.
      IF pt_t09[] IS NOT INITIAL.
        SELECT
          a~*
          FROM @pt_t09 AS m JOIN zret0020 AS a
                            ON   m~zspz_id = a~zspz_id
          INTO CORRESPONDING FIELDS OF TABLE @pt_t20.
      ENDIF.
    ENDIF.

    IF pt_t39[] IS NOT INITIAL.
      SELECT
        a~*
        FROM @pt_t39 AS m JOIN zret0009 AS a
                          ON   m~zspwd = 'C'
                          AND  m~zspz_id = a~zspz_id
        APPENDING CORRESPONDING FIELDS OF TABLE @pt_t09.
      IF pt_t09[] IS NOT INITIAL.
        SELECT
          a~*
          FROM @pt_t09 AS m JOIN zret0020 AS a
                            ON   m~zspz_id = a~zspz_id
          APPENDING CORRESPONDING FIELDS OF TABLE @pt_t20.
      ENDIF.
    ENDIF.
  ENDIF.

*  获取渠道供应商 供货方 生产商
  SELECT
    a~*
    FROM @pt_t06 AS m JOIN zret0012 AS a
                      ON m~zxy_id = a~zxy_id
    WHERE m~zxy_id = @p_zxy_id
    INTO CORRESPONDING FIELDS OF TABLE @pt_t12.

  SELECT
    a~*
    FROM @pt_t06 AS m JOIN zret0013 AS a
                      ON m~zxy_id = a~zxy_id
    WHERE m~zxy_id = @p_zxy_id
    INTO CORRESPONDING FIELDS OF TABLE @pt_t13.

  SELECT
    a~*
    FROM @pt_t06 AS m JOIN zret0038 AS a
                      ON m~zxy_id = a~zxy_id
    WHERE m~zxy_id = @p_zxy_id
    INTO CORRESPONDING FIELDS OF TABLE @pt_t38.

  SELECT
    a~*
    FROM @pt_t06 AS m JOIN zret0021 AS a
                      ON m~zxy_id = a~zxy_id
    WHERE m~zxy_id = @p_zxy_id
    INTO CORRESPONDING FIELDS OF TABLE @pt_t21.

*  获取组织结构
  SELECT
    a~*
    FROM @pt_t06 AS m JOIN zret0014 AS a
                      ON m~zxy_id = a~zxy_id
    WHERE m~zxy_id = @p_zxy_id
    INTO CORRESPONDING FIELDS OF TABLE @pt_t14.

*  获取组织结构
  SELECT
    a~*
    FROM @pt_t06 AS m JOIN zret0037 AS a
                      ON m~zxy_id = a~zxy_id
    WHERE m~zxy_id = @p_zxy_id
    INTO CORRESPONDING FIELDS OF TABLE @pt_t37.

*   组织结构解析
  PERFORM frm_zzzlx_unfold                USING       pt_t14
                                          CHANGING    pt_bukrs
                                                      pt_dcwrk
                                                      pt_werks
                                                      pt_ekorg .
*   组织结构解析
  PERFORM frm_zzzlx_unfold                USING       pt_t37
                                          CHANGING    pt_bukrs_n
                                                      pt_dcwrk_n
                                                      pt_werks_n
                                                      pt_ekorg_n.

*  获取核算期间
  SELECT
    a~*
    FROM @pt_t06 AS m JOIN zret0015 AS a
                      ON m~zxy_id = a~zxy_id
    WHERE m~zxy_id = @p_zxy_id
    INTO CORRESPONDING FIELDS OF TABLE @pt_t15.

*  获取结算期间
  SELECT
    a~*
    FROM @pt_t06 AS m JOIN zret0016 AS a
                      ON m~zxy_id = a~zxy_id
    WHERE m~zxy_id = @p_zxy_id
    INTO CORRESPONDING FIELDS OF TABLE @pt_t16.


*  获取协议主阶梯信息
  SELECT
    a~*
    FROM @pt_t06 AS m JOIN zret0010 AS a
                      ON   m~zjt_id = a~zjt_id
    INTO CORRESPONDING FIELDS OF TABLE @pt_t10.

*  获取主阶梯明细
  IF pt_t10[] IS NOT INITIAL.
    SELECT
      a~*
      FROM @pt_t10 AS m JOIN zret0011 AS a
                        ON   m~zjt_id = a~zjt_id
      INTO CORRESPONDING FIELDS OF TABLE @pt_t11.
  ENDIF.

*  获取核算期间子阶梯
  IF pt_t15[] IS NOT INITIAL.
    SELECT
      a~*
      FROM @pt_t15 AS m JOIN zret0010 AS a
                        ON   m~zjt_id = a~zjt_id
      INTO CORRESPONDING FIELDS OF TABLE @pt_t10_hs.
  ENDIF.

*  获取子阶梯明细
  IF pt_t10_hs[] IS NOT INITIAL.
    SELECT
      a~*
      FROM @pt_t10_hs AS m JOIN zret0011 AS a
                        ON   m~zjt_id = a~zjt_id
      INTO CORRESPONDING FIELDS OF TABLE @pt_t11_hs.
  ENDIF.

*  获取返利计算子阶梯抬头
  IF pt_t11[] IS NOT INITIAL.
    SELECT
      a~*
      FROM @pt_t11 AS m JOIN zret0010 AS a
                        ON   m~zfljt_id = a~zjt_id
      INTO CORRESPONDING FIELDS OF TABLE @pt_t10_js.
  ENDIF.

*  获取返利计算子阶梯明细
  IF pt_t10_js[] IS NOT INITIAL.
    SELECT
      a~*
      FROM @pt_t10_js AS m JOIN zret0011 AS a
                        ON   m~zjt_id = a~zjt_id
      INTO CORRESPONDING FIELDS OF TABLE @pt_t11_js.
  ENDIF.

*  获取核算周期返利计算子阶梯抬头
  IF pt_t11_hs[] IS NOT INITIAL.
    SELECT
      a~*
      FROM @pt_t11_hs AS m JOIN zret0010 AS a
                        ON   m~zfljt_id = a~zjt_id
                        AND  a~zjt_id NE ''
      INTO CORRESPONDING FIELDS OF TABLE @pt_t10_jsh.
    IF pt_t10_jsh[] IS NOT INITIAL.
      APPEND LINES OF pt_t10_jsh TO pt_t10_js.
    ENDIF.
  ENDIF.

*  获取核算周期返利计算子阶梯明细
  IF pt_t10_jsh[] IS NOT INITIAL.
    SELECT
      a~*
      FROM @pt_t10_jsh AS m JOIN zret0011 AS a
                        ON   m~zjt_id = a~zjt_id
      INTO CORRESPONDING FIELDS OF TABLE @pt_t11_jsh.
  ENDIF.

*  获取支付方清单
  SELECT
    a~*
    FROM @pt_t06 AS m JOIN zret0044 AS a
                      ON m~zxy_id = a~zxy_id
    WHERE m~zxy_id = @p_zxy_id
    INTO CORRESPONDING FIELDS OF TABLE @pt_t44.


*  获取动态协议行项目
*&&&&& SELECT
*&&&&&   a~*
*&&&&&   FROM @pt_t06 AS m JOIN zret0025 AS a
*&&&&&                     ON m~zxy_id = a~zxy_id
*&&&&&   WHERE m~zxy_id = @p_zxy_id
*&&&&&   INTO CORRESPONDING FIELDS OF TABLE @pt_data_9125.


*  协议抬头数据-屏幕
  IF line_exists( pt_t06[ 1 ] ).
    gs_t06 = pt_t06[ 1 ].
  ENDIF.

*  阶梯抬头数据-屏幕
  IF line_exists( pt_t10[ 1 ] ).
    gs_t10 = pt_t10[ 1 ].
  ENDIF.

  IF line_exists( pt_t10_js[ 1 ] ).
    gs_t10_js = pt_t10_js[ 1 ].
  ENDIF.

  IF gs_t06-zsplit = 'X'.
    cb_split = 'X'.
  ENDIF.

  SORT gt_t16 BY zbegin.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_DATA_9003
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- GS_BUKRS
*&---------------------------------------------------------------------*
FORM frm_set_data_9003  CHANGING ps_bukrs TYPE ty_bukrs.
  SELECT SINGLE
    butxt
    FROM t001
    WHERE bukrs = @ps_bukrs-bukrs
    INTO @ps_bukrs-butxt.

  IF ps_bukrs-bukrs = 'ALL'.
    ps_bukrs-butxt = '全部'.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_DATA_9004
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- GS_DCWRK
*&---------------------------------------------------------------------*
FORM frm_set_data_9004  CHANGING ps_dcwrk TYPE ty_dcwrk.
  SELECT SINGLE
    name1
    FROM t001w
    WHERE werks = @ps_dcwrk-dcwrk
    INTO @ps_dcwrk-name1.

  IF ps_dcwrk-dcwrk = 'ALL'.
    ps_dcwrk-name1 = '全部'.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_DATA_9006
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- GS_EKORG
*&---------------------------------------------------------------------*
FORM frm_set_data_9006  CHANGING ps_ekorg TYPE ty_ekorg.
  SELECT SINGLE
    ekotx
    FROM t024e
    WHERE ekorg = @ps_ekorg-ekorg
    INTO @ps_ekorg-ekotx.

  IF ps_ekorg-ekorg = 'ALL'.
    ps_ekorg-ekotx = '全部'.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_DATA_9005
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- GS_WERKS
*&---------------------------------------------------------------------*
FORM frm_set_data_9005  CHANGING ps_werks TYPE ty_werks.
  SELECT SINGLE
    name1
    FROM t001w
    WHERE werks = @ps_werks-werks
    INTO @ps_werks-name1.

  IF ps_werks-werks = 'ALL'.
    ps_werks-name1 = '全部'.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_GET_ZXY_ID
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- PS_T06_ZXY_ID
*&---------------------------------------------------------------------*
FORM frm_get_zxy_id   CHANGING pv_zxy_id TYPE zret0006-zxy_id.
  PERFORM frm_get_num USING 'ZRE0001' '01' CHANGING pv_zxy_id.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_GET_ZJT_ID
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- PS_T10_ZJT_ID
*&---------------------------------------------------------------------*
FORM frm_get_zjt_id  CHANGING pv_zjt_id TYPE zret0010-zjt_id.
  PERFORM frm_get_num USING 'ZRE0002' '01' CHANGING pv_zjt_id.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_GET_ZSPZ_ID
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- PS_T07_ZSPZ_ID
*&---------------------------------------------------------------------*
FORM frm_get_zspz_id  CHANGING pv_zspz_id TYPE zret0009-zspz_id.
  PERFORM frm_get_num USING 'ZRE0003' '01' CHANGING pv_zspz_id.
ENDFORM.

FORM frm_get_num      USING u_object u_range_no CHANGING c_next_num.
  DATA:
    lv_times     TYPE i VALUE 1,
    lv_times_max TYPE i VALUE 5.

  CLEAR c_next_num.

  WHILE c_next_num = '' AND lv_times <= lv_times_max.
    lv_times = lv_times + 1.

    CALL FUNCTION 'NUMBER_RANGE_ENQUEUE'
      EXPORTING
        object           = u_object
      EXCEPTIONS
        foreign_lock     = 1
        object_not_found = 2
        system_failure   = 3
        OTHERS           = 4.
    IF sy-subrc NE 0.
      WAIT UP TO 1 SECONDS .
    ELSE.
      CALL FUNCTION 'NUMBER_GET_NEXT'
        EXPORTING
          nr_range_nr             = u_range_no
          object                  = u_object
*         QUANTITY                = '1'
*         SUBOBJECT               = ' '
*         TOYEAR                  = '0000'
          ignore_buffer           = 'X'
        IMPORTING
          number                  = c_next_num "流水号
*         QUANTITY                =
*         RETURNCODE              =
        EXCEPTIONS
          interval_not_found      = 1
          number_range_not_intern = 2
          object_not_found        = 3
          quantity_is_0           = 4
          quantity_is_not_1       = 5
          interval_overflow       = 6
          buffer_overflow         = 7
          OTHERS                  = 8.
      IF sy-subrc <> 0.
        WAIT UP TO 1 SECONDS .
*        MESSAGE ID SY-MSGID TYPE SY-MSGTY NUMBER SY-MSGNO
*          WITH SY-MSGV1 SY-MSGV2 SY-MSGV3 SY-MSGV4.
      ELSE.
        CALL FUNCTION 'NUMBER_RANGE_DEQUEUE'
          EXPORTING
            object           = u_object
          EXCEPTIONS
            object_not_found = 1
            OTHERS           = 2.
        EXIT.
      ENDIF.
    ENDIF.
  ENDWHILE.

  IF c_next_num IS INITIAL.
    MESSAGE e888(sabapdocu) WITH '获取流水号失败！' DISPLAY LIKE 'E'.
  ENDIF.

ENDFORM.                    "GET_NUM
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_BEFORE_SAVE
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- GV_FLG_ERR
*&      <-- GV_MSG
*&---------------------------------------------------------------------*
FORM frm_check_before_save              USING       ps_t06        TYPE ty_t06
                                                    ps_t10        TYPE ty_t10
                                                    ps_t10_js     TYPE ty_t10
                                                    pt_t11        TYPE tt_t11
                                                    pt_t11_hs     TYPE tt_t11
                                                    pt_t11_js     TYPE tt_t11
                                                    pt_t11_jsh    TYPE tt_t11
                                                    pt_t07        TYPE tt_t07
                                                    pt_t39        TYPE tt_t39
                                                    pt_t20        TYPE tt_t20
                                                    pt_t13        TYPE tt_t13
                                                    pt_t38        TYPE tt_t38
                                                    pt_bukrs      TYPE tt_bukrs
                                                    pt_dcwrk      TYPE tt_dcwrk
                                                    pt_werks      TYPE tt_werks
                                                    pt_ekorg      TYPE tt_ekorg
                                                    pt_bukrs_n    TYPE tt_bukrs
                                                    pt_dcwrk_n    TYPE tt_dcwrk
                                                    pt_werks_n    TYPE tt_werks
                                                    pt_ekorg_n    TYPE tt_ekorg
                                        CHANGING    pv_flg_err    TYPE char1.

  PERFORM frm_data_check_main           USING       ps_t06
                                                    ps_t10
                                                    ps_t10_js
                                                    pt_t11
                                                    pt_t11_hs
                                                    pt_t11_js
                                                    pt_t11_jsh
                                                    pt_t07
                                                    pt_t39
                                                    pt_t20
                                                    pt_t13
                                                    pt_t38
                                                    pt_bukrs
                                                    pt_dcwrk
                                                    pt_werks
                                                    pt_ekorg
                                                    pt_bukrs_n
                                                    pt_dcwrk_n
                                                    pt_werks_n
                                                    pt_ekorg_n
                                                    gt_t12
                                                    gt_t21
                                        CHANGING    pv_flg_err.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SAVE_ALL_DATA
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*& -->  P1        TEXT
*& <--  P2        TEXT
*&---------------------------------------------------------------------*
FORM frm_save_all_data                  USING pv_flg TYPE char10.

  DATA:
    lv_mtype TYPE bapi_mtype,
    lv_msg   TYPE bapi_msg.

  PERFORM frm_pro_data_command                  USING     pv_flg
                                                CHANGING  gs_t06
                                                          gs_t09.

  PERFORM frm_zzzlx_fold                        USING     gt_bukrs
                                                          gt_dcwrk
                                                          gt_werks
                                                          gt_ekorg
                                                          'A'
                                                CHANGING  gt_t14.

  PERFORM frm_zzzlx_fold                        USING     gt_bukrs_n
                                                          gt_dcwrk_n
                                                          gt_werks_n
                                                          gt_ekorg_n
                                                          'B'
                                                CHANGING  gt_t37.

  PERFORM frm_pro_before_save                   CHANGING  gs_t06
                                                          gt_t06
                                                          gt_t07
                                                          gt_t39
                                                          gt_t08
                                                          gt_t09
                                                          gs_t10
                                                          gs_t10_js
                                                          gt_t10
                                                          gt_t10_null
                                                          gt_t11
                                                          gt_t10_hs
                                                          gt_t11_hs
                                                          gt_t10_js
                                                          gt_t11_js
                                                          gt_t10_jsh
                                                          gt_t11_jsh
                                                          gt_t12
                                                          gt_t13
                                                          gt_t38
                                                          gt_t14
                                                          gt_t37
                                                          gt_t15
                                                          gt_t16
                                                          gt_t20
                                                          gt_t21
                                                          gt_t44  .

  PERFORM frm_save_data_into_db                   USING   pv_flg
                                                          gs_t06
                                                          gt_t06
                                                          gt_t07
                                                          gt_t39
                                                          gt_t08
                                                          gt_t09
                                                          gt_t10
                                                          gt_t10_null
                                                          gt_t11
                                                          gt_t10_hs
                                                          gt_t11_hs
                                                          gt_t10_js
                                                          gt_t11_js
                                                          gt_t10_jsh
                                                          gt_t11_jsh
                                                          gt_t12
                                                          gt_t13
                                                          gt_t38
                                                          gt_t14
                                                          gt_t37
                                                          gt_t15
                                                          gt_t16
                                                          gt_t20
                                                          gt_t21
                                                          gt_t44
                                                   CHANGING
                                                          lv_mtype
                                                          lv_msg.

  IF lv_mtype = 'S'.

*    将组织数据和商品维度解析保存

*    保证更新成功
    IF gt_t14[] IS NOT INITIAL.
      DO 1000 TIMES.
        SELECT COUNT(*) FROM zret0014 WHERE zxy_id = gs_t06-zxy_id.
        IF sy-subrc NE 0.
          WAIT UP TO '0.1' SECONDS.
        ELSE.
          EXIT.
        ENDIF.
      ENDDO.
    ENDIF.

    IF gt_t37[] IS NOT INITIAL.
      DO 1000 TIMES.
        SELECT COUNT(*) FROM zret0037 WHERE zxy_id = gs_t06-zxy_id.
        IF sy-subrc NE 0.
          WAIT UP TO '0.1' SECONDS.
        ELSE.
          EXIT.
        ENDIF.
      ENDDO.
    ENDIF.

    IF gt_t07[] IS NOT INITIAL.
      DO 1000 TIMES.
        SELECT COUNT(*) FROM zret0007 WHERE zxy_id = gs_t06-zxy_id.
        IF sy-subrc NE 0.
          WAIT UP TO '0.1' SECONDS.
        ELSE.
          EXIT.
        ENDIF.
      ENDDO.
    ENDIF.
    IF gt_t39[] IS NOT INITIAL.
      DO 1000 TIMES.
        SELECT COUNT(*) FROM zret0039 WHERE zxy_id = gs_t06-zxy_id.
        IF sy-subrc NE 0.
          WAIT UP TO '0.1' SECONDS.
        ELSE.
          EXIT.
        ENDIF.
      ENDDO.
    ENDIF.

    PERFORM frm_save_data_att                      USING  pv_flg
                                                          gt_t06.


    CASE pv_flg.
      WHEN 'SAVE'.
        MESSAGE s888(sabapdocu) WITH '协议保存成功！'.
      WHEN 'COMT'.
        MESSAGE s888(sabapdocu) WITH '协议提交成功！'.
      WHEN 'RLES'.
        MESSAGE s888(sabapdocu) WITH '协议审批成功！'.
      WHEN 'RLES_C'.
        MESSAGE s888(sabapdocu) WITH '协议取消审批成功！'.
      WHEN 'CANCEL'.
        MESSAGE s888(sabapdocu) WITH '协议作废成功！'.
      WHEN OTHERS.
    ENDCASE.

  ELSE.
    MESSAGE s888(sabapdocu) WITH lv_msg DISPLAY LIKE 'E'.
  ENDIF.


ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_PRO_BEFORE_SAVE
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- GS_T06
*&---------------------------------------------------------------------*
FORM frm_pro_before_save                CHANGING  ps_t06        TYPE ty_t06
                                                  pt_t06        TYPE tt_t06
                                                  pt_t07        TYPE tt_t07
                                                  pt_t39        TYPE tt_t39
                                                  pt_t08        TYPE tt_t08
                                                  pt_t09        TYPE tt_t09
                                                  ps_t10        TYPE ty_t10
                                                  ps_t10_js     TYPE ty_t10
                                                  pt_t10        TYPE tt_t10
                                                  pt_t10_null   TYPE tt_t10
                                                  pt_t11        TYPE tt_t11
                                                  pt_t10_hs     TYPE tt_t10
                                                  pt_t11_hs     TYPE tt_t11
                                                  pt_t10_js     TYPE tt_t10
                                                  pt_t11_js     TYPE tt_t11
                                                  pt_t10_jsh    TYPE tt_t10
                                                  pt_t11_jsh    TYPE tt_t11
                                                  pt_t12        TYPE tt_t12
                                                  pt_t13        TYPE tt_t13
                                                  pt_t38        TYPE tt_t38
                                                  pt_t14        TYPE tt_t14
                                                  pt_t37        TYPE tt_t37
                                                  pt_t15        TYPE tt_t15
                                                  pt_t16        TYPE tt_t16
                                                  pt_t20        TYPE tt_t20
                                                  pt_t21        TYPE tt_t21
                                                  pt_t44        TYPE tt_t44.

  DATA:
        lv_num TYPE i.
  DATA:
    ls_t10_hs TYPE LINE OF tt_t10,
    ls_t11_hs TYPE LINE OF tt_t11.


  CLEAR:
        pt_t06,
        pt_t09,
        pt_t10,
        pt_t10_hs,
        pt_t10_js,
        pt_t10_jsh,
        pt_t10_null.


*  剔除无效空数据
  DELETE pt_t07       WHERE zspwd         IS INITIAL.
  DELETE pt_t39       WHERE zspwd         IS INITIAL.
*  DELETE pt_t08       WHERE zdate         IS INITIAL.
  DELETE pt_t20       WHERE matnr         IS INITIAL.
  DELETE pt_t12       WHERE lifnr         IS INITIAL.
  DELETE pt_t13       WHERE zghf          IS INITIAL.
  DELETE pt_t38       WHERE zghf          IS INITIAL.
  DELETE pt_t21       WHERE zcj           IS INITIAL.
  DELETE pt_t14       WHERE zzzlx         IS INITIAL.
  DELETE pt_t37       WHERE zzzlx         IS INITIAL.
  DELETE pt_t15       WHERE zbegin        IS INITIAL.
  DELETE pt_t16       WHERE zbegin        IS INITIAL.
  DELETE pt_t11       WHERE zjt_from      IS INITIAL AND zjt_by IS INITIAL.
  DELETE pt_t11_hs    WHERE zjt_from      IS INITIAL AND zjt_by IS INITIAL.
  DELETE pt_t11_js    WHERE zjt_from      IS INITIAL AND zjt_by IS INITIAL.
  DELETE pt_t11_jsh    WHERE zjt_from      IS INITIAL AND zjt_by IS INITIAL.


*  阶梯数据处理 防止因未回车导致的错误
  PERFORM frm_set_data_zjt  USING    ps_t10
                            CHANGING pt_t11.
*
*  PERFORM frm_set_data_zjt  USING    ps_t10
*                            CHANGING pt_t11_hs.


*  排序
  SORT pt_t11   BY zjt_from zjt_by.
  SORT pt_t15   BY zbegin zend.
  SORT pt_t16   BY zbegin zend.

  IF rb_add = 'X' AND ps_t06-zxy_id IS INITIAL.

*    获取协议编号
    PERFORM frm_get_zxy_id CHANGING ps_t06-zxy_id.

  ENDIF.

  IF rb_add = 'X' AND ps_t10-zjt_id IS INITIAL.

*    获取阶梯编号
    PERFORM frm_get_zjt_id CHANGING ps_t10-zjt_id.

  ENDIF.



*  支付方清单
  IF gv_zxybstyp = 'V' OR gv_zxybstyp = 'T' .
    PERFORM frm_pro_zff CHANGING ps_t06
                                 pt_t44.

    PERFORM frm_set_zmtype USING  pt_t44
                           CHANGING ps_t06.

    LOOP AT pt_t44 INTO DATA(ls_t44).
      ls_t44-zflzff   = |{ ls_t44-zflzff ALPHA = IN }| .
      ls_t44-zxy_id = ps_t06-zxy_id.
      MODIFY pt_t44 FROM ls_t44.
    ENDLOOP.

*    IF lines( pt_t44 ) <= 1.
*      CLEAR pt_t44[].
*    ENDIF.
  ELSE.
    PERFORM frm_pro_zff CHANGING ps_t06
                                 pt_t44.

    LOOP AT pt_t44 INTO ls_t44.
      ls_t44-zflzff   = |{ ls_t44-zflzff ALPHA = IN }| .
      ls_t44-zxy_id = ps_t06-zxy_id.
      MODIFY pt_t44 FROM ls_t44.
    ENDLOOP.

  ENDIF.

*  将抬头结构数据转为表数据
*  协议抬头表
  ps_t06-zjt_id   = ps_t10-zjt_id.
  ps_t06-zflzff   = |{ ps_t06-zflzff ALPHA = IN }| .
  ps_t06-zydqd    = 'M'.
  ps_t06-zxybstyp = gv_zxybstyp.
  APPEND ps_t06 TO pt_t06.

*  阶梯抬头表
  APPEND ps_t10 TO pt_t10.

*  动态返利协议-行项目
  CLEAR lv_num.
  LOOP AT pt_t07 INTO DATA(ls_t07).
    lv_num            = lv_num + 1.
    ls_t07-zxy_id     = ps_t06-zxy_id.
    ls_t07-zxy_itemid = lv_num.
    ls_t07-matnr      = |{ ls_t07-matnr ALPHA = IN WIDTH = 18 }| .
    PERFORM frm_set_data_07 CHANGING ls_t07.
    MODIFY pt_t07 FROM ls_t07.
  ENDLOOP.

*  动态返利协议-行项目
  CLEAR lv_num.
  LOOP AT pt_t39 INTO DATA(ls_t39).
    lv_num            = lv_num + 1.
    ls_t39-zxy_id     = ps_t06-zxy_id.
    ls_t39-zxy_itemid = lv_num.
    ls_t39-matnr      = |{ ls_t39-matnr ALPHA = IN WIDTH = 18 }| .
    PERFORM frm_set_data_07 CHANGING ls_t39.
    MODIFY pt_t39 FROM ls_t39.
  ENDLOOP.

*  固定返利协议-行项目
  CLEAR lv_num.
  LOOP AT pt_t08 INTO DATA(ls_t08).
    lv_num            = lv_num + 1.
    ls_t08-zxy_id     = ps_t06-zxy_id.
    ls_t08-zxy_itemid = lv_num.
    ls_t08-matnr      = |{ ls_t08-matnr ALPHA = IN WIDTH = 18 }| .
    ls_t08-lifnr      = |{ ls_t08-lifnr ALPHA = IN }| .
    MODIFY pt_t08 FROM ls_t08.
  ENDLOOP.

*  核算期间表
  CLEAR lv_num.
  LOOP AT pt_t15 INTO DATA(ls_t15).
    lv_num            = lv_num + 1.
    ls_t15-zhsqj_id   = lv_num.
    ls_t15-zxy_id     = ps_t06-zxy_id.

*    若没有子阶梯明细，则将子阶梯号码清空
    READ TABLE pt_t11_hs TRANSPORTING NO FIELDS WITH KEY zjt_id = ls_t15-zjt_id.
    IF sy-subrc NE 0.
      CLEAR ls_t15-zjt_id.
    ENDIF.
    MODIFY pt_t15 FROM ls_t15.
  ENDLOOP.


*  结算期间表
  CLEAR lv_num.
  LOOP AT pt_t16 INTO DATA(ls_t16).
    lv_num            = lv_num + 1.
    ls_t16-zjsqj_id   = lv_num.
    ls_t16-zxy_id     = ps_t06-zxy_id.
    MODIFY pt_t16 FROM ls_t16.
  ENDLOOP.

*  渠道供应商
  LOOP AT pt_t12 INTO DATA(ls_t12).
    ls_t12-zxy_id     = ps_t06-zxy_id.
    ls_t12-lifnr      = |{ ls_t12-lifnr ALPHA = IN }| .
    MODIFY pt_t12 FROM ls_t12.
  ENDLOOP.
  IF pt_t12[] IS INITIAL.
    ls_t12-zxy_id     = ps_t06-zxy_id.
    ls_t12-lifnr      = 'ALL'.
    APPEND ls_t12 TO pt_t12.
  ENDIF.

*  生产商
  LOOP AT pt_t21 INTO DATA(ls_t21).
    ls_t21-zxy_id     = ps_t06-zxy_id.
    MODIFY pt_t21 FROM ls_t21.
  ENDLOOP.
  IF pt_t21[] IS INITIAL.
    ls_t21-zxy_id     = ps_t06-zxy_id.
    ls_t21-zcj        = 'ALL'.
    APPEND ls_t21 TO pt_t21.
  ENDIF.

*  供货方
  LOOP AT pt_t13 INTO DATA(ls_t13).
    ls_t13-zxy_id     = ps_t06-zxy_id.
    ls_t13-zghf       = |{ ls_t13-zghf ALPHA = IN }| .
    MODIFY pt_t13 FROM ls_t13.
  ENDLOOP.
  IF pt_t13[] IS INITIAL.
    ls_t13-zxy_id     = ps_t06-zxy_id.
    ls_t13-zghf       = 'ALL'.
    APPEND ls_t13 TO pt_t13.
  ENDIF.

*  供货方
  LOOP AT pt_t38 INTO DATA(ls_t38).
    ls_t38-zxy_id     = ps_t06-zxy_id.
    ls_t38-zghf       = |{ ls_t38-zghf ALPHA = IN }| .
    MODIFY pt_t38 FROM ls_t38.
  ENDLOOP.
  IF pt_t38[] IS INITIAL.
    ls_t38-zxy_id     = ps_t06-zxy_id.
    ls_t38-zghf       = 'ALL'.
    APPEND ls_t38 TO pt_t38.
  ENDIF.


*  组织结构
  LOOP AT pt_t14 INTO DATA(ls_t14).
    ls_t14-zxy_id     = ps_t06-zxy_id.
    MODIFY pt_t14 FROM ls_t14.
  ENDLOOP.

*  组织结构
  LOOP AT pt_t37 INTO DATA(ls_t37).
    ls_t37-zxy_id     = ps_t06-zxy_id.
    MODIFY pt_t37 FROM ls_t37.
  ENDLOOP.

*  商品组明细
*  从屏幕处理中更新到数据库
***  LOOP AT pt_t20 INTO DATA(ls_t20).
***    ls_t20-matnr   = |{ ls_t20-matnr ALPHA = IN WIDTH = 18 }| .
***    MODIFY pt_t20 FROM ls_t20.
***  ENDLOOP.

*  阶梯明细表行项目编号
  CLEAR lv_num.
  LOOP AT pt_t11 INTO DATA(ls_t11).
    lv_num            = lv_num + 1.
    ls_t11-zjt_itemid = lv_num.
    ls_t11-zjt_id     = ps_t10-zjt_id.
    MODIFY pt_t11 FROM ls_t11.
  ENDLOOP.

*  阶梯抬头处理
  PERFORM frm_process_t10 USING ps_t10.
  PERFORM frm_process_t10 USING ps_t10_js.

*  核算期间子阶梯明细表
  PERFORM frm_process_zjt USING       ps_t10
                          CHANGING    pt_t10_hs
                                      pt_t11_hs.

*  返利计算子阶梯明细表
  PERFORM frm_process_zjt USING       ps_t10_js
                          CHANGING    pt_t10_js
                                      pt_t11_js.

*  核算期间子阶梯返利计算子阶梯明细表
  PERFORM frm_process_zjt USING       ps_t10_js
                          CHANGING    pt_t10_jsh
                                      pt_t11_jsh.

*  增加虚拟子阶梯
  IF ps_t10-zjsff = 'T'.
    PERFORM frm_add_data_zjt  USING       ps_t10
                                          pt_t11
                              CHANGING    pt_t10_null.

    PERFORM frm_add_data_zjt  USING       ps_t10_js
                                          pt_t11_hs
                              CHANGING    pt_t10_null.
  ENDIF.


*****
******  核算期间子阶梯明细表
*****  CLEAR lv_num.
*****  SORT pt_t11_hs BY zjt_id.
*****
******  将阶梯表的MANDT置为空，防止 AT NEW 语句BUG
*****  ls_t11_hs-mandt = ''.
*****  MODIFY pt_t11_hs FROM ls_t11_hs TRANSPORTING mandt WHERE mandt NE ''.
*****  CLEAR ls_t11_hs.
*****
*****  LOOP AT pt_t11_hs INTO ls_t11_hs.
*****    AT NEW zjt_id.
******      阶梯号码更改后，行项目重新编号
*****      CLEAR lv_num.
*****    ENDAT.
*****    lv_num                = lv_num + 1.
*****    ls_t11_hs-zjt_itemid  = lv_num.
*****    MODIFY pt_t11_hs FROM ls_t11_hs.
*****
******   根据明细表组建抬头表 因为核算期间子阶梯没有抬头数据
******   取协议主阶梯中的数据
*****    CLEAR: ls_t10_hs.
*****    ls_t10_hs-zjt_id = ls_t11_hs-zjt_id.
*****    ls_t10_hs-zlhwd = ps_t10-zlhwd.
*****    ls_t10_hs-zjths = ps_t10-zjths.
*****    ls_t10_hs-zjtlx = ps_t10-zjtlx.
*****    ls_t10_hs-zflxs = ps_t10-zflxs.
*****    ls_t10_hs-zjsff = ps_t10-zjsff.
*****    ls_t10_hs-zjgwd = ps_t10-zjgwd.
*****    APPEND  ls_t10_hs TO pt_t10_hs .
*****  ENDLOOP.
******  去重
*****  SORT pt_t10_hs BY zjt_id.
*****  DELETE ADJACENT DUPLICATES FROM pt_t10_hs COMPARING zjt_id.


*  固定金额和固定数量 组织结构表为空
  IF gv_zxybstyp = 'F'  OR gv_zxybstyp = 'Q'.
    CLEAR
          pt_t14.
  ENDIF.

  IF gv_zxybstyp = 'T'.
    CLEAR: pt_t21.
  ENDIF.

*  若商品组没有明细数据则删除抬头数据
***  SORT pt_t20 BY zspz_id.
***  LOOP AT pt_t09 INTO DATA(ls_t09).
***    READ TABLE pt_t20 TRANSPORTING NO FIELDS WITH KEY zspz_id = ls_t09-zspz_id BINARY SEARCH.
***    IF sy-subrc NE 0.
***      DELETE pt_t09.
***      CONTINUE.
***    ENDIF.
***  ENDLOOP.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SAVE_DATA_INTO_DB
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_T06
*&      --> GS_T10
*&      --> GT_T11
*&      --> GT_T07
*&      --> GT_T15
*&      --> GT_T16
*&---------------------------------------------------------------------*
FORM frm_save_data_into_db             USING  pv_flg         TYPE char10
                                               ps_t06         TYPE ty_t06
                                               pt_t06         TYPE tt_t06
                                               pt_t07         TYPE tt_t07
                                               pt_t39         TYPE tt_t39
                                               pt_t08         TYPE tt_t08
                                               pt_t09         TYPE tt_t09
                                               pt_t10         TYPE tt_t10
                                               pt_t10_null    TYPE tt_t10
                                               pt_t11         TYPE tt_t11
                                               pt_t10_hs      TYPE tt_t10
                                               pt_t11_hs      TYPE tt_t11
                                               pt_t10_js      TYPE tt_t10
                                               pt_t11_js      TYPE tt_t11
                                               pt_t10_jsh     TYPE tt_t10
                                               pt_t11_jsh     TYPE tt_t11
                                               pt_t12         TYPE tt_t12
                                               pt_t13         TYPE tt_t13
                                               pt_t38         TYPE tt_t38
                                               pt_t14         TYPE tt_t14
                                               pt_t37         TYPE tt_t37
                                               pt_t15         TYPE tt_t15
                                               pt_t16         TYPE tt_t16
                                               pt_t20         TYPE tt_t20
                                               pt_t21         TYPE tt_t21
                                               pt_t44         TYPE tt_t44
                                        CHANGING
                                               pv_mtype       TYPE bapi_mtype
                                               pv_msg         TYPE bapi_msg.

  DATA:
    lt_db_t06 TYPE TABLE OF zret0006 WITH HEADER LINE,
    lt_db_t07 TYPE TABLE OF zret0007 WITH HEADER LINE,
    lt_db_t39 TYPE TABLE OF zret0039 WITH HEADER LINE,
    lt_db_t08 TYPE TABLE OF zret0008 WITH HEADER LINE,
    lt_db_t09 TYPE TABLE OF zret0009 WITH HEADER LINE,
    lt_db_t10 TYPE TABLE OF zret0010 WITH HEADER LINE,
    lt_db_t11 TYPE TABLE OF zret0011 WITH HEADER LINE,
    lt_db_t12 TYPE TABLE OF zret0012 WITH HEADER LINE,
    lt_db_t13 TYPE TABLE OF zret0013 WITH HEADER LINE,
    lt_db_t38 TYPE TABLE OF zret0038 WITH HEADER LINE,
    lt_db_t14 TYPE TABLE OF zret0014 WITH HEADER LINE,
    lt_db_t37 TYPE TABLE OF zret0037 WITH HEADER LINE,
    lt_db_t15 TYPE TABLE OF zret0015 WITH HEADER LINE,
    lt_db_t16 TYPE TABLE OF zret0016 WITH HEADER LINE,
    lt_db_t20 TYPE TABLE OF zret0020 WITH HEADER LINE,
    lt_db_t21 TYPE TABLE OF zret0021 WITH HEADER LINE,
    lt_db_t44 TYPE TABLE OF zret0044 WITH HEADER LINE.


  DATA:

    lt_db_t07_del TYPE TABLE OF zret0007 WITH HEADER LINE,
    lt_db_t39_del TYPE TABLE OF zret0039 WITH HEADER LINE,
    lt_db_t08_del TYPE TABLE OF zret0008 WITH HEADER LINE,
    lt_db_t11_del TYPE TABLE OF zret0011 WITH HEADER LINE,
    lt_db_t12_del TYPE TABLE OF zret0012 WITH HEADER LINE,
    lt_db_t13_del TYPE TABLE OF zret0013 WITH HEADER LINE,
    lt_db_t38_del TYPE TABLE OF zret0038 WITH HEADER LINE,
    lt_db_t14_del TYPE TABLE OF zret0014 WITH HEADER LINE,
    lt_db_t37_del TYPE TABLE OF zret0037 WITH HEADER LINE,
    lt_db_t15_del TYPE TABLE OF zret0015 WITH HEADER LINE,
    lt_db_t16_del TYPE TABLE OF zret0016 WITH HEADER LINE,
    lt_db_t44_del TYPE TABLE OF zret0044 WITH HEADER LINE,
    lt_db_t21_del TYPE TABLE OF zret0021 WITH HEADER LINE.


  LOOP AT pt_t06 INTO DATA(ls_t06).
    MOVE-CORRESPONDING ls_t06 TO lt_db_t06.
    APPEND lt_db_t06.
  ENDLOOP.

  LOOP AT pt_t07 INTO DATA(ls_t07).
    MOVE-CORRESPONDING ls_t07 TO lt_db_t07.
    APPEND lt_db_t07.
  ENDLOOP.

  LOOP AT pt_t39 INTO DATA(ls_t39).
    MOVE-CORRESPONDING ls_t39 TO lt_db_t39.
    APPEND lt_db_t39.
  ENDLOOP.

  LOOP AT pt_t08 INTO DATA(ls_t08).
    MOVE-CORRESPONDING ls_t08 TO lt_db_t08.
    APPEND lt_db_t08.
  ENDLOOP.

  LOOP AT pt_t09 INTO DATA(ls_t09).
    MOVE-CORRESPONDING ls_t09 TO lt_db_t09.
    APPEND lt_db_t09.
  ENDLOOP.

  LOOP AT pt_t10 INTO DATA(ls_t10).
    MOVE-CORRESPONDING ls_t10 TO lt_db_t10.
    APPEND lt_db_t10.
  ENDLOOP.

  LOOP AT pt_t11 INTO DATA(ls_t11).
    MOVE-CORRESPONDING ls_t11 TO lt_db_t11.
    APPEND lt_db_t11.
  ENDLOOP.

*  将子阶梯数据合并到阶梯表中集中更新
  LOOP AT pt_t10_hs INTO DATA(ls_t10_hs).
    MOVE-CORRESPONDING ls_t10_hs TO lt_db_t10.
    APPEND lt_db_t10.
  ENDLOOP.
  LOOP AT pt_t11_hs INTO DATA(ls_t11_hs).
    MOVE-CORRESPONDING ls_t11_hs TO lt_db_t11.
    APPEND lt_db_t11.
  ENDLOOP.

*  将返利计算子阶梯数据合并到阶梯表中集中更新
  LOOP AT pt_t10_js INTO DATA(ls_t10_js).
    MOVE-CORRESPONDING ls_t10_js TO lt_db_t10.
    APPEND lt_db_t10.
  ENDLOOP.
  LOOP AT pt_t11_js INTO DATA(ls_t11_js).
    MOVE-CORRESPONDING ls_t11_js TO lt_db_t11.
    APPEND lt_db_t11.
  ENDLOOP.

*  将核算周期返利计算子阶梯数据合并到阶梯表中集中更新
  LOOP AT pt_t10_jsh INTO DATA(ls_t10_jsh).
    MOVE-CORRESPONDING ls_t10_jsh TO lt_db_t10.
    APPEND lt_db_t10.
  ENDLOOP.
  LOOP AT pt_t11_jsh INTO DATA(ls_t11_jsh).
    MOVE-CORRESPONDING ls_t11_jsh TO lt_db_t11.
    APPEND lt_db_t11.
  ENDLOOP.

  LOOP AT pt_t10_null INTO DATA(ls_t10_null).
    MOVE-CORRESPONDING ls_t10_null TO lt_db_t10.
    APPEND lt_db_t10.
  ENDLOOP.

*  若没有阶梯号码则将返利计算阶梯号置为空
*  LOOP AT lt_db_t11.
*    READ TABLE lt_db_t11 TRANSPORTING NO FIELDS WITH KEY zjt_id = lt_db_t11-zfljt_id .
*    IF sy-subrc NE 0.
*      CLEAR lt_db_t11-zfljt_id.
*      MODIFY lt_db_t11.
*    ENDIF.
*  ENDLOOP.

  LOOP AT pt_t12 INTO DATA(ls_t12).
    MOVE-CORRESPONDING ls_t12 TO lt_db_t12.
    APPEND lt_db_t12.
  ENDLOOP.

  LOOP AT pt_t13 INTO DATA(ls_t13).
    MOVE-CORRESPONDING ls_t13 TO lt_db_t13.
    APPEND lt_db_t13.
  ENDLOOP.

  LOOP AT pt_t38 INTO DATA(ls_t38).
    MOVE-CORRESPONDING ls_t38 TO lt_db_t38.
    APPEND lt_db_t38.
  ENDLOOP.

  LOOP AT pt_t14 INTO DATA(ls_t14).
    MOVE-CORRESPONDING ls_t14 TO lt_db_t14.
    APPEND lt_db_t14.
  ENDLOOP.

  LOOP AT pt_t37 INTO DATA(ls_t37).
    MOVE-CORRESPONDING ls_t37 TO lt_db_t37.
    APPEND lt_db_t37.
  ENDLOOP.

  LOOP AT pt_t15 INTO DATA(ls_t15).
    MOVE-CORRESPONDING ls_t15 TO lt_db_t15.
    APPEND lt_db_t15.
  ENDLOOP.

  LOOP AT pt_t16 INTO DATA(ls_t16).
    MOVE-CORRESPONDING ls_t16 TO lt_db_t16.
    APPEND lt_db_t16.
  ENDLOOP.

  LOOP AT pt_t20 INTO DATA(ls_t20).
    MOVE-CORRESPONDING ls_t20 TO lt_db_t20.
    APPEND lt_db_t20.
  ENDLOOP.

  LOOP AT pt_t21 INTO DATA(ls_t21).
    MOVE-CORRESPONDING ls_t21 TO lt_db_t21.
    APPEND lt_db_t21.
  ENDLOOP.

  LOOP AT pt_t44 INTO DATA(ls_t44).
    MOVE-CORRESPONDING ls_t44 TO lt_db_t44.
    APPEND lt_db_t44.
  ENDLOOP.

*  获取旧的数据
  SELECT
    *
    INTO TABLE lt_db_t07_del
    FROM zret0007
    WHERE zxy_id = ps_t06-zxy_id.

  SELECT
    *
    INTO TABLE lt_db_t39_del
    FROM zret0039
    WHERE zxy_id = ps_t06-zxy_id.

  SELECT
    *
    INTO TABLE lt_db_t08_del
    FROM zret0008
    WHERE zxy_id = ps_t06-zxy_id.

*  主协议阶梯数据-待删除
  SELECT
    *
    INTO TABLE lt_db_t11_del
    FROM zret0011
    WHERE zjt_id = ps_t06-zjt_id.

*  核算周期子阶梯数据-待删除
  SELECT
    a~*
    APPENDING TABLE @lt_db_t11_del
    FROM zret0011 AS a JOIN zret0015 AS b
                       ON   a~zjt_id = b~zjt_id
                       AND  b~zxy_id = @ps_t06-zxy_id.

*  主阶梯对应返利计算子阶梯数据-待删除
  SELECT
    b~*
    APPENDING TABLE @lt_db_t11_del
    FROM zret0011 AS a JOIN zret0011 AS b
                       ON   a~zfljt_id =  b~zjt_id
                       AND  a~zjt_id = @ps_t06-zjt_id.

*  核算周期对应返利计算子阶梯数据-待删除
  SELECT
    c~*
    APPENDING TABLE @lt_db_t11_del
    FROM zret0011 AS a JOIN zret0015 AS b
                       ON   a~zjt_id = b~zjt_id
                       AND  b~zxy_id = @ps_t06-zxy_id
                       JOIN zret0011 AS c
                       ON   a~zfljt_id =  c~zjt_id.

  SELECT
    *
    INTO TABLE lt_db_t12_del
    FROM zret0012
    WHERE zxy_id = ps_t06-zxy_id.

  SELECT
    *
    INTO TABLE lt_db_t13_del
    FROM zret0013
    WHERE zxy_id = ps_t06-zxy_id.

  SELECT
    *
    INTO TABLE lt_db_t38_del
    FROM zret0038
    WHERE zxy_id = ps_t06-zxy_id.

  SELECT
    *
    INTO TABLE lt_db_t14_del
    FROM zret0014
    WHERE zxy_id = ps_t06-zxy_id.

  SELECT
    *
    INTO TABLE lt_db_t37_del
    FROM zret0037
    WHERE zxy_id = ps_t06-zxy_id.

  SELECT
    *
    INTO TABLE lt_db_t15_del
    FROM zret0015
    WHERE zxy_id = ps_t06-zxy_id.

  SELECT
    *
    INTO TABLE lt_db_t16_del
    FROM zret0016
    WHERE zxy_id = ps_t06-zxy_id.

  SELECT
    *
    INTO TABLE lt_db_t21_del
    FROM zret0021
    WHERE zxy_id = ps_t06-zxy_id.

  SELECT
    *
    INTO TABLE lt_db_t44_del
    FROM zret0044
    WHERE zxy_id = ps_t06-zxy_id.

  IF pv_flg = 'COMT'.
*    perform frm_down_pdf tables
*                                lt_db_t06
*                                lt_db_t07
*                                lt_db_t08
*                                lt_db_t09
*                                lt_db_t10
*                                lt_db_t11
*                                lt_db_t12
*                                lt_db_t13
*                                lt_db_t14
*                                lt_db_t15
*                                lt_db_t16
*                                lt_db_t20
*                                lt_db_t21
*                          changing
*                                pv_mtype
*                                pv_msg.
*    if pv_mtype = 'E'.
*      return.
*    endif.
  ENDIF.


*  商品组单独处理
  IF rb_spz_n NE 'X'.
*    非复制模式 商品组走独立更新流程 ，不用随协议数据一并更新
    CLEAR:
          lt_db_t09,
          lt_db_t09[],
          lt_db_t20,
          lt_db_t20[].
  ENDIF.

  CALL FUNCTION 'ZREFM0002' IN UPDATE TASK
    TABLES
      it_t06     = lt_db_t06[]
      it_t07     = lt_db_t07[]
      it_t39     = lt_db_t39[]
      it_t08     = lt_db_t08[]
      it_t09     = lt_db_t09[]
      it_t10     = lt_db_t10[]
      it_t11     = lt_db_t11[]
      it_t12     = lt_db_t12[]
      it_t13     = lt_db_t13[]
      it_t38     = lt_db_t38[]
      it_t14     = lt_db_t14[]
      it_t37     = lt_db_t37[]
      it_t15     = lt_db_t15[]
      it_t16     = lt_db_t16[]
      it_t20     = lt_db_t20[]
      it_t21     = lt_db_t21[]
      it_t44     = lt_db_t44[]
      it_t07_del = lt_db_t07_del[]
      it_t39_del = lt_db_t39_del[]
      it_t08_del = lt_db_t08_del[]
      it_t11_del = lt_db_t11_del[]
      it_t12_del = lt_db_t12_del[]
      it_t13_del = lt_db_t13_del[]
      it_t38_del = lt_db_t38_del[]
      it_t14_del = lt_db_t14_del[]
      it_t37_del = lt_db_t37_del[]
      it_t15_del = lt_db_t15_del[]
      it_t16_del = lt_db_t16_del[]
      it_t21_del = lt_db_t21_del[]
      it_t44_del = lt_db_t44_del[].

  IF sy-subrc EQ 0.
    pv_mtype = 'S'.
    IF pv_flg = 'RLES_C' OR pv_flg = 'CANCEL'.
      PERFORM frm_process_cancel TABLES lt_db_t06.
    ENDIF.
  ELSE.
    pv_mtype = 'E'.
  ENDIF.
  COMMIT WORK AND WAIT.
ENDFORM.

FORM frm_process_cancel TABLES pt_db_t06 STRUCTURE zret0006.

  CALL FUNCTION 'ZREFM0019'
*   IMPORTING
*     EV_MTYPE       =
*     EV_MSG         =
    TABLES
      it_head = pt_db_t06[].

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_PRO_DATA_14
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GT_BUKRS
*&      --> GT_DCWRK
*&      --> GT_WERKS
*&      --> GT_EKORG
*&      <-- GT_T14
*&---------------------------------------------------------------------*
FORM frm_zzzlx_fold  USING    pt_bukrs TYPE tt_bukrs
                               pt_dcwrk TYPE tt_dcwrk
                               pt_werks TYPE tt_werks
                               pt_ekorg TYPE tt_ekorg
                               pv_flg   TYPE char1
                      CHANGING pt_t14   TYPE tt_t14.

  DATA:
        ls_t14 TYPE LINE OF tt_t14.

  CLEAR pt_t14.
  CLEAR ls_t14.

*  删除无效的数据
  DELETE pt_bukrs WHERE bukrs IS INITIAL.
  DELETE pt_dcwrk WHERE dcwrk IS INITIAL.
  DELETE pt_werks WHERE werks IS INITIAL.
  DELETE pt_ekorg WHERE ekorg IS INITIAL.

  IF gv_zxybstyp = 'T'.
    CLEAR pt_ekorg.
  ENDIF.

  LOOP AT pt_ekorg INTO DATA(ls_ekorg).
    CLEAR:
          ls_t14.
    ls_t14-zzzlx = 'P'.
    ls_t14-zzzid = ls_ekorg-ekorg.
    ls_t14-zzzpc = ls_ekorg-exclude.
    APPEND ls_t14 TO pt_t14.
  ENDLOOP.


  IF pt_ekorg[] IS INITIAL .
    CLEAR:
          ls_t14.
    ls_t14-zzzlx = 'P'.
    ls_t14-zzzid = 'ALL'.
    APPEND ls_t14 TO pt_t14.
  ENDIF.

*  三者为空时 表不做处理
  IF pv_flg = 'B'.
    IF pt_bukrs[] IS INITIAL AND pt_dcwrk[] IS INITIAL AND pt_werks[] IS INITIAL.
      RETURN.
    ENDIF.
  ENDIF.


  LOOP AT pt_bukrs INTO DATA(ls_bukrs).
    CLEAR:
          ls_t14.
    ls_t14-zzzlx = 'A'.
    ls_t14-zzzid = ls_bukrs-bukrs.
    ls_t14-zzzpc = ls_bukrs-exclude.
    APPEND ls_t14 TO pt_t14.
  ENDLOOP.

  LOOP AT pt_dcwrk INTO DATA(ls_dcwrk).
    CLEAR:
          ls_t14.
    ls_t14-zzzlx = 'C'.
    ls_t14-zzzid = ls_dcwrk-dcwrk.
    ls_t14-zzzpc = ls_dcwrk-exclude.
    APPEND ls_t14 TO pt_t14.
  ENDLOOP.


  LOOP AT pt_werks INTO DATA(ls_werks).
    CLEAR:
          ls_t14.
    ls_t14-zzzlx = 'S'.
    ls_t14-zzzid = ls_werks-werks.
    ls_t14-zzzpc = ls_werks-exclude.
    APPEND ls_t14 TO pt_t14.
  ENDLOOP.

  IF pv_flg = 'A'.

    IF pt_werks[] IS INITIAL .
      CLEAR:
            ls_t14.
      ls_t14-zzzlx = 'S'.
      ls_t14-zzzid = 'ALL'.

      APPEND ls_t14 TO pt_t14.
    ENDIF.
  ENDIF.

  IF pv_flg = 'B'.
    IF pt_bukrs[] IS INITIAL .
      CLEAR:
            ls_t14.
      ls_t14-zzzlx = 'A'.
      ls_t14-zzzid = 'ALL'.

      APPEND ls_t14 TO pt_t14.
    ENDIF.

    IF pt_dcwrk[] IS INITIAL .
      CLEAR:
            ls_t14.
      ls_t14-zzzlx = 'C'.
      ls_t14-zzzid = 'ALL'.

      APPEND ls_t14 TO pt_t14.
    ENDIF.

    IF pt_werks[] IS INITIAL .
      CLEAR:
            ls_t14.
      ls_t14-zzzlx = 'S'.
      ls_t14-zzzid = 'ALL'.

      APPEND ls_t14 TO pt_t14.
    ENDIF.

  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_ZZZLX_UNFOLD
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> PT_T14
*&      <-- PT_BUKRS
*&      <-- PT_DCWRK
*&      <-- PT_WERKS
*&      <-- PT_EKORG
*&---------------------------------------------------------------------*
FORM frm_zzzlx_unfold  USING    pt_t14   TYPE tt_t14
                       CHANGING pt_bukrs TYPE tt_bukrs
                                pt_dcwrk TYPE tt_dcwrk
                                pt_werks TYPE tt_werks
                                pt_ekorg TYPE tt_ekorg.

  DATA:
    ls_bukrs TYPE LINE OF tt_bukrs,
    ls_dcwrk TYPE LINE OF tt_dcwrk,
    ls_werks TYPE LINE OF tt_werks,
    ls_ekorg TYPE LINE OF tt_ekorg.


  CLEAR:
        pt_bukrs ,
        pt_dcwrk ,
        pt_werks ,
        pt_ekorg .

  LOOP AT pt_t14 INTO DATA(ls_t14).
    CLEAR:
          ls_bukrs  ,
          ls_dcwrk  ,
          ls_werks  ,
          ls_ekorg  .

    ls_bukrs-exclude = ls_t14-zzzpc.
    ls_dcwrk-exclude = ls_t14-zzzpc.
    ls_werks-exclude = ls_t14-zzzpc.
    ls_ekorg-exclude = ls_t14-zzzpc.


    CASE ls_t14-zzzlx.
      WHEN 'A'.
        ls_bukrs-bukrs = ls_t14-zzzid.
        APPEND ls_bukrs TO pt_bukrs.
      WHEN 'P'.
        ls_ekorg-ekorg = ls_t14-zzzid.
        APPEND ls_ekorg TO pt_ekorg.
      WHEN 'C'.
        ls_dcwrk-dcwrk = ls_t14-zzzid.
        APPEND ls_dcwrk TO pt_dcwrk.
      WHEN 'S'.
        ls_werks-werks = ls_t14-zzzid.
        APPEND ls_werks TO pt_werks.
      WHEN OTHERS.
    ENDCASE.

  ENDLOOP.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CODE_PRO
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- LV_CODE
*&---------------------------------------------------------------------*
FORM frm_code_pro  CHANGING ok_code TYPE sy-ucomm
                            pv_code TYPE sy-ucomm.
  CLEAR pv_code.
  pv_code = ok_code.
  CLEAR ok_code.
  CLEAR:
        sy-ucomm.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_SCREEN_9100
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*& -->  P1        TEXT
*& <--  P2        TEXT
*&---------------------------------------------------------------------*
FORM frm_set_screen_9100 .
  DATA:
        lv_flg_err TYPE char1.

  IF rb_dis = 'X'.
    LOOP AT SCREEN.
      IF screen-group1 = '11' OR
*          screen-group1 = '12' OR
          screen-group1 = '13' OR
          screen-group1 = '14' .
        screen-active = 0.
      ELSEIF screen-group1 = '15' OR  screen-group1 = '12' .
        screen-active = 1.
      ENDIF.

*      作废按钮权限检查 若失败则隐藏
      CLEAR lv_flg_err.
      PERFORM frm_author_check_cancel USING gs_t06
                                           '02'
                                     CHANGING lv_flg_err.
      IF lv_flg_err = 'E'.
        IF screen-group1 = '12'.
          screen-active = 0.
        ENDIF.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
  ELSEIF rb_add = 'X'.
    LOOP AT SCREEN.
      IF  screen-group1 = '12' OR
          screen-group1 = '13' OR
          screen-group1 = '14' OR
          screen-group1 = '15' .
        screen-active = 0.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
  ELSEIF rb_edit = 'X'.
    LOOP AT SCREEN.
      IF  screen-group1 = '13' OR
          screen-group1 = '12' OR
          screen-group1 = '14' OR
          screen-group1 = '15'.
        screen-active = 0.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
  ELSEIF rb_rles = 'X'.
    LOOP AT SCREEN.
      IF  screen-group1 = '11' OR
          screen-group1 = '12' OR
          screen-group1 = '15'.
        screen-active = 0.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.

  CASE gv_flg_comm.
    WHEN 'COMT'.
      LOOP AT SCREEN.
        IF  screen-group1 = '11' OR screen-group1 = '12'.
          screen-input = 0.
        ENDIF.
        MODIFY SCREEN.
      ENDLOOP.
    WHEN 'RLES'.
      LOOP AT SCREEN.
        IF  screen-group1 = '13' .
          screen-input = 0.
        ELSEIF screen-group1 = '14' .
          screen-input = 1.
        ENDIF.
        MODIFY SCREEN.
      ENDLOOP.
    WHEN 'RLES_C'.
      LOOP AT SCREEN.
        IF  screen-group1 = '14' .
          screen-input = 0.
        ELSEIF screen-group1 = '13' .
          screen-input = 1.
        ENDIF.
        MODIFY SCREEN.
      ENDLOOP.
    WHEN 'CANCEL'.
      LOOP AT SCREEN.
        IF  screen-group1 = '11' OR
             screen-group1 = '12' OR
             screen-group1 = '13' OR
             screen-group1 = '14' .
          screen-input = 0.
        ENDIF.
        MODIFY SCREEN.
      ENDLOOP.

    WHEN 'BT_RLES'.
    WHEN 'BT_RLES'.
    WHEN OTHERS.
  ENDCASE.

  IF gv_zxybstyp = 'T'.
    LOOP AT SCREEN.
      IF  screen-group3 = '330' .
        screen-active = 0.
        MODIFY SCREEN.
      ENDIF.
    ENDLOOP.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_SCREEN_ALL
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*& -->  P1        TEXT
*& <--  P2        TEXT
*&---------------------------------------------------------------------*
FORM frm_set_screen_all .

*  返利与任务拆分
  LOOP AT SCREEN.
    IF screen-group3 = '301'.
      IF gs_t06-zsplit = ''.
        screen-active = 0.
      ELSE.
        screen-active = 1.
      ENDIF.
      MODIFY SCREEN.
    ENDIF.
  ENDLOOP.

  LOOP AT SCREEN.
    IF screen-group3 = '310'.
      IF gs_t10-zjsff = 'T' .
        screen-active = 0.
      ELSE.
        screen-active = 1.
      ENDIF.
    ELSEIF screen-group3 = '320'.
      IF gs_t10_js-zjsff = 'T' .
        screen-active = 0.
      ELSE.
        screen-active = 1.
      ENDIF.

    ELSEIF screen-group3 = '330'.
      IF gv_zxybstyp = 'T'.
        screen-active = 0.
      ELSE.
        screen-active = 1.
      ENDIF.

    ENDIF.

*    IF screen-group4 = '410'.
*      IF gs_t10-zjtlx = 'R' AND ( gs_t10-zjsff = 'P' OR gs_t10-zjsff = 'R' ).
*        screen-active = 1.
*      ELSE.
*        screen-active = 0.
*      ENDIF.
*    ELSEIF screen-group4 = '420'.
*      IF gs_t10_js-zjtlx = 'R' AND ( gs_t10_js-zjsff = 'P' or gs_t10_js-zjsff = 'R' ).
*        screen-active = 1.
*      ELSE.
*        screen-active = 0.
*      ENDIF.
*    ENDIF.
    MODIFY SCREEN.
  ENDLOOP.

  IF rb_dis = 'X' OR
     rb_rles = 'X' OR
    gv_flg_comm = 'COMT'.
    LOOP AT SCREEN.
      IF screen-group1 = 'EDT' .
        screen-input = 0.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.

  ENDIF.


ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_DATA_9132
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- GS_T07
*&---------------------------------------------------------------------*
FORM frm_set_data_9132  USING   pt_t09 TYPE tt_t09
                        CHANGING ps_t07 TYPE ty_t07.

  ps_t07-matnr   = |{ ps_t07-matnr ALPHA = IN WIDTH = 18 }| .

  SELECT SINGLE
    maktx
    INTO ps_t07-maktx
    FROM makt
    WHERE matnr = ps_t07-matnr.

  SELECT SINGLE
    meins
    INTO ps_t07-meins
    FROM mara
    WHERE matnr = ps_t07-matnr.

  SELECT SINGLE
    zspzid_txt
    INTO ps_t07-zspzid_txt
    FROM zret0009
    WHERE zspz_id = ps_t07-zspz_id.
  IF sy-subrc EQ 0.
    READ TABLE pt_t09 INTO DATA(ls_t09) WITH KEY zspz_id = ps_t07-zspz_id.
    IF sy-subrc EQ 0.
      ps_t07-zspzid_txt = ls_t09-zspzid_txt.
    ENDIF.
  ENDIF.

*  商品维度为S时价格倍数默认为1
  IF ps_t07-zpeinh IS INITIAL AND ps_t07-zspwd = 'S'.
    ps_t07-zpeinh = 1.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_SCREEN_9110
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*& -->  P1        TEXT
*& <--  P2        TEXT
*&---------------------------------------------------------------------*
FORM frm_set_screen_9110 .
  PERFORM frm_set_screen_all.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_SCREEN_9121
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*& -->  P1        TEXT
*& <--  P2        TEXT
*&---------------------------------------------------------------------*
FORM frm_set_screen_9121 .
  PERFORM frm_set_screen_all.

  LOOP AT SCREEN.

    CASE gv_zxybstyp.
      WHEN 'F'.

        IF screen-group2 = '12' .
          screen-active = 0.
        ENDIF.

      WHEN 'Q'.

        IF screen-group2 = '12' .
          screen-active = 0.
        ENDIF.

      WHEN 'A'.

        IF screen-group2 = '16' OR
            screen-group2 = '12' .
          screen-active = 0.
        ENDIF.

      WHEN OTHERS.
    ENDCASE.

    IF screen-group2 = '20'.
      IF gv_zxybstyp = 'V' OR gv_zxybstyp = 'T'.
        screen-active = 1.
      ELSE.
        screen-active = 0.

      ENDIF.

    ENDIF.

    MODIFY SCREEN.
  ENDLOOP.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_SCREEN_9122
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*& -->  P1        TEXT
*& <--  P2        TEXT
*&---------------------------------------------------------------------*
FORM frm_set_screen_9122 .
  PERFORM frm_set_screen_all.

  LOOP AT SCREEN.

    CASE gv_zxybstyp.
      WHEN 'F'.

        IF screen-group2 = '13' OR
          screen-group2 = '14' .
          screen-active = 0.
        ENDIF.

      WHEN 'Q'.

        IF screen-group2 = '13' OR
          screen-group2 = '14' .
          screen-active = 0.
        ENDIF.

      WHEN 'A'.

        IF screen-group2 = '14' .
          screen-active = 0.
        ENDIF.

*      WHEN 'T'.
*
*        IF screen-group3 = '330' .
*          screen-active = 0.
*        ENDIF.

      WHEN OTHERS.
    ENDCASE.

    MODIFY SCREEN.
  ENDLOOP.


ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_SCREEN_9123
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*& -->  P1        TEXT
*& <--  P2        TEXT
*&---------------------------------------------------------------------*
FORM frm_set_screen_9123 .

  LOOP AT SCREEN.

    CASE gv_zxybstyp.
      WHEN 'F'.

        IF screen-group2 = '15'.
          screen-active = 0.
        ENDIF.

      WHEN 'Q'.

        IF screen-group2 = '15' .
          screen-active = 0.
        ENDIF.

      WHEN OTHERS.
    ENDCASE.

    MODIFY SCREEN.
  ENDLOOP.

  PERFORM frm_set_screen_all.


ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_SCREEN_9124
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*& -->  P1        TEXT
*& <--  P2        TEXT
*&---------------------------------------------------------------------*
FORM frm_set_screen_9124 .
  PERFORM frm_set_screen_all.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_SCREEN_9131
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*& -->  P1        TEXT
*& <--  P2        TEXT
*&---------------------------------------------------------------------*
FORM frm_set_screen_9131 .


  CASE p_zfllx.
    WHEN 'RB05'.
      LOOP AT SCREEN.
        IF screen-group2 = '18' .
          screen-active = 0.
        ENDIF.
        MODIFY SCREEN.
      ENDLOOP.
    WHEN OTHERS.
  ENDCASE.

  LOOP AT SCREEN.
    CASE gv_zxybstyp.
      WHEN 'A'.
        IF screen-group2 = '19' .
          screen-input = 0.
        ENDIF.
    ENDCASE.
    MODIFY SCREEN.
  ENDLOOP.


  IF rb_dis = 'X' OR
     rb_rles = 'X' OR
    gv_flg_comm = 'COMT'.
    LOOP AT SCREEN.
      IF screen-group1 = 'EDT' .
        screen-input = 0.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.

  PERFORM frm_set_screen_all.

***
***  LOOP AT SCREEN.
***    CASE gv_zxybstyp.
***      WHEN 'F'.
***
***        IF screen-group2 = '12' OR
***          screen-group2 = '13' OR
***          screen-group2 = '14' OR
***          screen-group2 = '15'.
***          screen-active = 0.
***        ENDIF.
***
***      WHEN 'Q'.
***
***        IF screen-group2 = '12' OR
***          screen-group2 = '13' OR
***          screen-group2 = '14' OR
***          screen-group2 = '15' .
***          screen-active = 0.
***        ENDIF.
***
***      WHEN 'A'.
***
***        IF screen-group2 = '18' OR
***            screen-group2 = '16' OR
***            screen-group2 = '12' OR
***            screen-group2 = '14' .
***          screen-active = 0.
***        ENDIF.
***
***      WHEN OTHERS.
***    ENDCASE.
***
***    MODIFY SCREEN.
***  ENDLOOP.
***
***  CASE p_zfllx.
***    WHEN 'RB05'.
***      LOOP AT SCREEN.
***        IF screen-group2 = '19' .
***          screen-input = 0.
***        ENDIF.
***        MODIFY SCREEN.
***      ENDLOOP.
***    WHEN OTHERS.
***  ENDCASE.


ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_DATA_ZSPZ
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- LS_T07
*&      <-- GT_T09
*&      <-- GT_T20
*&      <-- GT_T20_SUB
*&---------------------------------------------------------------------*
FORM frm_pro_data_zspz               USING       ps_t07      TYPE ty_t07
                                                 pt_t09_all   TYPE tt_t09
                                                 pt_t20_all   TYPE tt_t20
                                      CHANGING    ps_t09      TYPE ty_t09
                                                  pt_t20_sub  TYPE tt_t20.

  CLEAR:
        ps_t09,
        pt_t20_sub.



  IF ps_t07-zspz_id IS INITIAL.
    IF rb_add = 'X' OR rb_edit = 'X'.
*     获取商品组编号
      PERFORM frm_get_zspz_id         CHANGING   ps_t09-zspz_id.
    ENDIF.
  ELSE.
*    获取商品组明细
    IF rb_spz_n = 'X'.
*      从内表中取
      PERFORM frm_get_data_spz_n        USING      ps_t07-zspz_id
                                                   pt_t20_all
                                                   pt_t09_all
                                        CHANGING   ps_t09
                                                   pt_t20_sub.

    ELSE.
*      从数据库中取
      PERFORM frm_get_data_spz          USING      ps_t07-zspz_id
                                        CHANGING   ps_t09
                                                   pt_t20_sub.

    ENDIF.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_BT_DTL_COMMAND
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*& -->  P1        TEXT
*& <--  P2        TEXT
*&---------------------------------------------------------------------*
FORM frm_bt_dtl_spz_command USING   pv_dynnr TYPE sy-dynnr
                                     pv_tc    TYPE dynfnam
                                     pv_cursor_line TYPE i
                            CHANGING pt_t07 TYPE tt_t07
                                     pt_t39 TYPE tt_t39
                                     ps_t09 TYPE ty_t09
                                     pt_t09 TYPE tt_t09
                                     pt_t20 TYPE tt_t20
                                     pt_t20_sub TYPE tt_t20.


  DATA:
        lv_index_line TYPE i.
  DATA:
    ls_t09_data TYPE LINE OF tt_t09,
    ls_t20_data TYPE LINE OF tt_t20.

  PERFORM frm_get_index_line                    USING         pv_tc
                                                              pv_cursor_line
                                                CHANGING      lv_index_line.

  IF pv_tc = 'TC_ITEM_D'.

    READ TABLE pt_t07 INTO DATA(ls_t07) INDEX lv_index_line.
    IF sy-subrc EQ 0.

*    将协议屏幕数据传递给商品组屏幕
      PERFORM frm_pro_data_zspz                   USING         ls_t07
                                                                pt_t09
                                                                pt_t20
                                                  CHANGING      ps_t09
                                                                pt_t20_sub.

*    锁定商品组编号
      IF rb_add = 'X' OR rb_edit = 'X'.
        PERFORM frm_pro_lock_zspz_id               USING        ps_t09-zspz_id
                                                                 ''.
      ENDIF.

      CALL SCREEN 9913.


*    解锁商品组编号
      IF rb_add = 'X' OR rb_edit = 'X'.
        PERFORM frm_pro_lock_zspz_id               USING        ps_t09-zspz_id
                                                                 'X'.
      ENDIF.

*    将商品组屏幕数据传递给协议屏幕
      IF ps_t09 IS NOT INITIAL.
        ls_t07-zspz_id  = ps_t09-zspz_id.
      ENDIF.

*    商品组更新 走独立的9913 更新进程，跟主表更新不在一块
*********   若是新增的商品，则将数据更新到主表
********    IF GV_FLG_SPZ_EXIST = ''.
********      LS_T07-ZSPZID_TXT = GS_T09-ZSPZID_TXT.
********      APPEND GS_T09 TO GT_T09.
********      LOOP AT GT_T20_SUB INTO DATA(LS_T20_SUB).
********        LS_T20_SUB-ZSPZ_ID = LS_T07-ZSPZ_ID.
********        APPEND LS_T20_SUB TO GT_T20.
********      ENDLOOP.
********    ENDIF.

      MODIFY pt_t07 FROM ls_t07 INDEX lv_index_line.
    ENDIF.
  ELSEIF pv_tc = 'TC_ITEM_D_N'.

    READ TABLE pt_t39 INTO DATA(ls_t39) INDEX lv_index_line.
    IF sy-subrc EQ 0.

*    将协议屏幕数据传递给商品组屏幕
      PERFORM frm_pro_data_zspz                   USING         ls_t39
                                                                pt_t09
                                                                pt_t20
                                                  CHANGING      ps_t09
                                                                pt_t20_sub.

*    锁定商品组编号
      IF rb_add = 'X' OR rb_edit = 'X'.
        PERFORM frm_pro_lock_zspz_id               USING        ps_t09-zspz_id
                                                                 ''.
      ENDIF.

      CALL SCREEN 9913.


*    解锁商品组编号
      IF rb_add = 'X' OR rb_edit = 'X'.
        PERFORM frm_pro_lock_zspz_id               USING        ps_t09-zspz_id
                                                                 'X'.
      ENDIF.

*    将商品组屏幕数据传递给协议屏幕
      IF ps_t09 IS NOT INITIAL.
        ls_t39-zspz_id  = ps_t09-zspz_id.
      ENDIF.

*    商品组更新 走独立的9913 更新进程，跟主表更新不在一块
*********   若是新增的商品，则将数据更新到主表
********    IF GV_FLG_SPZ_EXIST = ''.
********      LS_T07-ZSPZID_TXT = GS_T09-ZSPZID_TXT.
********      APPEND GS_T09 TO GT_T09.
********      LOOP AT GT_T20_SUB INTO DATA(LS_T20_SUB).
********        LS_T20_SUB-ZSPZ_ID = LS_T07-ZSPZ_ID.
********        APPEND LS_T20_SUB TO GT_T20.
********      ENDLOOP.
********    ENDIF.

      MODIFY pt_t39 FROM ls_t39 INDEX lv_index_line.
    ENDIF.
  ENDIF.


ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_DATA_9131
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_T10
*&      <-- GS_T11
*&      <-- GV_ZJT_FZ_T
*&      <-- GV_ZJT_FM_T
*&---------------------------------------------------------------------*
FORM frm_set_style_zjt  USING    ps_t10       TYPE ty_t10
                                 pv_fname     TYPE char20
                                 pv_tc        TYPE dynfnam
                        CHANGING pt_t11       TYPE tt_t11
                                 pv_zjt_fz_t  TYPE char10
                                 pv_zjt_fm_t  TYPE char10.

  DATA: lth_cols TYPE cx_tableview_column.
  DATA:
        ls_t11 TYPE LINE OF tt_t11.
  DATA:
    lv_fname  TYPE char50,
    lv_fname2 TYPE char50,
    lv_fname3 TYPE char50.

  DATA:lv_table  LIKE feld-name,
       lv_fname1 TYPE char20.
  FIELD-SYMBOLS <lt_table> TYPE STANDARD TABLE.
  FIELD-SYMBOLS <wa>.
  FIELD-SYMBOLS <wa_value>.


  lv_fname = pv_fname && '-ZJT_FM'.
  CONDENSE lv_fname.
  lv_fname1 = 'INVISIBLE'.

  lv_table = pv_tc && '-COLS'.
  ASSIGN (lv_table) TO <lt_table>.

  CLEAR:
        pv_zjt_fz_t,
        pv_zjt_fm_t.

*  先统一全部展示分子分母
***  LOOP AT tc_zjt-cols INTO lth_cols.
****    IF lth_cols-screen-name = 'GS_T11-ZJT_FM'.
***    IF lth_cols-screen-name = lv_fname.
***      lth_cols-invisible = 0.
***      MODIFY tc_zjt-cols FROM lth_cols.
***    ENDIF.
***  ENDLOOP.

  LOOP AT <lt_table> ASSIGNING <wa>.
    CLEAR:
          lth_cols.
    lth_cols = <wa>.

    IF lth_cols-screen-name = lv_fname.
      UNASSIGN <wa_value>.
      ASSIGN COMPONENT lv_fname1 OF STRUCTURE <wa> TO <wa_value>.
      <wa_value> = 0.
    ENDIF.

  ENDLOOP.

  IF ps_t10-zjsff NE 'T'.


    lv_fname2 = pv_fname && '-ZJT_FZ'.
    CONDENSE lv_fname2.

    IF pv_fname = 'GS_T11' .
      lv_fname3 = 'BT_DTL_JS'.
    ELSEIF pv_fname = 'GS_T11_HS_SUB'.
      lv_fname3 = 'BT_DTL_JSH'.
    ENDIF.

    LOOP AT <lt_table> ASSIGNING <wa>.
      CLEAR:
            lth_cols.
      lth_cols = <wa>.
      IF lth_cols-screen-name = 'GV_ICON' OR lth_cols-screen-name = lv_fname3.
        UNASSIGN <wa_value>.
        ASSIGN COMPONENT lv_fname1 OF STRUCTURE <wa> TO <wa_value>.
        <wa_value> = 1.
      ENDIF.

      IF lth_cols-screen-name = lv_fname2.
        UNASSIGN <wa_value>.
        ASSIGN COMPONENT lv_fname1 OF STRUCTURE <wa> TO <wa_value>.
        <wa_value> = 0.
      ENDIF.

    ENDLOOP.
  ENDIF.




  IF ps_t10-zflxs = 'M' AND ps_t10-zjsff = 'F'.
***    LOOP AT tc_zjt-cols INTO lth_cols.
****      IF lth_cols-screen-name = 'GS_T11-ZJT_FM'.
***      IF lth_cols-screen-name = lv_fname.
***        lth_cols-invisible = 1.
***        MODIFY tc_zjt-cols FROM lth_cols.
***      ENDIF.
***    ENDLOOP.

    LOOP AT <lt_table> ASSIGNING <wa>.
      CLEAR:
            lth_cols.
      lth_cols = <wa>.

      IF lth_cols-screen-name = lv_fname.
        UNASSIGN <wa_value>.
        ASSIGN COMPONENT lv_fname1 OF STRUCTURE <wa> TO <wa_value>.
        <wa_value> = 1.
      ENDIF.
    ENDLOOP.

    gv_zjt_fz_t = '固定金额'.

*    LOOP AT pt_t11 INTO ls_t11.
*      CLEAR ls_t11-zjt_fm.
*      MODIFY pt_t11 FROM ls_t11.
*    ENDLOOP.
  ELSEIF ps_t10-zflxs = 'M' AND ps_t10-zjsff = 'S'.
    pv_zjt_fz_t = '单价'.
    pv_zjt_fm_t = '价格数量'.
*    LOOP AT pt_t11 INTO ls_t11.
*      ls_t11-zjt_fm = 1.
*      MODIFY pt_t11 FROM ls_t11.
*    ENDLOOP.
  ELSEIF ps_t10-zflxs = 'M' AND ps_t10-zjsff = 'P'.
    pv_zjt_fz_t = '比例'.
    pv_zjt_fm_t = '%'.
*    LOOP AT pt_t11 INTO ls_t11.
*      ls_t11-zjt_fm = 100.
*      MODIFY pt_t11 FROM ls_t11.
*    ENDLOOP.
  ELSEIF ps_t10-zflxs = 'Q' AND ps_t10-zjsff = 'F'.
    pv_zjt_fz_t = '固定数量'.

**    LOOP AT tc_zjt-cols INTO lth_cols.
***      IF lth_cols-screen-name = 'GS_T11-ZJT_FM'.
**      IF lth_cols-screen-name = lv_fname.
**        lth_cols-invisible = 1.
**        MODIFY tc_zjt-cols FROM lth_cols.
**      ENDIF.
**    ENDLOOP.

    LOOP AT <lt_table> ASSIGNING <wa>.
      CLEAR:
            lth_cols.
      lth_cols = <wa>.

      IF lth_cols-screen-name = lv_fname.
        UNASSIGN <wa_value>.
        ASSIGN COMPONENT lv_fname1 OF STRUCTURE <wa> TO <wa_value>.
        <wa_value> = 1.
      ENDIF.

    ENDLOOP.
*    LOOP AT pt_t11 INTO ls_t11.
*      CLEAR ls_t11-zjt_fm.
*      MODIFY pt_t11 FROM ls_t11.
*    ENDLOOP.
  ELSEIF ps_t10-zflxs = 'Q' AND ps_t10-zjsff = 'P'.
    pv_zjt_fz_t = '返量'.
    pv_zjt_fm_t = '每'.
  ENDIF.

*  阶梯含税变灰
  IF ps_t10-zjgwd = 'A'.
    ps_t10-zjths = 'X'.
    LOOP AT SCREEN.
      IF screen-name = 'GS_T10-ZJTHS'.
        screen-input = '0'.
        MODIFY SCREEN.
      ENDIF.
    ENDLOOP.
  ENDIF.

  IF ps_t10-zfljgwd = 'A'.
    ps_t10-zflhs = 'X'.
    LOOP AT SCREEN.
      IF screen-name = 'GS_T10-ZFLHS'.
        screen-input = '0'.
        MODIFY SCREEN.
      ENDIF.
    ENDLOOP.
  ENDIF.

  IF pv_fname = 'GS_T11' AND cb_split = '' .
    LOOP AT tc_zjt-cols INTO lth_cols.
*      IF lth_cols-screen-name = 'GS_T11-ZJT_FM'.
      IF lth_cols-screen-name = 'GV_ICON' OR lth_cols-screen-name = 'BT_DTL_JS'.
*        OR lth_cols-screen-name =  'GS_T11-ZJT_FM'.
        lth_cols-invisible = 1.
        MODIFY tc_zjt-cols FROM lth_cols.
      ENDIF.
    ENDLOOP.
  ENDIF.

  IF pv_fname = 'GS_T11_HS_SUB' AND cb_split = ''.
    LOOP AT tc_zjt-cols INTO lth_cols.
*      IF lth_cols-screen-name = 'GS_T11-ZJT_FM'.
      IF lth_cols-screen-name = 'GV_ICON' OR lth_cols-screen-name = 'BT_DTL_JSH'.
*        OR lth_cols-screen-name =  'GS_T11-ZJT_FM'.
        lth_cols-invisible = 1.
        MODIFY tc_zjt-cols FROM lth_cols.
      ENDIF.
    ENDLOOP.
  ENDIF.

  IF ps_t10-zjsff = 'T'.

    lv_fname = pv_fname && '-ZJT_FM'.
    CONDENSE lv_fname.

    lv_fname2 = pv_fname && '-ZJT_FZ'.
    CONDENSE lv_fname2.

    IF pv_fname = 'GS_T11' .
      lv_fname3 = 'BT_DTL_JS'.
    ELSEIF pv_fname = 'GS_T11_HS_SUB'.
      lv_fname3 = 'BT_DTL_JSH'.
    ENDIF.

    LOOP AT <lt_table> ASSIGNING <wa>.
      CLEAR:
            lth_cols.
      lth_cols = <wa>.
      IF lth_cols-screen-name = lv_fname OR lth_cols-screen-name = lv_fname2.
        UNASSIGN <wa_value>.
        ASSIGN COMPONENT lv_fname1 OF STRUCTURE <wa> TO <wa_value>.
        <wa_value> = 1.
      ENDIF.

      IF lth_cols-screen-name = 'GV_ICON' OR lth_cols-screen-name = lv_fname3.
        UNASSIGN <wa_value>.
        ASSIGN COMPONENT lv_fname1 OF STRUCTURE <wa> TO <wa_value>.
        <wa_value> = 0.
      ENDIF.


    ENDLOOP.

  ENDIF.

  IF ps_t10-zjsff = 'R'.

    lv_fname = pv_fname && '-ZJT_FM'.
    CONDENSE lv_fname.

    lv_fname2 = pv_fname && '-ZJT_FZ'.
    CONDENSE lv_fname2.

    LOOP AT <lt_table> ASSIGNING <wa>.
      CLEAR:
            lth_cols.
      lth_cols = <wa>.
      IF lth_cols-screen-name = lv_fname OR lth_cols-screen-name = lv_fname2.
        UNASSIGN <wa_value>.
        ASSIGN COMPONENT lv_fname1 OF STRUCTURE <wa> TO <wa_value>.
        <wa_value> = 1.
      ENDIF.

    ENDLOOP.

  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_BT_DTL_ZJT_COMMAND
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*& -->  P1        TEXT
*& <--  P2        TEXT
*&---------------------------------------------------------------------*
****FORM frm_bt_dtl_zjt_command .
****
****  DATA:
****        lv_index_line TYPE i.
****  DATA:
****        ls_t11_data TYPE LINE OF tt_t11.
****
*****  检查阶梯抬头 返利形式和计算方法 不能为空 否则子阶梯不允许维护
****  IF gs_t10-zjsff NE 'T'.
****    IF gs_t10-zflxs IS INITIAL OR gs_t10-zjsff IS INITIAL.
****      MESSAGE s888(sabapdocu) WITH '阶梯【返利形式】和【计算方法】不能为空' DISPLAY LIKE 'E'.
****      RETURN.
****    ENDIF.
****  ENDIF.
****
****
****  PERFORM frm_get_index_line USING      'TC_ZHSQJ'
****                                        gv_cursor_line
****                             CHANGING   lv_index_line.
****
****  READ TABLE gt_t15 INTO DATA(ls_t15) INDEX lv_index_line.
****  IF sy-subrc EQ 0.
****
*****    从全部子阶梯数据中分离出当前行对应的子阶梯数据
****    PERFORM frm_pro_data_hszjt CHANGING   ls_t15
****                                          gt_t11_hs
****                                          gt_t11_hs_sub.
****
****    CALL SCREEN 9914.
****
****
****
*****  阶梯数据处理 防止因未回车导致的错误
****    PERFORM frm_set_data_zjt  USING    gs_t10
****                              CHANGING gt_t11_hs_sub.
****
*****   更新子阶梯ID
****    CLEAR ls_t11_data.
****    ls_t11_data-zjt_id = ls_t15-zjt_id.
****    MODIFY gt_t11_hs_sub FROM ls_t11_data TRANSPORTING zjt_id WHERE zjt_id IS INITIAL.
****
*****    将子阶梯数据合并到阶梯表中 后面一起更新到数据库中
****    APPEND LINES OF gt_t11_hs_sub TO gt_t11_hs.
****    MODIFY gt_t15 FROM ls_t15 INDEX lv_index_line.
****  ENDIF.
****ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_GET_SEL_LINE
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> P_
*&      <-- LV_SEL_LINE
*&---------------------------------------------------------------------*
FORM frm_get_index_line  USING    p_tc_name      TYPE dynfnam
                                   pv_cursor_line TYPE i
                          CHANGING pv_index       TYPE i.

  DATA:
        lv_field_name TYPE char30.
  FIELD-SYMBOLS:
                 <fs_any> TYPE any.

  CLEAR pv_index.

  lv_field_name = p_tc_name && '-TOP_LINE'.

  ASSIGN (lv_field_name) TO <fs_any>.

  pv_index = <fs_any> + pv_cursor_line - 1.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_PRO_DATA_HSZJT
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- LV_SEL_LINE
*&      <-- GT_T10_HS
*&      <-- GT_T10_HS_SUB
*&      <-- GT_T11_HS
*&      <-- GT_T11_HS_SUB
*&---------------------------------------------------------------------*
FORM frm_pro_data_hszjt  CHANGING ps_t15        TYPE ty_t15
                                  pt_t11_hs     TYPE tt_t11
                                  pt_t11_hs_sub TYPE tt_t11.

  CLEAR:
        pt_t11_hs_sub.

  IF ps_t15-zjt_id IS INITIAL.
    IF rb_add = 'X' OR rb_edit = 'X'.
*     获取阶梯编号
      PERFORM frm_get_zjt_id CHANGING ps_t15-zjt_id.
    ENDIF.
  ELSE.
*    从全部数据中获取当前行对应的子阶梯数据，同时从中删除
    pt_t11_hs_sub[] = pt_t11_hs[].
    DELETE pt_t11_hs_sub WHERE zjt_id NE ps_t15-zjt_id.
    DELETE pt_t11_hs     WHERE zjt_id = ps_t15-zjt_id.
  ENDIF.


ENDFORM.
FORM frm_pro_data_jszjt  CHANGING ps_t11        TYPE ty_t11
                                  pt_t11_hs     TYPE tt_t11
                                  pt_t11_hs_sub TYPE tt_t11.

  CLEAR:
        pt_t11_hs_sub.

  IF ps_t11-zfljt_id IS INITIAL.
    IF rb_add = 'X' OR rb_edit = 'X'.
*     获取阶梯编号
      PERFORM frm_get_zjt_id CHANGING ps_t11-zfljt_id.
    ENDIF.
  ELSE.
*    从全部数据中获取当前行对应的子阶梯数据，同时从中删除
    pt_t11_hs_sub[] = pt_t11_hs[].
    DELETE pt_t11_hs_sub WHERE zjt_id NE ps_t11-zfljt_id.
    DELETE pt_t11_hs     WHERE zjt_id = ps_t11-zfljt_id.
  ENDIF.


ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_SCREEN_9221
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*& -->  P1        TEXT
*& <--  P2        TEXT
*&---------------------------------------------------------------------*
FORM frm_set_screen_9221 .
  PERFORM frm_set_screen_all.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_LIST_BOX
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*& -->  P1        TEXT
*& <--  P2        TEXT
*&---------------------------------------------------------------------*
FORM frm_set_list_box USING   pv_id     TYPE  vrm_id
                              pt_values TYPE vrm_values.

  CALL FUNCTION 'VRM_SET_VALUES'
    EXPORTING
      id              = pv_id
      values          = pt_values[]
    EXCEPTIONS
      id_illegal_name = 1
      OTHERS          = 2.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_INIT_DATA
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*& -->  P1        TEXT
*& <--  P2        TEXT
*&---------------------------------------------------------------------*
FORM frm_init_data .

  CLEAR:
    gt_t06        ,      gs_t06        ,      gt_bukrs ,      ok_code           ,
    gt_t07        ,      gs_t07        ,      gs_bukrs ,      lv_code           ,
    gt_t08        ,      gs_t08        ,      gt_dcwrk ,      gv_zjt_fz_t       ,
    gt_t09        ,      gs_t09        ,      gs_dcwrk ,      gv_zjt_fm_t       ,
    gt_t10        ,      gs_t10        ,      gt_werks ,      gv_zxybstyp       ,
    gt_t10_hs     ,      gs_t11        ,      gs_werks ,      gv_cursor_line    ,
    gt_t10_hs_sub ,      gs_t10_hs     ,      gt_ekorg ,      gv_index_line     ,
    gt_t11        ,      gs_t10_hs_sub ,      gs_ekorg ,      gv_flg_comm       ,
    gt_t11_hs     ,      gs_t11_hs     ,                      gv_flg_spz_exist  ,
    gt_t11_hs_sub ,      gs_t11_hs_sub ,                      gv_title          ,
    gt_t12        ,      gs_t12        ,                      gv_title_01       ,
    gt_t13        ,      gs_t13        ,                      gv_title_02       ,
    gt_t14        ,      gs_t14        ,
    gt_t15        ,      gs_t15        ,
    gt_t16        ,      gs_t16        ,
    gt_t20        ,      gs_t20        ,
    gt_t21        ,
    gt_t20_sub    ,      gs_t20_sub    .

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_PRO_DATA_COMMAND
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> PV_FLG
*&      <-- GS_T06
*&---------------------------------------------------------------------*
FORM frm_pro_data_command  USING    pv_flg TYPE char10
                           CHANGING  ps_t06 TYPE ty_t06
                                     ps_t09 TYPE ty_t09.

  IF rb_add = 'X'.
    ps_t06-zcjrq  = sy-datum.
    ps_t06-zcjsj  = sy-uzeit.
    ps_t06-zcjr   = sy-uname.

    ps_t09-zcjrq  = sy-datum.
    ps_t09-zcjsj  = sy-uzeit.
    ps_t09-zcjr   = sy-uname.
  ELSEIF rb_edit = 'X'.
    ps_t06-zxgrq  = sy-datum.
    ps_t06-zxgsj  = sy-uzeit.
    ps_t06-zxgr   = sy-uname.

    ps_t09-zxgrq  = sy-datum.
    ps_t09-zxgsj  = sy-uzeit.
    ps_t09-zxgr   = sy-uname.
  ENDIF.


  CASE pv_flg.
    WHEN 'COMT'.
      ps_t06-zxyzt  = 'P'.
    WHEN 'RLES'.
      ps_t06-zxyzt  = 'A'.
      ps_t06-zsprq  = sy-datum.
      ps_t06-zspsj  = sy-uzeit.
      ps_t06-zspr   = sy-uname.

    WHEN 'RLES_C'.
      ps_t06-zxyzt  = 'R'.
      ps_t06-zxgrq  = sy-datum.
      ps_t06-zxgsj  = sy-uzeit.
      ps_t06-zxgr   = sy-uname.

    WHEN 'CANCEL'.
      ps_t06-zxyzt  = 'D'.
      ps_t06-zqxrq  = sy-datum.
      ps_t06-zqxsj  = sy-uzeit.
      ps_t06-zqxr   = sy-uname.
    WHEN OTHERS.
  ENDCASE.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_BEFORE_COMT
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- GV_FLG_ERR
*&      <-- GV_MSG
*&---------------------------------------------------------------------*
FORM frm_check_before_comt  USING      ps_t06     TYPE ty_t06
                                       ps_t10     TYPE ty_t10
                                       ps_t10_js  TYPE ty_t10
                                       pt_t11     TYPE tt_t11
                                       pt_t11_hs  TYPE tt_t11
                                       pt_t11_js  TYPE tt_t11
                                       pt_t11_jsh TYPE tt_t11
                                       pt_t07     TYPE tt_t07
                                       pt_t39     TYPE tt_t07
                                       pt_t20     TYPE tt_t20
                                       pt_t08     TYPE tt_t08
                                       pt_t13     TYPE tt_t13
                                       pt_t38     TYPE tt_t38
                                       pt_bukrs   TYPE tt_bukrs
                                       pt_dcwrk   TYPE tt_dcwrk
                                       pt_werks   TYPE tt_werks
                                       pt_ekorg   TYPE tt_ekorg
                                       pt_bukrs_n TYPE tt_bukrs
                                       pt_dcwrk_n TYPE tt_dcwrk
                                       pt_werks_n TYPE tt_werks
                                       pt_ekorg_n TYPE tt_ekorg
                                       pt_t12     TYPE tt_t12
                                       pt_t21     TYPE tt_t21
                             CHANGING  pv_flg_err TYPE char1.

  PERFORM frm_data_check_main USING     ps_t06
                                        ps_t10
                                        ps_t10_js
                                        pt_t11
                                        pt_t11_hs
                                        pt_t11_js
                                        pt_t11_jsh
                                        pt_t07
                                        pt_t39
                                        pt_t20
                                        pt_t13
                                        pt_t38
                                        pt_bukrs
                                        pt_dcwrk
                                        pt_werks
                                        pt_ekorg
                                        pt_bukrs_n
                                        pt_dcwrk_n
                                        pt_werks_n
                                        pt_ekorg_n
                                        pt_t12
                                        pt_t21
                               CHANGING pv_flg_err.

  PERFORM frm_data_check_comt USING     ps_t06
                                        pt_t07
                                        pt_t08
                               CHANGING pv_flg_err.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_ZCJ
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_T06_ZCJ
*&---------------------------------------------------------------------*
FORM frm_check_zcj  USING    pv_zcj TYPE zret0006-zcj.

  IF pv_zcj IS INITIAL OR pv_zcj = 'ALL'.
    RETURN.
  ENDIF.
  SELECT SINGLE zcj    FROM zmmt0039    WHERE zcj = @pv_zcj
    INTO @DATA(lv_zcj).
  IF sy-subrc NE 0.
    MESSAGE e888(sabapdocu) WITH '生产商不存在!'.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_LIFNR
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_T12_LIFNR
*&---------------------------------------------------------------------*
FORM frm_check_lifnr  USING    pv_lifnr TYPE lfa1-lifnr.

  IF pv_lifnr IS INITIAL OR pv_lifnr = 'ALL'.
    RETURN.
  ENDIF.
  pv_lifnr   = |{ pv_lifnr ALPHA = IN  }| .
  SELECT SINGLE lifnr    FROM lfa1    WHERE lifnr = @pv_lifnr
    INTO @DATA(lv_lifnr).
  IF sy-subrc NE 0.
    MESSAGE e888(sabapdocu) WITH '供应商不存在!'.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_BUKRS
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- GS_BUKRS_BUKRS
*&---------------------------------------------------------------------*
FORM frm_check_bukrs  CHANGING pv_bukrs.

  IF pv_bukrs IS INITIAL OR pv_bukrs = 'ALL'.
    RETURN.
  ENDIF.
  SELECT SINGLE bukrs    FROM t001    WHERE bukrs = @pv_bukrs
    INTO @DATA(lv_bukrs).
  IF sy-subrc NE 0.
    MESSAGE e888(sabapdocu) WITH '公司代码不存在！'.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_DCWRK
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_DCWRK
*&      --> GT_BUKRS
*&---------------------------------------------------------------------*
FORM frm_check_dcwrk  USING    ps_dcwrk TYPE ty_dcwrk
                               pt_bukrs TYPE tt_bukrs.

  IF ps_dcwrk-dcwrk IS INITIAL OR ps_dcwrk-dcwrk = 'ALL'.
    RETURN.
  ENDIF.
  SELECT SINGLE bwkey    FROM t001k    WHERE bwkey = @ps_dcwrk-dcwrk
    INTO @DATA(lv_bwkey).
  IF sy-subrc NE 0.
    MESSAGE e888(sabapdocu) WITH 'DC代码不存在！'.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_WERKS
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_WERKS
*&      --> GT_BUKRS
*&      --> GT_DCWRK
*&---------------------------------------------------------------------*
FORM frm_check_werks  USING    ps_werks TYPE ty_werks
                               pt_bukrs TYPE tt_bukrs
                               pt_dcwrk TYPE tt_dcwrk.

  IF ps_werks-werks IS INITIAL OR ps_werks-werks = 'ALL'.
    RETURN.
  ENDIF.
  SELECT SINGLE bwkey    FROM t001k    WHERE bwkey = @ps_werks-werks
    INTO @DATA(lv_bwkey).
  IF sy-subrc NE 0.
    MESSAGE e888(sabapdocu) WITH '门店不存在！'.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_EKORG
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_EKORG_EKORG
*&---------------------------------------------------------------------*
FORM frm_check_ekorg  USING    pv_ekorg TYPE t024e-ekorg.

  IF pv_ekorg IS INITIAL OR pv_ekorg = 'ALL'.
    RETURN.
  ENDIF.
  SELECT SINGLE ekorg    FROM t024e    WHERE ekorg = @pv_ekorg
    INTO @DATA(lv_ekorg).
  IF sy-subrc NE 0.
    MESSAGE e888(sabapdocu) WITH '采购组织不存在！'.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_MATNR
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_T07
*&---------------------------------------------------------------------*
FORM frm_check_matnr  USING    pv_matnr TYPE mara-matnr.

  IF  pv_matnr IS NOT INITIAL.
    pv_matnr   = |{ pv_matnr ALPHA = IN WIDTH = 18 }| .
    SELECT SINGLE matnr    FROM mara    WHERE matnr = @pv_matnr
      INTO @DATA(lv_matnr).
    IF sy-subrc NE 0.
      MESSAGE e888(sabapdocu) WITH '商品号码不存在！'.
    ENDIF.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_ZFLXS
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_T10_ZFLXS
*&      --> GS_T06_ZDFFS
*&---------------------------------------------------------------------*
FORM frm_check_zflxs  USING   ps_t10 TYPE ty_t10
                              pv_flg TYPE char1
                      CHANGING pv_flg_err TYPE char1
                               pt_msglist TYPE scp1_general_errors .

  DATA:

    ls_msglist TYPE scp1_general_error.


  IF ps_t10-zflxs = 'Q'.
    IF ps_t10-zjsff EQ 'S'.
      IF pv_flg = 'X'.
        pv_flg_err = 'E'.
        ls_msglist-msgv1 = '返利形式为数量时，计算方法不能为单价!'.
        APPEND ls_msglist TO pt_msglist.
        CLEAR ls_msglist.
      ELSE.
        MESSAGE e888(sabapdocu) WITH '返利形式为数量时，计算方法不能为单价!'.
      ENDIF.
    ENDIF.
  ENDIF.

  IF ps_t10-zlhwd = 'Q' AND
     ps_t10-zflxs = 'M' AND
     ps_t10-zjtlx = 'S' .
    IF ps_t10-zjsff EQ 'P'.
      IF pv_flg = 'X'.
        pv_flg_err = 'E'.
        ls_msglist-msgv1 = '量化维度为数量，返利形式为金额，阶梯类型为增量时计算方法不能是比例!'.
        APPEND ls_msglist TO pt_msglist.
        CLEAR ls_msglist.
      ELSE.
        MESSAGE e888(sabapdocu) WITH '量化维度为数量，返利形式为金额，阶梯类型为增量时计算方法不能是比例!'.
      ENDIF.
    ENDIF.
  ENDIF.

  IF ps_t10-zlhwd = 'M' AND
     ps_t10-zflxs = 'Q' AND
     ps_t10-zjtlx = 'S' .
    IF ps_t10-zjsff NE 'F'.
      IF pv_flg = 'X'.
        pv_flg_err = 'E'.
        ls_msglist-msgv1 = '量化维度为金额，返利形式为数量，阶梯类型为增量时计算方法只能是固定!'.
        APPEND ls_msglist TO pt_msglist.
        CLEAR ls_msglist.
      ELSE.
        MESSAGE e888(sabapdocu) WITH '量化维度为金额，返利形式为数量，阶梯类型为增量时计算方法只能是固定!'.
      ENDIF.
    ENDIF.
  ENDIF.

  IF ps_t10-zlhwd = 'M' AND
     ps_t10-zflxs = 'M' AND
     ps_t10-zjtlx = 'S' .
    IF ps_t10-zjsff EQ 'S'.
      IF pv_flg = 'X'.
        pv_flg_err = 'E'.
        ls_msglist-msgv1 = '量化维度为金额，返利形式为金额，阶梯类型为增量时计算方法不能是单价!'.
        APPEND ls_msglist TO pt_msglist.
        CLEAR ls_msglist.
      ELSE.
        MESSAGE e888(sabapdocu) WITH '量化维度为金额，返利形式为金额，阶梯类型为增量时计算方法不能是单价!'.
      ENDIF.
    ENDIF.
  ENDIF.
  IF ps_t10-zjsff = 'R' .
    IF ps_t10-zjgwd =  'A'.
      IF pv_flg = 'X'.
        pv_flg_err = 'E'.
        ls_msglist-msgv1 = '计算方法为补差时，任务价格维度不能为核算价！!'.
        APPEND ls_msglist TO pt_msglist.
        CLEAR ls_msglist.
      ELSE.
        MESSAGE e888(sabapdocu) WITH '计算方法为补差时，任务价格维度不能为核算价！'.
      ENDIF.
    ENDIF.

    IF ps_t10-zfljgwd NE   'A'.
      IF pv_flg = 'X'.
        pv_flg_err = 'E'.
        ls_msglist-msgv1 = '计算方法为补差时,返利价格维度只能为核算价！!'.
        APPEND ls_msglist TO pt_msglist.
        CLEAR ls_msglist.
      ELSE.
        MESSAGE e888(sabapdocu) WITH '计算方法为补差时,返利价格维度只能为核算价！'.
      ENDIF.
    ENDIF.
  ENDIF.

  IF ps_t10-zjtlx = 'S' AND ( ps_t10-zjsff = 'P' OR ps_t10-zjsff = 'R' ).
    IF ps_t10-zjgwd NE ps_t10-zfljgwd.
      IF pv_flg = 'X'.
        pv_flg_err = 'E'.
        ls_msglist-msgv1 = '当阶梯类型=“S-增量”且计算方法=“P-比例”或“R-补差”时，返利价格维度和价格维度必须一致!'.
        APPEND ls_msglist TO pt_msglist.
        CLEAR ls_msglist.
      ELSE.
        MESSAGE e888(sabapdocu) WITH '当阶梯类型=“S-增量”且计算方法=“P-比例”或“R-补差”时，返利价格维度和价格维度必须一致!'.
      ENDIF.
    ENDIF.
  ENDIF.

  IF gv_zxybstyp = 'V' OR gv_zxybstyp = 'T'.
    IF ps_t10-zflxs = 'M' OR ps_t10-zlhwd = 'M'.
      IF ps_t10-zjgwd = ''.
        IF pv_flg = 'X'.
          pv_flg_err = 'E'.
          ls_msglist-msgv1 = '当量化纬度或返利形式为金额时，价格纬度不能为空!'.
          APPEND ls_msglist TO pt_msglist.
          CLEAR ls_msglist.
        ELSE.
          MESSAGE e888(sabapdocu) WITH '当量化纬度或返利形式为金额时，价格纬度不能为空!'.
        ENDIF.
      ENDIF.
    ENDIF.

  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_ZHSQJ
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_T06
*&      <-- GT_T15
*&---------------------------------------------------------------------*
FORM frm_set_zqj  USING         ps_t06 TYPE ty_t06
                                pv_flg  TYPE char10
                    CHANGING    pt_data TYPE tt_zqj.
  DATA:
        ls_data TYPE LINE OF tt_zqj.
  DATA:
    lv_date    TYPE d,
    lv_add_mon TYPE t5a4a-dlymo.
  DATA:
        lv_zqs TYPE int4.

  CLEAR pt_data.
  IF pv_flg = 'ZHSQJ'.
    IF ps_t06-zhszq IS INITIAL.
      RETURN.
    ENDIF.

    SELECT SINGLE zhszqs INTO lv_zqs
      FROM zret0005
      WHERE zhszq = ps_t06-zhszq.

  ELSEIF pv_flg = 'ZJSQJ'.
    IF ps_t06-zjszq IS INITIAL.
      RETURN.
    ENDIF.

    SELECT SINGLE zjszqs INTO lv_zqs
      FROM zret0004
      WHERE zjszq = ps_t06-zjszq.
  ENDIF.


*  初始日期等于协议开始日期
  lv_date = ps_t06-zbegin.
  lv_add_mon = lv_zqs.

  IF ps_t06-zhstype = 'B' OR pv_flg = 'ZJSQJ'.

    DO  .

      CLEAR ls_data.
      ls_data-zbegin = lv_date.

      CALL FUNCTION 'RP_CALC_DATE_IN_INTERVAL'
        EXPORTING
          date      = lv_date
          days      = 0
          months    = lv_add_mon
*         SIGNUM    = U_SIGN
          years     = 0
        IMPORTING
          calc_date = ls_data-zend.

      ls_data-zend = ls_data-zend - 1.

*      若首次循环 核算期间结束日期大于等于协议结束日期，则停止
      IF ls_data-zend >= ps_t06-zend.
        ls_data-zend = ps_t06-zend.
        APPEND ls_data TO pt_data.
        EXIT.
      ENDIF.

*      预判断下次循环 核算期间结束日期是否大于协议结束日期 ，
*      若大于 则不进行下次循环 更改日期为协议结束日期
      lv_date = ls_data-zend + 1.

      CALL FUNCTION 'RP_CALC_DATE_IN_INTERVAL'
        EXPORTING
          date      = lv_date
          days      = 0
          months    = lv_add_mon
*         SIGNUM    = U_SIGN
          years     = 0
        IMPORTING
          calc_date = lv_date.
      lv_date = lv_date - 1.

      IF lv_date > ps_t06-zend.
        ls_data-zend = ps_t06-zend.
        APPEND ls_data TO pt_data.
        EXIT.
      ELSE.
        APPEND ls_data TO pt_data.
*        下一次循环的开始日期等于本次循环结束日期 + 1
        lv_date = ls_data-zend + 1 .
      ENDIF.

*      防止死循环 跳出
      IF sy-index > 1000.
        EXIT.
      ENDIF.
    ENDDO.


  ELSE.

    DO  .

      CLEAR ls_data.
      ls_data-zbegin = ps_t06-zbegin.

      CALL FUNCTION 'RP_CALC_DATE_IN_INTERVAL'
        EXPORTING
          date      = ls_data-zbegin
          days      = 0
          months    = lv_add_mon
*         SIGNUM    = 0
          years     = 0
        IMPORTING
          calc_date = ls_data-zend.
      ls_data-zend = ls_data-zend - 1.

*      若首次循环 核算期间结束日期大于等于协议结束日期，则停止
      IF ls_data-zend >= ps_t06-zend.
        ls_data-zend = ps_t06-zend.
        APPEND ls_data TO pt_data.
        EXIT.
      ENDIF.

*      预判断下次循环 核算期间结束日期是否大于协议结束日期 ，
*      若大于 则不进行下次循环 更改日期为协议结束日期
      lv_add_mon = lv_add_mon + lv_zqs.

      CLEAR:
            lv_date.
      CALL FUNCTION 'RP_CALC_DATE_IN_INTERVAL'
        EXPORTING
          date      = ls_data-zbegin
          days      = 0
          months    = lv_add_mon
*         SIGNUM    = U_SIGN
          years     = 0
        IMPORTING
          calc_date = lv_date.

      lv_date = lv_date - 1.

      IF lv_date > ps_t06-zend.
        ls_data-zend = ps_t06-zend.
        APPEND ls_data TO pt_data.
        EXIT.
      ELSE.
        APPEND ls_data TO pt_data..
      ENDIF.

*      防止死循环 跳出
      IF sy-index > 1000.
        EXIT.
      ENDIF.
    ENDDO.



  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_ZHSQJ
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_T06
*&      <-- GT_T15
*&      <-- GT_T10
*&      <-- GT_T11
*&---------------------------------------------------------------------*
FORM frm_set_zhsqj  USING    ps_t06  TYPE ty_t06
                    CHANGING pt_t15  TYPE tt_t15
                             pt_t10  TYPE tt_t10
                             pt_t11  TYPE tt_t11.
  DATA:
        lt_zqj TYPE tt_zqj.

*  清空核算期间时 需清空子阶梯数据
  LOOP AT pt_t15 INTO DATA(ls_t15) WHERE zjt_id IS NOT INITIAL .
    DELETE pt_t10 WHERE zjt_id = ls_t15-zjt_id.
    DELETE pt_t11 WHERE zjt_id = ls_t15-zjt_id.
  ENDLOOP.
  IF sy-subrc EQ 0.
    MESSAGE s888(sabapdocu) WITH '请重新维护核算期间子阶梯！' DISPLAY LIKE 'I'.
  ENDIF.

  CLEAR pt_t15.

*  核算期间拆分
  IF gv_zxybstyp NE 'T'.
    PERFORM frm_set_zqj USING ps_t06
                                'ZHSQJ'
                          CHANGING lt_zqj.

    LOOP AT lt_zqj INTO DATA(ls_zqj).
      CLEAR ls_t15.
      ls_t15-zbegin = ls_zqj-zbegin.
      ls_t15-zend = ls_zqj-zend.
      APPEND ls_t15 TO pt_t15.
    ENDLOOP.

  ELSE.

    CLEAR ls_t15.
    ls_t15-zbegin = ps_t06-zbegin.
    ls_t15-zend = ps_t06-zend.
    APPEND ls_t15 TO pt_t15.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_ZJSQJ
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_T06
*&      <-- GT_T16
*&---------------------------------------------------------------------*
FORM frm_set_zjsqj  USING    ps_t06 TYPE ty_t06
                    CHANGING pt_t16 TYPE tt_t16.

  DATA:
        lt_zqj TYPE tt_zqj.
  DATA:
        ls_t16 TYPE LINE OF tt_t16.

  CLEAR pt_t16.

*  结算期间拆分
*  IF gv_zxybstyp NE 'T'.
  PERFORM frm_set_zqj   USING gs_t06
                              'ZJSQJ'
                        CHANGING lt_zqj.

  LOOP AT lt_zqj INTO DATA(ls_zqj).
    CLEAR ls_t16.
    ls_t16-zbegin = ls_zqj-zbegin.
    ls_t16-zend = ls_zqj-zend.
    APPEND ls_t16 TO pt_t16.
  ENDLOOP.
*  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_DATA_ZJT
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- GT_T11
*&---------------------------------------------------------------------*
FORM frm_set_data_zjt     USING   ps_t10  TYPE ty_t10
                          CHANGING pt_t11 TYPE tt_t11.
  DATA:
        lv_zjt_by TYPE zret0011-zjt_by.
  IF pt_t11[] IS INITIAL.
    RETURN.
  ENDIF.

*  倒序排列
  SORT pt_t11 BY zjt_from DESCENDING.

*    首次时设置为最大值
  lv_zjt_by = gc_max_dec.
  LOOP AT pt_t11 INTO DATA(ls_t11).
*    每行记录的【至】 = 上次行的【从】 - 1.
    ls_t11-zjt_by = lv_zjt_by.
*    记录【从】 - 1 的值
    lv_zjt_by = ls_t11-zjt_from - 1.
    IF lv_zjt_by < 0.
      lv_zjt_by = 0.
    ENDIF.
    ls_t11-zflg_1st = ''.
*    IF ls_t11-zjt_from IS INITIAL AND ls_t11-zjt_by IS NOT INITIAL.
*      ls_t11-zflg_del = 'X'.
*    ENDIF.
    MODIFY pt_t11 FROM ls_t11.
  ENDLOOP.
  SORT pt_t11 BY zjt_from .

*  更新首行标志
  IF pt_t11[] IS NOT INITIAL.
    ls_t11-zjt_from = 0.
    ls_t11-zflg_1st = 'X'.
    MODIFY pt_t11 FROM ls_t11 INDEX 1 TRANSPORTING zflg_1st zjt_from.
  ENDIF.

*  按照类型更新分子和分母
  IF ps_t10-zflxs = 'M' AND ps_t10-zjsff = 'F'.
    LOOP AT pt_t11 INTO ls_t11.
      CLEAR ls_t11-zjt_fm.
      MODIFY pt_t11 FROM ls_t11.
    ENDLOOP.
  ELSEIF ps_t10-zflxs = 'M' AND ps_t10-zjsff = 'S'.

    LOOP AT pt_t11 INTO ls_t11.
      ls_t11-zjt_fm = 1.
      MODIFY pt_t11 FROM ls_t11.
    ENDLOOP.
  ELSEIF ps_t10-zflxs = 'M' AND ps_t10-zjsff = 'P'.

    LOOP AT pt_t11 INTO ls_t11.
      ls_t11-zjt_fm = 100.
      MODIFY pt_t11 FROM ls_t11.
    ENDLOOP.
  ELSEIF ps_t10-zflxs = 'Q' AND ps_t10-zjsff = 'F'.

    LOOP AT pt_t11 INTO ls_t11.
      CLEAR ls_t11-zjt_fm.
      MODIFY pt_t11 FROM ls_t11.
    ENDLOOP.
  ENDIF.

*  DELETE pt_t11 WHERE ZFLG_DEL = 'X' AND zflg_1st = ''.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_ZJT_FROM
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_T11
*&      --> GT_T11
*&---------------------------------------------------------------------*
FORM frm_check_zjt_from  USING    ps_t11 TYPE ty_t11
                                  pt_t11 TYPE tt_t11
                                  pv_index TYPE i.
*  IF ps_t11-zjt_from IS NOT INITIAL.
  DATA(lt_tmp) = pt_t11[].
  READ TABLE lt_tmp TRANSPORTING NO FIELDS INDEX pv_index.
  IF sy-subrc EQ 0.
    DELETE lt_tmp INDEX pv_index.
  ENDIF.
  LOOP AT lt_tmp INTO DATA(ls_tmp) WHERE zjt_from = ps_t11-zjt_from.
    IF ls_tmp-mandt IS NOT INITIAL.
      MESSAGE e888(sabapdocu) WITH '起始值存在相同的记录！'.
    ENDIF.
  ENDLOOP.
*  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_DATA_9911
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- GS_T12
*&---------------------------------------------------------------------*
FORM frm_set_data_9911  CHANGING ps_t12 TYPE ty_t12.

  ps_t12-lifnr   = |{ ps_t12-lifnr ALPHA = IN  }| .
  SELECT SINGLE name1
    FROM lfa1
    WHERE lifnr = @ps_t12-lifnr
    INTO @ps_t12-name1.

  IF ps_t12-lifnr = 'ALL'.
    ps_t12-name1 = '全部'.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_DATA_9012
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- GS_T13
*&---------------------------------------------------------------------*
FORM frm_set_data_9012  CHANGING ps_t13 TYPE ty_t13.

  ps_t13-zghf   = |{ ps_t13-zghf ALPHA = IN  }| .
  SELECT SINGLE name1
    FROM lfa1
    WHERE lifnr = @ps_t13-zghf
    INTO @ps_t13-name1.

  IF ps_t13-zghf = 'ALL'.
    ps_t13-name1 = '全部'.
  ENDIF.
ENDFORM.
FORM frm_set_data_9922  CHANGING ps_t44 TYPE ty_t44.

  ps_t44-zflzff   = |{ ps_t44-zflzff ALPHA = IN  }| .
  SELECT SINGLE name1
    FROM lfa1
    WHERE lifnr = @ps_t44-zflzff
    INTO @ps_t44-name1.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_DATA_9121
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GT_T12
*&      --> GT_T13
*&      <-- GS_T06
*&---------------------------------------------------------------------*
FORM frm_set_data_9121  USING    pt_t12 TYPE tt_t12
                                 pt_t13 TYPE tt_t13
                                 pt_t38 TYPE tt_t38
                                 pt_t21 TYPE tt_t21
                        CHANGING ps_t06 TYPE ty_t06.
  CLEAR:
        ps_t06-lifnr_t,
        ps_t06-zghf_t,
        ps_t06-zcj_t,
        ps_t06-zghf_t_n.

  LOOP AT pt_t12 INTO DATA(ls_t12).
    ps_t06-lifnr_t = ls_t12-lifnr && ',' && ps_t06-lifnr_t.
  ENDLOOP.
  LOOP AT pt_t21 INTO DATA(ls_t21).
    ps_t06-zcj_t = ls_t21-zcj && ',' && ps_t06-zcj_t.
  ENDLOOP.
  LOOP AT pt_t13 INTO DATA(ls_t13).
    IF ls_t13-zzzpc = 'X'.
      ps_t06-zghf_t =  '-' && ls_t13-zghf && ','  && ps_t06-zghf_t.
    ELSE.
      ps_t06-zghf_t = ls_t13-zghf && ',' && ps_t06-zghf_t.
    ENDIF.
  ENDLOOP.

  LOOP AT pt_t38 INTO DATA(ls_t38).
    IF ls_t38-zzzpc = 'X'.
      ps_t06-zghf_t_n =  '-' && ls_t38-zghf && ','  && ps_t06-zghf_t_n.
    ELSE.
      ps_t06-zghf_t_n = ls_t38-zghf && ',' && ps_t06-zghf_t_n.
    ENDIF.
  ENDLOOP.


  SELECT SINGLE name1 INTO ps_t06-zflzff_t FROM lfa1
    WHERE lifnr = ps_t06-zflzff.
  SELECT SINGLE butxt INTO ps_t06-zflsqf_t FROM t001
    WHERE bukrs = ps_t06-zflsqf.
  SELECT SINGLE butxt INTO ps_t06-zbukrs_t FROM t001
    WHERE bukrs = ps_t06-zbukrs.
  SELECT SINGLE eknam INTO ps_t06-ekgrp_t FROM t024
    WHERE ekgrp = ps_t06-ekgrp.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_DATA_9122
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GT_LIFNR
*&      --> GT_DCWRK
*&      --> GT_WERKS
*&      --> GT_EKORG
*&      <-- PS_T06
*&---------------------------------------------------------------------*
FORM frm_set_data_9122  USING    pt_bukrs TYPE tt_bukrs
                                 pt_dcwrk TYPE tt_dcwrk
                                 pt_werks TYPE tt_werks
                                 pt_ekorg TYPE tt_ekorg
                                 pt_bukrs_n TYPE tt_bukrs
                                 pt_dcwrk_n TYPE tt_dcwrk
                                 pt_werks_n TYPE tt_werks
                                 pt_ekorg_n TYPE tt_ekorg
                        CHANGING ps_t06 TYPE ty_t06.

  CLEAR:
        ps_t06-bukrs_t,
        ps_t06-dcwrk_t,
        ps_t06-werks_t,
        ps_t06-ekorg_t,
        ps_t06-bukrs_t_n,
        ps_t06-dcwrk_t_n,
        ps_t06-werks_t_n,
        ps_t06-ekorg_t_n.

  LOOP AT pt_bukrs INTO DATA(ls_bukrs).
    ps_t06-bukrs_t = ls_bukrs-bukrs && ',' && ps_t06-bukrs_t.

  ENDLOOP.

  LOOP AT pt_dcwrk INTO DATA(ls_dcwrk).
    IF ls_dcwrk-exclude = 'X'.
      ps_t06-dcwrk_t =  '-' && ls_dcwrk-dcwrk && ','  && ps_t06-dcwrk_t.
    ELSE.
      ps_t06-dcwrk_t = ls_dcwrk-dcwrk && ',' && ps_t06-dcwrk_t.
    ENDIF.
  ENDLOOP.

  LOOP AT pt_werks INTO DATA(ls_werks).
    IF ls_werks-exclude = 'X'.
      ps_t06-werks_t =   '-' && ls_werks-werks && ',' && ps_t06-werks_t.
    ELSE.
      ps_t06-werks_t = ls_werks-werks && ',' && ps_t06-werks_t.
    ENDIF.
  ENDLOOP.

  LOOP AT pt_ekorg INTO DATA(ls_ekorg).
    ps_t06-ekorg_t = ls_ekorg-ekorg && ',' && ps_t06-ekorg_t.
  ENDLOOP.


  LOOP AT pt_bukrs_n INTO DATA(ls_bukrs_n).
    ps_t06-bukrs_t_n = ls_bukrs_n-bukrs && ',' && ps_t06-bukrs_t_n.

  ENDLOOP.

  LOOP AT pt_dcwrk_n INTO DATA(ls_dcwrk_n).
    IF ls_dcwrk_n-exclude = 'X'.
      ps_t06-dcwrk_t_n =  '-' && ls_dcwrk_n-dcwrk && ','  && ps_t06-dcwrk_t_n.
    ELSE.
      ps_t06-dcwrk_t_n = ls_dcwrk_n-dcwrk && ',' && ps_t06-dcwrk_t_n.
    ENDIF.
  ENDLOOP.

  LOOP AT pt_werks_n INTO DATA(ls_werks_n).
    IF ls_werks_n-exclude = 'X'.
      ps_t06-werks_t_n =   '-' && ls_werks_n-werks && ',' && ps_t06-werks_t_n.
    ELSE.
      ps_t06-werks_t_n = ls_werks_n-werks && ',' && ps_t06-werks_t_n.
    ENDIF.
  ENDLOOP.

  LOOP AT pt_ekorg_n INTO DATA(ls_ekorg_n).
    ps_t06-ekorg_t_n = ls_ekorg_n-ekorg && ',' && ps_t06-ekorg_t_n.
  ENDLOOP.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_PRO_LOCK_ZXY_ID
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> P_ZXY_ID
*&---------------------------------------------------------------------*
FORM frm_pro_lock_zxy_id  USING    pv_zxy_id TYPE zret0006-zxy_id
                                    pv_flg TYPE char1.

  IF pv_flg = ''.
    CALL FUNCTION 'ENQUEUE_EZ_ZRET0006'
      EXPORTING
*       MODE_ZRET0006  = 'X'
*       MANDT          = SY-MANDT
        zxy_id         = pv_zxy_id
*       X_ZXY_ID       = ' '
*       _SCOPE         = '2'
*       _WAIT          = ' '
*       _COLLECT       = ' '
      EXCEPTIONS
        foreign_lock   = 1
        system_failure = 2
        OTHERS         = 3.

    IF sy-subrc NE 0.
      MESSAGE s888(sabapdocu) WITH  '用户' sy-msgv1 '正在处理' DISPLAY LIKE 'E'.
      LEAVE LIST-PROCESSING.
    ENDIF.
  ELSE.
    CALL FUNCTION 'DEQUEUE_EZ_ZRET0006'
      EXPORTING
        zxy_id = pv_zxy_id.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_ZXYZT
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> P_ZXY_ID
*&---------------------------------------------------------------------*
FORM frm_check_zxyzt  USING    pv_zxy_id TYPE zret0006-zxy_id.
  SELECT SINGLE *
    FROM zret0006
    WHERE zxy_id = @pv_zxy_id
    INTO @DATA(ls_zret0006).
  IF sy-subrc NE 0.
    MESSAGE s888(sabapdocu) WITH '没有对应的协议存在！' DISPLAY LIKE 'E'.
    LEAVE LIST-PROCESSING.
  ENDIF.

  CASE 'X'.
    WHEN rb_edit.

      CASE ls_zret0006-zxyzt.
        WHEN 'A'.
          MESSAGE s888(sabapdocu) WITH '协议已审批不能修改！' DISPLAY LIKE 'E'.
          LEAVE LIST-PROCESSING.
        WHEN 'D'.
          MESSAGE s888(sabapdocu) WITH '协议已作废不能修改！' DISPLAY LIKE 'E'.
          LEAVE LIST-PROCESSING.
        WHEN 'P'.
          MESSAGE s888(sabapdocu) WITH '协议已提交不能修改！' DISPLAY LIKE 'E'.
          LEAVE LIST-PROCESSING.
        WHEN OTHERS.
      ENDCASE.


    WHEN rb_rles.

      CASE ls_zret0006-zxyzt.
        WHEN 'N'.
          MESSAGE s888(sabapdocu) WITH '协议未提交不能审批！' DISPLAY LIKE 'E'.
          LEAVE LIST-PROCESSING.
        WHEN 'D'.
          MESSAGE s888(sabapdocu) WITH '协议已作废不能审批！' DISPLAY LIKE 'E'.
          LEAVE LIST-PROCESSING.
        WHEN 'R'.
          MESSAGE s888(sabapdocu) WITH '协议未提交不能审批！' DISPLAY LIKE 'E'.
          LEAVE LIST-PROCESSING.
        WHEN '1'.
          MESSAGE s888(sabapdocu) WITH '协议已提交，OA接收失败不能审批！' DISPLAY LIKE 'E'.
          LEAVE LIST-PROCESSING.
        WHEN OTHERS.
      ENDCASE.

    WHEN OTHERS.
  ENDCASE.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_ZFLSQF
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> P_ZFLSQF
*&---------------------------------------------------------------------*
FORM frm_check_zflsqf  USING    pv_zflsqf TYPE zret0006-zflsqf.

  IF pv_zflsqf IS INITIAL.
    MESSAGE s888(sabapdocu) WITH '返利收取方必填' DISPLAY LIKE 'E'.
    LEAVE LIST-PROCESSING.
  ELSE.
    SELECT COUNT(*)
      FROM t001
      WHERE bukrs = pv_zflsqf.
    IF sy-subrc NE 0.
      MESSAGE s888(sabapdocu) WITH '公司代码不存在' DISPLAY LIKE 'E'.
      LEAVE LIST-PROCESSING.
    ENDIF.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_ZFLZFF
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> P_ZFLZFF
*&---------------------------------------------------------------------*
FORM frm_check_zflzff  USING    pv_zflzff TYPE zret0006-zflzff.
  IF pv_zflzff IS INITIAL.
    MESSAGE s888(sabapdocu) WITH '返利支付方必填' DISPLAY LIKE 'E'.
    LEAVE LIST-PROCESSING.
  ELSE.
    SELECT COUNT(*)
      FROM lfa1
      WHERE lifnr = pv_zflzff.
    IF sy-subrc NE 0.
      MESSAGE s888(sabapdocu) WITH '供应商不存在！' DISPLAY LIKE 'E'.
      LEAVE LIST-PROCESSING.
    ENDIF.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_DATA_9913
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- GS_T20
*&---------------------------------------------------------------------*
FORM frm_set_data_9913    USING ps_t09 TYPE ty_t09
                          CHANGING ps_t20 TYPE ty_t20.

  ps_t20-matnr   = |{ ps_t20-matnr ALPHA = IN WIDTH = 18 }| .
  SELECT SINGLE
    maktx
    INTO ps_t20-maktx
    FROM makt
    WHERE matnr = ps_t20-matnr.

  SELECT SINGLE
    meins
    INTO ps_t20-meins
    FROM mara
    WHERE matnr = ps_t20-matnr.

  IF ps_t20-zpeinh IS INITIAL .
    ps_t20-zpeinh = 1.
  ENDIF.
  ps_t20-zspz_id = ps_t09-zspz_id.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_SCREEN_9913
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*& -->  P1        TEXT
*& <--  P2        TEXT
*&---------------------------------------------------------------------*
FORM frm_set_screen_9913 .

  PERFORM frm_set_screen_all.
***  IF GV_FLG_SPZ_EXIST = 'X'.
***    LOOP AT SCREEN.
***      IF SCREEN-GROUP1 = 'EDT' .
***        SCREEN-INPUT = 0.
***      ENDIF.
***      MODIFY SCREEN.
***    ENDLOOP.
***  ENDIF.

  DATA: lth_cols TYPE cx_tableview_column.
  LOOP AT tc_spz-cols INTO lth_cols.
    IF lth_cols-screen-name = 'GS_T20_SUB-ZSPZ_ID'.
      lth_cols-invisible = 1.
      MODIFY tc_spz-cols FROM lth_cols.
    ENDIF.
  ENDLOOP.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_GET_DATA_SPZ
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> PS_T07_ZSPZ_ID
*&      <-- PS_T09
*&      <-- PT_T20
*&---------------------------------------------------------------------*
FORM frm_get_data_spz  USING    pv_zspz_id TYPE zret0009-zspz_id
                       CHANGING ps_t09 TYPE ty_t09
                                pt_t20 TYPE tt_t20.

  SELECT SINGLE *
    INTO CORRESPONDING FIELDS OF ps_t09
    FROM zret0009
    WHERE zspz_id = pv_zspz_id.
  SELECT
    *
    INTO CORRESPONDING FIELDS OF TABLE pt_t20
    FROM zret0020
    WHERE zspz_id = pv_zspz_id.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_GET_DATA_SPZ
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> PS_T07_ZSPZ_ID
*&      <-- PS_T09
*&      <-- PT_T20
*&---------------------------------------------------------------------*
FORM frm_get_data_spz_n  USING    pv_zspz_id TYPE zret0009-zspz_id
                                  pt_t20_all TYPE tt_t20
                                  pt_t09_all TYPE tt_t09
                       CHANGING ps_t09 TYPE ty_t09
                                pt_t20 TYPE tt_t20.
  CLEAR:
        ps_t09,
        pt_t20.

  SELECT SINGLE *
    FROM @pt_t09_all AS i
    WHERE zspz_id = @pv_zspz_id
    INTO CORRESPONDING FIELDS OF @ps_t09.

  SELECT
    *
    FROM @pt_t20_all AS i
    WHERE zspz_id = @pv_zspz_id
    INTO CORRESPONDING FIELDS OF TABLE @pt_t20.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_ZSPZ_ID
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_T07_ZSPZ_ID
*&---------------------------------------------------------------------*
FORM frm_check_zspz_id  USING    pv_zspz_id TYPE zret0009-zspz_id
                                 pt_t09 TYPE tt_t09.

  IF pv_zspz_id IS INITIAL .
    RETURN.
  ENDIF.

  SELECT COUNT(*)
    FROM zret0009
    WHERE zspz_id = pv_zspz_id.
  IF sy-subrc NE 0.
    READ TABLE pt_t09 TRANSPORTING NO FIELDS WITH KEY zspz_id = pv_zspz_id .
    IF sy-subrc NE 0.
      MESSAGE e888(sabapdocu) WITH '商品组不存在！'.
    ENDIF.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_GET_DATA_LIST_BOX
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- GT_VLS_ZFLLX
*&      <-- GT_VLS_ZHSZQ
*&      <-- GT_VLS_ZJSZQ
*&      <-- GT_VLS_ZHSJZ
*&---------------------------------------------------------------------*
FORM frm_get_data_list_box  CHANGING pt_vls_zfllx   TYPE vrm_values
                                     pt_vls_zjsff   TYPE vrm_values
                                     pt_vls_zhszq   TYPE vrm_values
                                     pt_vls_zjszq   TYPE vrm_values
                                     pt_vls_zhsjz   TYPE vrm_values
                                     pt_vls_zhtlx   TYPE vrm_values.

  SELECT
    zfllx  AS  key ,
    zfllx_txt      AS  text
    FROM zret0002
    INTO CORRESPONDING FIELDS OF TABLE @pt_vls_zfllx.

  SELECT
    zhszq  AS  key ,
    zhszq_txt      AS  text
    FROM zret0005
    INTO CORRESPONDING FIELDS OF TABLE @pt_vls_zhszq.

  SELECT
    zjszq  AS  key ,
    zjszq_txt      AS  text
    FROM zret0004
    INTO CORRESPONDING FIELDS OF TABLE @pt_vls_zjszq.

  SELECT
    zhsjz  AS  key ,
    zhsjz_txt      AS  text
    FROM zret0003
    INTO CORRESPONDING FIELDS OF TABLE @pt_vls_zhsjz.

  SELECT
    domvalue_l  AS  key ,
    ddtext      AS  text
    FROM dd07t
    WHERE domname = 'ZREM_ZHTLX'
    AND   ddlanguage = @sy-langu
    INTO CORRESPONDING FIELDS OF TABLE @pt_vls_zhtlx.

  SELECT
    domvalue_l  AS  key ,
    ddtext      AS  text
    FROM dd07t
    WHERE domname = 'ZREM_ZJSFF'
    AND   ddlanguage = @sy-langu
    INTO CORRESPONDING FIELDS OF TABLE @pt_vls_zjsff.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_ADD_INIT_LINE
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- GT_T06
*&      <-- GT_T07
*&      <-- GT_T08
*&      <-- GT_T11
*&      <-- GT_T12
*&      <-- GT_T13
*&      <-- GT_T14
*&      <-- GT_T15
*&      <-- GT_T16
*&      <-- GT_T20
*&      <-- GT_BUKRS
*&      <-- GT_DCWRK
*&      <-- GT_WERKS
*&      <-- GT_EKORG
*&---------------------------------------------------------------------*
FORM frm_add_init_line  CHANGING pt_t06      TYPE tt_t06
                                 pt_t07       TYPE tt_t07
                                 pt_t39       TYPE tt_t39
                                 pt_t08       TYPE tt_t08
                                 pt_t11       TYPE tt_t11
                                 pt_t12       TYPE tt_t12
                                 pt_t13       TYPE tt_t13
                                 pt_t38       TYPE tt_t38
                                 pt_t14       TYPE tt_t14
                                 pt_t37       TYPE tt_t37
                                 pt_t15       TYPE tt_t15
                                 pt_t16       TYPE tt_t16
                                 pt_t20       TYPE tt_t20
                                 pt_t21       TYPE tt_t21
                                 pt_bukrs     TYPE tt_bukrs
                                 pt_dcwrk     TYPE tt_dcwrk
                                 pt_werks     TYPE tt_werks
                                 pt_ekorg     TYPE tt_ekorg
                                 pt_bukrs_n   TYPE tt_bukrs
                                 pt_dcwrk_n   TYPE tt_dcwrk
                                 pt_werks_n   TYPE tt_werks
                                 pt_ekorg_n   TYPE tt_ekorg.



  DO 1 TIMES.
    APPEND INITIAL LINE TO pt_t06    .
    APPEND INITIAL LINE TO pt_t07    .
    APPEND INITIAL LINE TO pt_t39    .
    APPEND INITIAL LINE TO pt_t08    .
    APPEND INITIAL LINE TO pt_t11    .
    APPEND INITIAL LINE TO pt_t12    .
    APPEND INITIAL LINE TO pt_t13    .
    APPEND INITIAL LINE TO pt_t38    .
    APPEND INITIAL LINE TO pt_t14    .
    APPEND INITIAL LINE TO pt_t37    .
    APPEND INITIAL LINE TO pt_t15    .
    APPEND INITIAL LINE TO pt_t16    .
    APPEND INITIAL LINE TO pt_t20    .
    APPEND INITIAL LINE TO pt_t21    .
    APPEND INITIAL LINE TO pt_bukrs    .
    pt_bukrs[ 1 ]-bukrs = 'ALL'.
    APPEND INITIAL LINE TO pt_dcwrk    .
    pt_dcwrk[ 1 ]-dcwrk = 'ALL'.
    APPEND INITIAL LINE TO pt_werks    .
    pt_werks[ 1 ]-werks = 'ALL'.
    APPEND INITIAL LINE TO pt_ekorg    .
    pt_ekorg[ 1 ]-ekorg = 'ALL'.

*    APPEND INITIAL LINE TO pt_bukrs_n    .
*    pt_bukrs_n[ 1 ]-bukrs = 'ALL'.
*    APPEND INITIAL LINE TO pt_dcwrk_n    .
*    pt_dcwrk_n[ 1 ]-dcwrk = 'ALL'.
*    APPEND INITIAL LINE TO pt_werks_n    .
*    pt_werks_n[ 1 ]-werks = 'ALL'.
    APPEND INITIAL LINE TO pt_ekorg_n    .
    pt_ekorg_n[ 1 ]-ekorg = 'ALL'.
  ENDDO.


ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_TITLE
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GV_TITLE
*&---------------------------------------------------------------------*
FORM frm_set_title  USING    pv_title.

  CALL FUNCTION 'RECA_GUI_SET_TITLEBAR'
    EXPORTING
      id_text1 = pv_title.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_DATA_CHECK_MAIN
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*& -->  P1        TEXT
*& <--  P2        TEXT
*&---------------------------------------------------------------------*
FORM frm_data_check_main USING     ps_t06      TYPE ty_t06
                                    ps_t10      TYPE ty_t10
                                    ps_t10_js   TYPE ty_t10
                                    pt_t11      TYPE tt_t11
                                    pt_t11_hs   TYPE tt_t11
                                    pt_t11_js   TYPE tt_t11
                                    pt_t11_jsh  TYPE tt_t11
                                    pt_t07      TYPE tt_t07
                                    pt_t39      TYPE tt_t39
                                    pt_t20      TYPE tt_t20
                                    pt_t13      TYPE tt_t13
                                    pt_t38      TYPE tt_t38
                                    pt_bukrs    TYPE tt_bukrs
                                    pt_dcwrk    TYPE tt_dcwrk
                                    pt_werks    TYPE tt_werks
                                    pt_ekorg    TYPE tt_ekorg
                                    pt_bukrs_n  TYPE tt_bukrs
                                    pt_dcwrk_n  TYPE tt_dcwrk
                                    pt_werks_n  TYPE tt_werks
                                    pt_ekorg_n  TYPE tt_ekorg
                                    pt_t12      TYPE tt_t12
                                    pt_t21      TYPE tt_t21
                        CHANGING    pv_flg_err  TYPE char1.

  DATA:
    lt_msglist TYPE scp1_general_errors,
    ls_msglist TYPE scp1_general_error.

  DATA:
    lv_flg_err     TYPE char1.


  DATA(lt_t11_js) = pt_t11_hs[].
  DATA(lt_t11_jsh) = pt_t11_jsh[].
  DELETE lt_t11_js       WHERE zjt_from      IS INITIAL AND zjt_by IS INITIAL.
  DELETE lt_t11_jsh    WHERE zjt_from      IS INITIAL AND zjt_by IS INITIAL.



  CLEAR:
        lt_msglist.
  IF pv_flg_err NE 'E'.
    pv_flg_err = 'S'.
  ENDIF.

  ls_msglist-msgty = 'E'.
  ls_msglist-msgid = '00'.
  ls_msglist-msgno = '001'.


  IF ps_t06-zbegin >= '20210101' OR ps_t06-zend >= '20210101'.
    pv_flg_err = 'E'.
    ls_msglist-msgv1 = ' 2021年及之后返利协议请使用新版返利平台创建或修改!'.
    APPEND ls_msglist TO lt_msglist.
  ENDIF.

  IF ps_t06-zflzff IS INITIAL.
    pv_flg_err = 'E'.
    ls_msglist-msgv1 = ' 付款方不能为空!'.
    APPEND ls_msglist TO lt_msglist.

  ENDIF.

  IF ps_t06-ekgrp IS INITIAL.
    pv_flg_err = 'E'.
    ls_msglist-msgv1 = ' 采购组不能为空!'.
    APPEND ls_msglist TO lt_msglist.

  ENDIF.

  IF ps_t06-zhtid IS INITIAL.
    pv_flg_err = 'E'.
    ls_msglist-msgv1 = ' 关联合同号不能为空!'.
    APPEND ls_msglist TO lt_msglist.

  ENDIF.

  IF ps_t06-zpayday IS INITIAL.
    pv_flg_err = 'E'.
    ls_msglist-msgv1 = ' 付款期间不能为空!'.
    APPEND ls_msglist TO lt_msglist.
  ENDIF.

  IF ps_t06-zbegin IS INITIAL.
    pv_flg_err = 'E'.
    ls_msglist-msgv1 = ' 开始日期不能为空!'.
    APPEND ls_msglist TO lt_msglist.
  ENDIF.

  IF ps_t06-zend IS INITIAL.
    pv_flg_err = 'E'.
    ls_msglist-msgv1 = ' 结束日期不能为空!'.
    APPEND ls_msglist TO lt_msglist.
  ENDIF.

  IF ps_t06-zbegin > ps_t06-zend.
    PERFORM frm_add_msg(zbcs0001) USING '开始日期不能大于结束日期'  CHANGING lt_msglist.
  ENDIF.

  IF ps_t06-zdffs IS INITIAL.
    pv_flg_err = 'E'.
    ls_msglist-msgv1 = ' 兑付方式不能为空!'.
    APPEND ls_msglist TO lt_msglist.
  ENDIF.

  IF gv_zxybstyp = 'A' OR gv_zxybstyp EQ 'V' .
    IF ps_t06-zhsjz IS INITIAL.
      pv_flg_err = 'E'.
      ls_msglist-msgv1 = ' 核算基准必填!'.
      APPEND ls_msglist TO lt_msglist.
    ENDIF.
    IF ps_t06-zhszq IS INITIAL.
      pv_flg_err = 'E'.
      ls_msglist-msgv1 = ' 核算周期必填!'.
      APPEND ls_msglist TO lt_msglist.
    ENDIF.
  ENDIF.

  IF gv_zxybstyp = 'A' OR gv_zxybstyp EQ 'V' OR gv_zxybstyp = 'T'.

    IF ps_t06-zjszq IS INITIAL.
      pv_flg_err = 'E'.
      ls_msglist-msgv1 = ' 结算周期必填!'.
      APPEND ls_msglist TO lt_msglist.
    ENDIF.
    IF ps_t06-zhstype IS INITIAL.
      pv_flg_err = 'E'.
      ls_msglist-msgv1 = ' 核算周期类型必填!'.
      APPEND ls_msglist TO lt_msglist.
    ENDIF.
  ENDIF.

  IF gv_zxybstyp = 'T'.
    IF ps_t06-zdffs = 'M'.
      pv_flg_err = 'E'.
      ls_msglist-msgv1 = ' 协议类型为T时，兑付方式不能为货返!'.
      APPEND ls_msglist TO lt_msglist.
    ENDIF.
  ENDIF.

*  检查阶梯抬头 返利形式和计算方法 不能为空 否则子阶梯不允许维护
  IF gv_zxybstyp = 'V' OR gv_zxybstyp = 'A'.
    IF ps_t10-zjsff NE 'T'.

      IF ps_t10-zflxs IS INITIAL OR ps_t10-zjsff IS INITIAL.
        pv_flg_err = 'E'.
        ls_msglist-msgv1 = ' 阶梯【返利形式】和【计算方法】不能为空!'.
        APPEND ls_msglist TO lt_msglist.
      ENDIF.
    ENDIF.

*    IF lt_t11_js[] IS NOT INITIAL OR lt_t11_jsh[] IS NOT INITIAL.

    IF ps_t10-zjsff EQ 'T'.
      IF ps_t10_js-zflxs IS INITIAL OR ps_t10_js-zjsff IS INITIAL.
        pv_flg_err = 'E'.
        ls_msglist-msgv1 = ' 返利条件阶梯【返利形式】和【计算方法】不能为空!'.
        APPEND ls_msglist TO lt_msglist.
      ENDIF.
    ENDIF.
*    ENDIF.
  ENDIF.


  IF ( ps_t06-zhsjz = '1004' OR ps_t06-zhsjz = '1005' ) AND
      ( ps_t10-zjsff = 'T' OR ps_t10_js-zjsff = 'T').
    pv_flg_err = 'E'.
    ls_msglist-msgv1 = '计算方法为T,任务核算基准不支持取低值或者取高值！'.
    APPEND ls_msglist TO lt_msglist.

  ENDIF.

  IF ps_t10-zjsff = 'P'.
    LOOP AT pt_t11 INTO DATA(ls_t11_tmp) WHERE zjt_fm = 0.
      pv_flg_err = 'E'.
      ls_msglist-msgv1 = '当返利协议计算方法为比例时,分母必须大于0'.
      APPEND ls_msglist TO lt_msglist.
    ENDLOOP.
  ENDIF.

  IF gv_zxybstyp NE 'T'.
    IF ps_t06-zfltgf IS INITIAL.
      pv_flg_err = 'E'.
      ls_msglist-msgv1 = ' 返利提供方不能为空!'.
      APPEND ls_msglist TO lt_msglist.
    ENDIF.
  ENDIF.

  IF gv_zxybstyp EQ 'F' AND gs_t06-zdffs = 'O'.
    LOOP AT gt_t08 INTO DATA(ls_t08) WHERE zmwskz IS NOT INITIAL .
      IF ls_t08-zmwskz(1) NE 'J'.
        pv_flg_err = 'E'.
        ls_msglist-msgv1 = ' 固定费用类协议,兑付方式为票折时，只能选择进项税码!'.
        APPEND ls_msglist TO lt_msglist.
      ENDIF.
    ENDLOOP.
  ENDIF.


  PERFORM frm_author_check_ekgrp USING gs_t06-ekgrp
                                       gv_actvt
                                 CHANGING lv_flg_err.
  IF lv_flg_err = 'E'.
    pv_flg_err = 'E'.
    ls_msglist-msgv1 = '您没有采购组:' && gs_t06-ekgrp && ' 的权限 !'.
    APPEND ls_msglist TO lt_msglist.
  ENDIF.


*  IF ps_t06-zydqd = 'M' AND ps_t06-zcj IS INITIAL.
*    pv_flg_err = 'E'.
*    ls_msglist-msgv1 = '约定渠道为生产商时，生产商必填!'.
*    APPEND ls_msglist TO lt_msglist.
*  ENDIF.
  IF gv_zxybstyp NE 'T'.
    READ TABLE pt_t12 TRANSPORTING NO FIELDS WITH KEY lifnr = 'ALL'.
    IF sy-subrc EQ 0.
      READ TABLE pt_t21 TRANSPORTING NO FIELDS WITH KEY zcj = 'ALL'.
      IF sy-subrc EQ 0.
        READ TABLE pt_t07 TRANSPORTING NO FIELDS WITH KEY zspwd = 'A'.
        IF sy-subrc EQ 0.
          pv_flg_err = 'E'.
          ls_msglist-msgv1 = '渠道供应商和生产商和商品不能全部为ALL!'.
          APPEND ls_msglist TO lt_msglist.
        ENDIF.
      ENDIF.
    ENDIF.
  ENDIF.

  DATA(lt_t21_tmp) = pt_t21[].
  DATA(lt_t12_tmp) = pt_t12[].
  DELETE lt_t21_tmp WHERE zcj IS INITIAL.
  DELETE lt_t12_tmp WHERE lifnr IS INITIAL.
  IF lt_t21_tmp[] IS INITIAL AND lt_t12_tmp[] IS INITIAL.
    READ TABLE pt_t07 TRANSPORTING NO FIELDS WITH KEY zspwd = 'A'.
    IF sy-subrc EQ 0.
      pv_flg_err = 'E'.
      ls_msglist-msgv1 = '渠道供应商和生产商和商品不能全部为ALL!'.
      APPEND ls_msglist TO lt_msglist.
    ENDIF.
  ENDIF.


  IF ps_t10-zflxs = 'Q'.
    IF lines( pt_t07[] ) > 1.
      pv_flg_err = 'E'.
      ls_msglist-msgv1 = '返利形式为数量时，商品维度必须为单个商品，且只有一行!'.
      APPEND ls_msglist TO lt_msglist.
    ENDIF.
    IF line_exists( pt_t07[ 1 ] ).
      IF pt_t07[ 1 ]-zspwd NE 'S'.
        pv_flg_err = 'E'.
        ls_msglist-msgv1 = '返利形式为数量时，商品维度必须为单个商品，且只有一行!'.
        APPEND ls_msglist TO lt_msglist.
      ENDIF.
    ENDIF.
  ENDIF.
  IF ( ps_t10-zflxs = 'M' OR ps_t10_js-zflxs = 'M' ) AND ps_t06-zdffs EQ 'M'.
    pv_flg_err = 'E'.
    ls_msglist-msgv1 = '返利形式为金额时，兑付方式不能为货返!'.
    APPEND ls_msglist TO lt_msglist.
  ENDIF.
  IF ( ps_t10-zflxs = 'Q' OR ps_t10_js-zflxs = 'Q'  ) AND ps_t06-zdffs NE 'M'.
    pv_flg_err = 'E'.
    ls_msglist-msgv1 = '返利形式为数量时，兑付方式只能为货返!'.
    APPEND ls_msglist TO lt_msglist.
  ENDIF.

  IF gv_zxybstyp = 'Q' AND ps_t06-zdffs NE 'M'.
    pv_flg_err = 'E'.
    ls_msglist-msgv1 = '固定数量类的返利协议，兑付方式只能为货返!'.
    APPEND ls_msglist TO lt_msglist.
  ENDIF.

  PERFORM frm_check_item USING pt_t07
                               ps_t10
                               pt_t20
                         CHANGING pv_flg_err
                                  lt_msglist.

  PERFORM frm_check_item USING pt_t39
                               ps_t10
                               pt_t20
                         CHANGING pv_flg_err
                                  lt_msglist.


  IF lt_t11_js[] IS NOT INITIAL OR lt_t11_jsh IS NOT INITIAL.
    PERFORM frm_check_zflxs USING ps_t10_js
                                  'X'
                            CHANGING pv_flg_err
                                     lt_msglist.

  ENDIF.

  PERFORM frm_check_double USING
                                  pt_t12
                                  pt_t13
                                  pt_t38
                                  pt_t21
                                  pt_bukrs
                                  pt_dcwrk
                                  pt_werks
                                  pt_ekorg
                                  pt_bukrs_n
                                  pt_dcwrk_n
                                  pt_werks_n
                                  pt_ekorg_n
                         CHANGING pv_flg_err
                                  lt_msglist.


*  检查组织结构的层级关系
*________________________________________________________________________________________
*\       \ 排除                            \    \  新增                                  \
*\_______\_________________________________\____\________________________________________\
*\ 上级  \ 上级不存在、上级存在状态是排除  \    \ 上级存在（上级存在但是状态是排除允许） \
*\_______\_________________________________\____\________________________________________\
*\ 下级  \ 下级不允许排除                  \    \ 下级不允许新增                         \
*\_______\_________________________________\____\________________________________________\
  PERFORM frm_check_zzz USING     pt_bukrs
                                  pt_dcwrk
                                  pt_werks
                         CHANGING pv_flg_err
                                  lt_msglist.

  IF NOT ( pt_bukrs_n[] IS INITIAL AND pt_dcwrk_n[] IS INITIAL  AND pt_werks_n[] IS INITIAL ).
    PERFORM frm_check_zzz USING     pt_bukrs_n
                                    pt_dcwrk_n
                                    pt_werks_n
                           CHANGING pv_flg_err
                                    lt_msglist.
  ENDIF.

  IF pv_flg_err = 'E'.
    CALL FUNCTION 'SCPR_SV_SHOW_MESSAGE_LIST'
      EXPORTING
        title_text    = '消息提示'
        sort_by_level = ' '
        show_ids      = ''
        message_list  = lt_msglist[].
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_ZZZ
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> PT_BUKRS
*&      --> PT_DCWRK
*&      --> PT_WERKS
*&---------------------------------------------------------------------*
FORM frm_check_zzz  USING    pt_bukrs      TYPE tt_bukrs
                              pt_dcwrk      TYPE tt_dcwrk
                              pt_werks      TYPE tt_werks
                     CHANGING pv_flg_err    TYPE char1
                              pt_msglist    TYPE scp1_general_errors.

  DATA:
    lt_dc_buk   TYPE tt_wrk_check,
    lt_wrk_dc   TYPE tt_wrk_check,
    ls_data_wrk TYPE ty_wrk_check,
    ls_dcwrk    TYPE ty_dcwrk.
  DATA:
        ls_msglist TYPE scp1_general_error.

  DATA(lt_bukrs) = pt_bukrs[].
  DATA(lt_dcwrk) = pt_dcwrk[].
  DATA(lt_werks) = pt_werks[].

  DELETE lt_bukrs WHERE bukrs IS INITIAL.
  DELETE lt_dcwrk WHERE dcwrk IS INITIAL.
  DELETE lt_werks WHERE werks IS INITIAL.


  ls_msglist-msgty = 'E'.
  ls_msglist-msgid = '00'.
  ls_msglist-msgno = '001'.


*  检查DC层级（与公司代码层级比较）

*  11111 先取出公司代码层级对应的DC清单
  READ TABLE lt_bukrs TRANSPORTING NO FIELDS WITH KEY bukrs = gc_all .
*  IF sy-subrc EQ 0 OR lt_bukrs[] IS INITIAL.
*  若存在 ALL 的条目则无限制取数
  IF sy-subrc EQ 0 .
    SELECT
      a~werks,
      ' ' AS exclude
      FROM t001w AS a  JOIN t001k AS b
                       ON a~werks = b~bwkey
                       AND a~vlfkz = 'B'
      INTO CORRESPONDING FIELDS OF TABLE @lt_dc_buk.

  ELSE.

*    取未排除的数据
    SELECT
      a~werks,
      ' ' AS exclude
      FROM t001w AS a  JOIN t001k AS b
                       ON a~werks = b~bwkey
                       AND a~vlfkz = 'B'
                       JOIN @lt_bukrs AS m
                       ON b~bukrs = m~bukrs
                       AND  m~exclude = ''
      INTO CORRESPONDING FIELDS OF TABLE @lt_dc_buk.

*    取排除的数据
    SELECT
      a~werks,
      'X' AS exclude
      FROM t001w AS a  JOIN t001k AS b
                       ON a~werks = b~bwkey
                       AND a~vlfkz = 'B'
                       JOIN @lt_bukrs AS m
                       ON b~bukrs = m~bukrs
                       AND  m~exclude = 'X'
      APPENDING CORRESPONDING FIELDS OF TABLE @lt_dc_buk.

  ENDIF.

*  22222  根据DC层级的DC清单 与 上一步公司代码层级对应的DC清单进行比较检查
  SORT lt_dc_buk BY exclude werks .
  LOOP AT lt_dcwrk INTO ls_dcwrk .

*    检查是否允许排除
    IF ls_dcwrk-exclude = 'X'.
      READ TABLE lt_dc_buk TRANSPORTING NO FIELDS WITH KEY
                                                              exclude = ''
                                                              werks = ls_dcwrk-dcwrk
                                                              BINARY SEARCH.
      IF sy-subrc NE 0.
        pv_flg_err = 'E'.
        ls_msglist-msgv1 = ls_dcwrk-dcwrk && '：上一级不存在的情况下，下一级不能被排除，但可以被增加!'.
        APPEND ls_msglist TO pt_msglist.
      ENDIF.

      READ TABLE lt_dc_buk TRANSPORTING NO FIELDS WITH KEY
                                                              exclude = 'X'
                                                              werks = ls_dcwrk-dcwrk
                                                              BINARY SEARCH.
      IF sy-subrc EQ 0.
        pv_flg_err = 'E'.
        ls_msglist-msgv1 = ls_dcwrk-dcwrk && '：上一级被排除的情况下，下一级不能被排除，但可以被增加!'.
        APPEND ls_msglist TO pt_msglist.
      ENDIF.

*    检查是否允许新增
    ELSE.
      READ TABLE lt_dc_buk TRANSPORTING NO FIELDS WITH KEY
                                                              exclude = 'X'
                                                              werks = ls_dcwrk-dcwrk
                                                              BINARY SEARCH.
      IF sy-subrc EQ 0.
      ELSE.
        READ TABLE lt_dc_buk TRANSPORTING NO FIELDS WITH KEY
                                                                exclude = ''
                                                                werks = ls_dcwrk-dcwrk
                                                                BINARY SEARCH.
        IF sy-subrc EQ 0.
          pv_flg_err = 'E'.
          ls_msglist-msgv1 = ls_dcwrk-dcwrk && '：存在上一级的情况下，下一级可以排除但不能被增加!'.
          APPEND ls_msglist TO pt_msglist.
        ENDIF.
      ENDIF.
    ENDIF.
  ENDLOOP.


*  检查门店层级（与DC代码层级比较）

*  首先将公司代码级对应的DC清单 并入到DC级
  LOOP AT lt_dc_buk INTO ls_data_wrk.
    CLEAR ls_dcwrk.
    ls_dcwrk-dcwrk = ls_data_wrk-werks.
    ls_dcwrk-exclude = ls_data_wrk-exclude.
    APPEND ls_dcwrk TO lt_dcwrk.
  ENDLOOP.

*  11111 先取出DC层级对应的门店清单
  READ TABLE lt_dcwrk TRANSPORTING NO FIELDS WITH KEY dcwrk = gc_all .
*  IF sy-subrc EQ 0 OR lt_dcwrk[] IS INITIAL.
*  若存在 ALL 的条目则无限制取数
  IF sy-subrc EQ 0 .
    SELECT
      a~werks,
      ' ' AS exclude
      FROM t001w AS a
      APPENDING CORRESPONDING FIELDS OF TABLE @lt_wrk_dc.

  ELSE.

*    取未排除的数据
*    select
*      a~werks,
*      ' ' as exclude
*      from t001w as a join zsdt0014 as b
*                      on   a~werks = b~locnr
*                      join @lt_dcwrk as m
*                      on   b~zsjbm = m~dcwrk
*                      and  m~exclude = ''
*      appending corresponding fields of table @lt_wrk_dc.

    SELECT
      a~werks,
      ' ' AS exclude
      FROM t001w AS a JOIN wrf3 AS b
                      ON   right( b~locnr,4 ) = a~werks
                      JOIN @lt_dcwrk AS m
                      ON   b~loclb = m~dcwrk
                      AND  m~exclude = ''
                      AND  a~vlfkz = 'A'
      APPENDING CORRESPONDING FIELDS OF TABLE @lt_wrk_dc.

*    取排除的数据
    SELECT
      a~werks,
      'X' AS exclude
      FROM t001w AS a JOIN wrf3 AS b
                      ON   right( b~locnr,4 ) = a~werks
                      JOIN @lt_dcwrk AS m
                      ON   b~loclb = m~dcwrk
                      AND  m~exclude = 'X'
                      AND  a~vlfkz = 'A'
      APPENDING CORRESPONDING FIELDS OF TABLE @lt_wrk_dc.

  ENDIF.

*  22222  根据门店层级的门店清单 与 上一步DC代码层级对应的门店清单进行比较检查
  SORT lt_wrk_dc BY exclude werks .
  LOOP AT lt_werks INTO DATA(ls_werks) .
*    检查是否允许排除
    IF ls_werks-exclude = 'X'.
      READ TABLE lt_wrk_dc TRANSPORTING NO FIELDS WITH KEY
                                                              exclude = ''
                                                              werks = ls_werks-werks
                                                              BINARY SEARCH.
      IF sy-subrc NE 0.
        pv_flg_err = 'E'.
        ls_msglist-msgv1 = ls_werks-werks && '：上一级不存在的情况下，下一级不能被排除，但可以被增加!'.
        APPEND ls_msglist TO pt_msglist.
      ENDIF.

      READ TABLE lt_wrk_dc TRANSPORTING NO FIELDS WITH KEY
                                                              exclude = 'X'
                                                              werks = ls_werks-werks
                                                              BINARY SEARCH.
      IF sy-subrc EQ 0.
        pv_flg_err = 'E'.
        ls_msglist-msgv1 = ls_werks-werks && '：上一级被排除的情况下，下一级不能被排除，但可以被增加!'.
        APPEND ls_msglist TO pt_msglist.
      ENDIF.

*    检查是否允许新增
    ELSE.
      READ TABLE lt_wrk_dc TRANSPORTING NO FIELDS WITH KEY
                                                              exclude = 'X'
                                                              werks = ls_werks-werks
                                                              BINARY SEARCH.
      IF sy-subrc EQ 0.
      ELSE.
        READ TABLE lt_wrk_dc TRANSPORTING NO FIELDS WITH KEY
                                                                exclude = ''
                                                                werks = ls_werks-werks
                                                                BINARY SEARCH.
        IF sy-subrc EQ 0.
          pv_flg_err = 'E'.
          ls_msglist-msgv1 = ls_werks-werks && '：存在上一级的情况下，下一级可以排除但不能被增加!'.
          APPEND ls_msglist TO pt_msglist.
        ENDIF.
      ENDIF.
    ENDIF.
  ENDLOOP.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_ZHSJZ
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GV_ZXYBSTYP
*&      <-- GT_VLS_ZHSJZ
*&---------------------------------------------------------------------*
FORM frm_set_zhsjz  USING    pv_zxybstyp    TYPE zret0003-zxybstyp
                    CHANGING pt_vls_zhsjz   TYPE vrm_values.
  SELECT
    *
    INTO TABLE @DATA(lt_zret0003)
    FROM zret0003
    WHERE zxybstyp = @pv_zxybstyp.

  SORT lt_zret0003 BY zhsjz.
  LOOP AT pt_vls_zhsjz INTO DATA(ls_vls_zhsjz).
    READ TABLE lt_zret0003 TRANSPORTING NO FIELDS WITH KEY
                                                          zhsjz =  ls_vls_zhsjz-key
                                                          BINARY SEARCH.
    IF sy-subrc NE 0.
      DELETE pt_vls_zhsjz.
      CONTINUE.
    ENDIF.
  ENDLOOP.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_DATA_9230
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- GS_T08
*&---------------------------------------------------------------------*
FORM frm_set_data_tc_item_dm     USING    ps_t06  TYPE ty_t06
                                  CHANGING ps_t08 TYPE ty_t08.


  ps_t08-matnr   = |{ ps_t08-matnr ALPHA = IN WIDTH = 18 }| .
  ps_t08-lifnr   = |{ ps_t08-lifnr ALPHA = IN  }| .

  SELECT SINGLE
    maktx
    INTO ps_t08-maktx
    FROM makt
    WHERE matnr = ps_t08-matnr.

  SELECT SINGLE
    meins
    INTO ps_t08-meins
    FROM mara
    WHERE matnr = ps_t08-matnr.

  SELECT SINGLE
    name1
    INTO ps_t08-name1
    FROM lfa1
    WHERE lifnr = ps_t08-lifnr.

  IF gv_zxybstyp = 'F' AND ( ps_t06-zdffs = 'C' OR ps_t06-zdffs = 'A' ).
    ps_t08-zmwskz = 'X3'.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_SCREEN_9230
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*& -->  P1        TEXT
*& <--  P2        TEXT
*&---------------------------------------------------------------------*
FORM frm_set_screen_9230 .

  DATA: lth_cols TYPE cx_tableview_column.


  LOOP AT tc_item_dm-cols INTO lth_cols.

    CASE gv_zxybstyp.
      WHEN 'F'.

        IF lth_cols-screen-group2 = '13' .
          lth_cols-invisible = 1.
        ENDIF.

      WHEN 'Q'.

        IF lth_cols-screen-group2 = '12' .
          lth_cols-invisible = 1.
        ENDIF.

      WHEN OTHERS.
    ENDCASE.
    MODIFY tc_item_dm-cols FROM lth_cols.
  ENDLOOP.

  PERFORM frm_set_screen_all.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SAVE_DATA_ATT
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GT_T06
*&---------------------------------------------------------------------*
FORM frm_save_data_att  USING     pv_flg TYPE char10
                                  pt_t06 TYPE tt_t06.

  DATA:
    lt_zxyid TYPE TABLE OF zres0002,
    ls_zxyid TYPE zres0002.

  IF line_exists( pt_t06[ 1 ] ).
    ls_zxyid-zxy_id = pt_t06[ 1 ]-zxy_id.
    APPEND ls_zxyid TO lt_zxyid.
  ENDIF.


  CASE pv_flg.
    WHEN 'RLES'.
      CALL FUNCTION 'ZREFM0003'
* IMPORTING
*   OV_TYPE        =
*   OV_MSG         =
        TABLES
          it_zxyid = lt_zxyid.

      CALL FUNCTION 'ZREFM0004'
*   IMPORTING
*     OV_TYPE        =
*     OV_MSG         =
        TABLES
          it_zxyid = lt_zxyid.

      CALL FUNCTION 'ZREFM0013'
* IMPORTING
*   OV_TYPE        =
*   OV_MSG         =
        TABLES
          it_zxyid = lt_zxyid.

      CALL FUNCTION 'ZREFM0014'
*   IMPORTING
*     OV_TYPE        =
*     OV_MSG         =
        TABLES
          it_zxyid = lt_zxyid.
    WHEN OTHERS.
  ENDCASE.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_BEFORE_RLES_C
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_T06
*&      <-- LV_FLG_ERR
*&---------------------------------------------------------------------*
FORM frm_check_before_rles_c  USING    ps_t06     TYPE ty_t06
                              CHANGING  pv_flg_err TYPE char1.
  DATA:
    lt_msglist TYPE scp1_general_errors,
    ls_msglist TYPE scp1_general_error.


  CLEAR:
        lt_msglist.
  pv_flg_err = 'S'.

  ls_msglist-msgty = 'E'.
  ls_msglist-msgid = '00'.
  ls_msglist-msgno = '001'.

*----------------------------------------------------------------------*
*    BEGIN MODIFY BY XYLIU1   17.08.2020 14:06:56
*    <NOTES>
*    <OLD CODES>
*  SELECT COUNT(*)
*    FROM zret0018
*    WHERE zxy_id = ps_t06-zxy_id
*    AND   zjsd_qx = ''.
*  IF sy-subrc EQ 0.
*    pv_flg_err = 'E'.
**    ls_msglist-msgv1 = '协议已经生成结算单，不允许操作!'.
*    ls_msglist-msgv1 = '该协议存在有效结算单或存在结算单已兑付，不允许该操作!'.
*    APPEND ls_msglist TO lt_msglist.
*  ENDIF.
*    </OLD CODES>
*    <NEW CODES>
  SELECT COUNT(*)
    FROM zret0006
    WHERE zxy_id = ps_t06-zxy_id
    AND   zjsbs = 'X'.
  IF sy-subrc EQ 0.
    pv_flg_err = 'E'.
*    ls_msglist-msgv1 = '协议已经生成结算单，不允许操作!'.
    ls_msglist-msgv1 = '该协议存在有效结算单或存在结算单已兑付，不允许该操作!'.
    APPEND ls_msglist TO lt_msglist.
  ENDIF.

  SELECT COUNT(*)
    FROM zret0016
    WHERE zxy_id = ps_t06-zxy_id
    AND   zjsbs NE '' .
  IF sy-subrc EQ 0.
    pv_flg_err = 'E'.
*    ls_msglist-msgv1 = '协议已经生成结算单，不允许操作!'.
    ls_msglist-msgv1 = '该协议存在有效结算单或存在结算单已兑付，不允许该操作!'.
    APPEND ls_msglist TO lt_msglist.
  ENDIF.

  SELECT COUNT(*)
      FROM zret0018
      WHERE zxy_id = ps_t06-zxy_id
      AND   ( zjsstate = 'N' OR  zjsstate =  'R' ) .
  IF sy-subrc EQ 0.
    pv_flg_err = 'E'.
    ls_msglist-msgv1 = '该协议存在有效结算单或存在结算单已兑付，不允许该操作!'.
    APPEND ls_msglist TO lt_msglist.
  ENDIF.
*    </NEW CODES>
*    END MODIFY BY XYLIU1
*----------------------------------------------------------------------*
  IF pv_flg_err = 'E'.
    CALL FUNCTION 'SCPR_SV_SHOW_MESSAGE_LIST'
      EXPORTING
        title_text    = '消息提示'
        sort_by_level = ' '
        show_ids      = ''
        message_list  = lt_msglist[].
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SAVE_DATA_SPZ
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_T09
*&      --> GT_T20
*&---------------------------------------------------------------------*
FORM frm_save_data_spz  USING    ps_t09 TYPE ty_t09
                                 pt_t20 TYPE tt_t20.
  DATA:
    lt_t09     TYPE TABLE OF zret0009,
    ls_t09     TYPE zret0009,
    lt_t20     TYPE TABLE OF zret0020,
    ls_t20     TYPE zret0020,
    lt_t09_del TYPE TABLE OF zret0009,
    lt_t20_del TYPE TABLE OF zret0020.

  MOVE-CORRESPONDING ps_t09 TO ls_t09.
  MOVE-CORRESPONDING pt_t20 TO lt_t20.

  DELETE lt_t20 WHERE matnr IS INITIAL.

  SELECT
    *
    INTO CORRESPONDING FIELDS OF TABLE lt_t09_del
    FROM zret0009
    WHERE zspz_id = ps_t09-zspz_id.

  SELECT
    *
    INTO CORRESPONDING FIELDS OF TABLE lt_t20_del
    FROM zret0020
    WHERE zspz_id = ps_t09-zspz_id.

  ls_t09-zcjr = sy-uname.
  ls_t09-zcjrq = sy-datum.
  ls_t09-zcjsj = sy-uzeit.

  DELETE zret0009 FROM TABLE lt_t09_del.
  DELETE zret0020 FROM TABLE lt_t20_del.
  MODIFY zret0009 FROM ls_t09.
  MODIFY zret0020 FROM TABLE lt_t20.
  COMMIT WORK AND WAIT.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_PRO_LOCK_ZSPZ_ID
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LS_T07_ZSPZ_ID
*&---------------------------------------------------------------------*
FORM frm_pro_lock_zspz_id  USING    pv_zspz_id TYPE zret0009-zspz_id
                                    pv_flg      TYPE char1.

  DATA:
        lv_msgv1 TYPE sy-msgv1.

  IF pv_flg = ''.
    CALL FUNCTION 'ENQUEUE_EZ_ZRET0009'
      EXPORTING
*       MODE_ZRET0006  = 'X'
*       MANDT          = SY-MANDT
        zspz_id        = pv_zspz_id
*       X_ZXY_ID       = ' '
*       _SCOPE         = '2'
*       _WAIT          = ' '
*       _COLLECT       = ' '
      EXCEPTIONS
        foreign_lock   = 1
        system_failure = 2
        OTHERS         = 3.

    IF sy-subrc NE 0.
      lv_msgv1 =  sy-msgv1.
      MESSAGE s888(sabapdocu) WITH  '用户' lv_msgv1 '正在处理' DISPLAY LIKE 'E'.
      LEAVE LIST-PROCESSING.
    ENDIF.
  ELSE.
    CALL FUNCTION 'DEQUEUE_EZ_ZRET0009'
      EXPORTING
        zspz_id = pv_zspz_id.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_DATA_9909
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- GS_T21
*&---------------------------------------------------------------------*
FORM frm_set_data_9909  CHANGING ps_t21 TYPE ty_t21.

  SELECT SINGLE zcjms    FROM zmmt0039    WHERE zcj = @ps_t21-zcj
    INTO @ps_t21-zcjms.

  IF ps_t21-zcj = 'ALL'.
    ps_t21-zcjms = '全部'.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_ZJT_FROM_NEW
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GT_T11
*&---------------------------------------------------------------------*
FORM frm_check_zjt_from_new  USING    pt_t11 TYPE tt_t11
                                      ps_t10  TYPE ty_t10.

  DATA:
    ls_t11 TYPE LINE OF tt_t11,
    lv_num TYPE i.

  DATA(lt_t11) = pt_t11[].

  DELETE lt_t11 WHERE zjt_from IS INITIAL.


  DATA(lv_lines1) = lines( lt_t11 ).
  SORT lt_t11 BY zjt_from .
  DELETE ADJACENT DUPLICATES FROM lt_t11 COMPARING zjt_from.
  DATA(lv_lines2) = lines( lt_t11 ).
  IF lv_lines1 <> lv_lines2.
    MESSAGE e888(sabapdocu) WITH '起始值存在相同的记录！'.
  ENDIF.

*  IF ps_t10-zjsff = 'P'.
*    LOOP AT pt_t11 INTO ls_t11 WHERE zjt_fm = 0.
*      MESSAGE e888(sabapdocu) WITH '当返利协议计算方法为比例时,分母必须大于0'.
*    ENDLOOP.
*  ENDIF.




*  ls_t11-mandt = ''.
*  MODIFY lt_t11 FROM ls_t11 TRANSPORTING mandt WHERE mandt IS NOT INITIAL.
*
*  SORT lt_t11 BY zjt_from ASCENDING.
*  LOOP AT lt_t11 INTO ls_t11.
*    AT NEW zjt_from.
*      CLEAR lv_num.
*    ENDAT.
*    lv_num =  lv_num + 1.
*    AT END OF zjt_from.
*      IF lv_num > 1.
*        MESSAGE e888(sabapdocu) WITH '起始值存在相同的记录！'.
*      ENDIF.
*    ENDAT.
*  ENDLOOP.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_DATA_FROM
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_T11
*&---------------------------------------------------------------------*
FORM frm_check_data_from  USING    ps_t11 TYPE ty_t11.
  IF ps_t11-zjt_from IS INITIAL AND
*     ps_t11-zjt_by IS NOT INITIAL AND
     ps_t11-zflg_1st = ''.
    MESSAGE e888(sabapdocu) WITH '起始值不允许改为0！'.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_AUTHOR_CHECK
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_author_check .
  DATA:lv_subrc TYPE c,
       lv_mess  TYPE bapiret2-message.
  DATA:
    lv_flg_err1 TYPE char1.

  DATA:
        ls_zret0006 TYPE zret0006.

  RANGES:
    s_bukrs FOR t001-bukrs.


  CASE 'X'.
    WHEN rb_add.    gv_actvt = '01'.
    WHEN rb_edit.    gv_actvt = '02'.
    WHEN rb_dis.    gv_actvt = '03'.
    WHEN rb_rles.    gv_actvt = '04'.
    WHEN OTHERS.
  ENDCASE.


  IF rb_add = 'X'.
    APPEND VALUE #( sign  = 'I'
                    option = 'EQ'
*                    low  = p_zflsqf
                    low  = p_zbukrs
                    high  = ''
                  ) TO s_bukrs .
    ls_zret0006-zhtlx = p_zhtlx.
  ELSE.
*    SELECT SINGLE zflsqf FROM zret0006
*      WHERE zxy_id = @p_zxy_id
*      INTO @DATA(lv_zflsqf).
*    APPEND VALUE #( sign  = 'I'
*                    option = 'EQ'
*                    low  = lv_zflsqf
*                    high  = ''
*                  ) TO s_bukrs .

*    SELECT SINGLE zbukrs ,ekgrp FROM zret0006
*      WHERE zxy_id = @p_zxy_id
*      INTO ( @DATA(lv_zbukrs) ,@DATA(lv_ekorg) ).
    SELECT SINGLE * INTO @ls_zret0006 FROM zret0006
      WHERE zxy_id = @p_zxy_id.
    IF sy-subrc NE 0.
      MESSAGE s888(sabapdocu) WITH '该返利协议不存在!' DISPLAY LIKE 'E'.
      LEAVE LIST-PROCESSING.
      RETURN.
    ENDIF.

    APPEND VALUE #( sign  = 'I'
                    option = 'EQ'
                    low  = ls_zret0006-zbukrs
                    high  = ''
                  ) TO s_bukrs .


    PERFORM frm_author_check_ekgrp USING ls_zret0006-ekgrp
                                         gv_actvt
                                   CHANGING lv_flg_err1.

    IF lv_flg_err1 = 'E'.
      MESSAGE s888(sabapdocu) WITH '您没有采购组:' && ls_zret0006-ekgrp && ' 的操作权限 !' DISPLAY LIKE 'E'.
      LEAVE LIST-PROCESSING.
    ENDIF.


  ENDIF.

  CLEAR:lv_subrc,
        lv_mess.

  CALL FUNCTION 'ZBCFM0001'
    EXPORTING
*     iv_object = 'J_B_BUKRS'
      iv_object = 'ZMMAR005'
      iv_field  = 'BUKRS'
      iv_actvt  = gv_actvt
    IMPORTING
      ex_subrc  = lv_subrc
      ex_mess   = lv_mess
    TABLES
      it_tab    = s_bukrs.

  IF lv_subrc = 'E'.
    MESSAGE lv_mess TYPE 'S' DISPLAY LIKE lv_subrc.
    LEAVE LIST-PROCESSING.
  ENDIF.


  PERFORM frm_author_check_zhtlx USING ls_zret0006-zhtlx
                                       gv_actvt
                                 CHANGING lv_flg_err1.

  IF lv_flg_err1 = 'E'.
    MESSAGE s888(sabapdocu) WITH '您没有合同类型:' && ls_zret0006-zhtlx && ' 的操作权限 !' DISPLAY LIKE 'E'.
    LEAVE LIST-PROCESSING.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_DATA_CHECK_COMT
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PS_T06
*&      --> PS_T10
*&      --> PT_T07
*&      --> PT_BUKRS
*&      --> PT_DCWRK
*&      --> PT_WERKS
*&      --> PT_T12
*&      --> PT_T21
*&      <-- PV_FLG_ERR
*&---------------------------------------------------------------------*
FORM frm_data_check_comt USING     ps_t06      TYPE ty_t06
                                    pt_t07      TYPE tt_t07
                                    pt_t08      TYPE tt_t08
                        CHANGING    pv_flg_err  TYPE char1.

  DATA:
    lt_msglist TYPE scp1_general_errors,
    ls_msglist TYPE scp1_general_error.

  DATA:
    lt_t07_tmp TYPE tt_t07,
    lt_t08_tmp TYPE tt_t08.


  CLEAR:
        lt_msglist.
  IF pv_flg_err NE 'E'.
    pv_flg_err = 'S'.
  ENDIF.

  ls_msglist-msgty = 'E'.
  ls_msglist-msgid = '00'.
  ls_msglist-msgno = '001'.

*F  固定金额
*Q  固定数量
*V  累计计算

  CASE gv_zxybstyp.
    WHEN 'V' OR 'T'.
      lt_t07_tmp[] = pt_t07[].
      DELETE lt_t07_tmp WHERE zspwd IS INITIAL.
      IF lt_t07_tmp[] IS INITIAL.
        pv_flg_err = 'E'.
        ls_msglist-msgv1 = '商品页签数据必填!'.
        APPEND ls_msglist TO lt_msglist.
      ENDIF.

      IF lt_t07_tmp[] IS NOT INITIAL.
        LOOP AT lt_t07_tmp TRANSPORTING NO FIELDS WHERE zspwd = 'C' AND zspz_id IS INITIAL.
          EXIT.
        ENDLOOP.
        IF sy-subrc EQ 0.
          pv_flg_err = 'E'.
          ls_msglist-msgv1 = '商品维度是商品组时，商品组必填!'.
          APPEND ls_msglist TO lt_msglist.
        ENDIF.

        LOOP AT lt_t07_tmp TRANSPORTING NO FIELDS WHERE zspwd = 'S' AND matnr IS INITIAL.
          EXIT.
        ENDLOOP.
        IF sy-subrc EQ 0.
          pv_flg_err = 'E'.
          ls_msglist-msgv1 = '商品维度是商品时，商品编码必填!'.
          APPEND ls_msglist TO lt_msglist.
        ENDIF.
      ENDIF.

    WHEN 'F'.
      lt_t08_tmp[] = pt_t08[].
      DELETE lt_t08_tmp WHERE zje IS INITIAL.
      IF lt_t08_tmp[] IS INITIAL.
        pv_flg_err = 'E'.
        ls_msglist-msgv1 = '明细数据必填!'.
        APPEND ls_msglist TO lt_msglist.
      ENDIF.

      IF lt_t08_tmp[] IS NOT INITIAL.
        LOOP AT lt_t08_tmp TRANSPORTING NO FIELDS WHERE zje IS INITIAL OR  zmwskz IS INITIAL.
          EXIT.
        ENDLOOP.
        IF sy-subrc EQ 0.
          pv_flg_err = 'E'.
          ls_msglist-msgv1 = '金额和税码必填!'.
          APPEND ls_msglist TO lt_msglist.
        ENDIF.
      ENDIF.
    WHEN 'Q'.
      lt_t08_tmp[] = pt_t08[].
      DELETE lt_t08_tmp WHERE matnr IS INITIAL.
      IF lt_t08_tmp[] IS INITIAL.
        pv_flg_err = 'E'.
        ls_msglist-msgv1 = '商品页签数据必填!'.
        APPEND ls_msglist TO lt_msglist.
      ENDIF.

      IF lt_t08_tmp[] IS NOT INITIAL.
        LOOP AT lt_t08_tmp TRANSPORTING NO FIELDS WHERE zsl IS INITIAL OR  matnr IS INITIAL.
          EXIT.
        ENDLOOP.
        IF sy-subrc EQ 0.
          pv_flg_err = 'E'.
          ls_msglist-msgv1 = '数量和商品编码必填!'.
          APPEND ls_msglist TO lt_msglist.
        ENDIF.

      ENDIF.
    WHEN OTHERS.
  ENDCASE.



  IF pv_flg_err = 'E' AND lt_msglist[] IS NOT INITIAL.
    CALL FUNCTION 'SCPR_SV_SHOW_MESSAGE_LIST'
      EXPORTING
        title_text    = '消息提示'
        sort_by_level = ' '
        show_ids      = ''
        message_list  = lt_msglist[].
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_SCREEN_9132
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_set_screen_9132 .
  PERFORM frm_set_screen_all.
ENDFORM.
FORM frm_set_screen_9133 .
  PERFORM frm_set_screen_all.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_SCREEN_9903
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_set_screen_9903 .
  PERFORM frm_set_screen_all.
ENDFORM.
FORM frm_set_screen_9917 .
  PERFORM frm_set_screen_all.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_SCREEN_9904
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_set_screen_9904 .
  PERFORM frm_set_screen_all.

ENDFORM.
FORM frm_set_screen_9918 .
  PERFORM frm_set_screen_all.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_SCREEN_9905
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_set_screen_9905 .

  PERFORM frm_set_screen_all.
ENDFORM.
FORM frm_set_screen_9919 .

  PERFORM frm_set_screen_all.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_SCREEN_9906
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_set_screen_9906 .
  PERFORM frm_set_screen_all.
ENDFORM.
FORM frm_set_screen_9920 .
  PERFORM frm_set_screen_all.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_SCREEN_9907
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_set_screen_9907 .

  PERFORM frm_set_screen_all.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_SCREEN_9908
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_set_screen_9908 .
  PERFORM frm_set_screen_all.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_SCREEN_9909
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_set_screen_9909 .

  PERFORM frm_set_screen_all.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_SCREEN_9911
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_set_screen_9911 .

  PERFORM frm_set_screen_all.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_SCREEN_9912
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_set_screen_9912 .

  PERFORM frm_set_screen_all.
ENDFORM.
FORM frm_set_screen_9921 .

  PERFORM frm_set_screen_all.
ENDFORM.
FORM frm_set_screen_9922 .

  PERFORM frm_set_screen_all.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_SCREEN_9914
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_set_screen_9914 .

  PERFORM frm_set_screen_all.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_DATA_9230
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- GT_T08
*&---------------------------------------------------------------------*
FORM frm_set_data_9230  CHANGING pt_t08 TYPE tt_t08.

  LOOP AT pt_t08 INTO DATA(ls_t08).
    IF ls_t08-lifnr IS INITIAL.
      ls_t08-lifnr = p_zflzff.
    ENDIF.
    MODIFY pt_t08 FROM ls_t08.
  ENDLOOP.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_AUTHOR_CHECK_EKGRP
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_T06_EKGRP
*&---------------------------------------------------------------------*
FORM frm_author_check_zhtlx  USING    pv_zhtlx TYPE zret0006-zhtlx
                                       pv_actvt TYPE activ_auth
                             CHANGING  pv_flg_err TYPE char1.

  CLEAR pv_flg_err.

  AUTHORITY-CHECK OBJECT 'ZREAR002'
                      ID 'ZHTLX' FIELD pv_zhtlx
                      ID 'ACTVT' FIELD pv_actvt.
  IF sy-subrc NE 0.
    pv_flg_err = 'E'.
  ELSE.
    pv_flg_err = 'S'.
  ENDIF.

ENDFORM.

*&---------------------------------------------------------------------*
*& Form FRM_AUTHOR_CHECK_EKGRP
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_T06_EKGRP
*&---------------------------------------------------------------------*
FORM frm_author_check_ekgrp  USING    pv_ekgrp TYPE ekgrp
                                       pv_actvt TYPE activ_auth
                             CHANGING  pv_flg_err TYPE char1.



  DATA:lv_subrc TYPE c,
       lv_mess  TYPE bapiret2-message.

*  RANGES:
*    s_ekgrp FOR t024-ekgrp.

  DATA:
        s_ekgrp TYPE TABLE OF zbcs0003.

  CLEAR pv_flg_err.
  IF pv_ekgrp IS NOT INITIAL.

    APPEND VALUE #( sign  = 'I'
                    option = 'EQ'
                    low  = pv_ekgrp
                    high  = ''
                  ) TO s_ekgrp .

    CLEAR:lv_subrc,
          lv_mess.

    CALL FUNCTION 'ZBCFM0001'
      EXPORTING
*       iv_object = 'M_BEST_EKG'
        iv_object = 'ZREAR001'
        iv_field  = 'EKGRP'
        iv_actvt  = pv_actvt
      IMPORTING
        ex_subrc  = lv_subrc
        ex_mess   = lv_mess
      TABLES
        it_tab    = s_ekgrp.

    IF lv_subrc = 'E'.
      pv_flg_err = 'E'.
    ELSE.
      pv_flg_err = 'S'.
    ENDIF.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_BEFORE_CANCEL
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_T06
*&      <-- LV_FLG_ERR
*&---------------------------------------------------------------------*
FORM frm_check_before_cancel  USING    ps_t06     TYPE ty_t06
                              CHANGING  pv_flg_err TYPE char1.

  PERFORM frm_check_before_rles_c      USING    ps_t06
                                       CHANGING pv_flg_err.
  IF pv_flg_err = 'S'.

    PERFORM frm_author_check_ekgrp USING ps_t06-ekgrp
                                         '02'
                                   CHANGING pv_flg_err.
    IF pv_flg_err = 'E'.
      MESSAGE s888(sabapdocu) WITH '您没有采购组:' && ps_t06-ekgrp && ' 的操作权限 !' DISPLAY LIKE 'E'.
    ELSE.
      PERFORM frm_author_check_zhtlx USING ps_t06-zhtlx
                                           '02'
                                     CHANGING pv_flg_err.

      IF pv_flg_err = 'E'.
        MESSAGE s888(sabapdocu) WITH '您没有合同类型:' && ps_t06-zhtlx && ' 的操作权限 !' DISPLAY LIKE 'E'.
      ELSE.
        PERFORM frm_author_check_zbukrs USING ps_t06-zbukrs
                                             '02'
                                       CHANGING pv_flg_err.

        IF pv_flg_err = 'E'.
          MESSAGE s888(sabapdocu) WITH '您没有公司代码:' && ps_t06-zbukrs && ' 的操作权限 !' DISPLAY LIKE 'E'.
        ENDIF.
      ENDIF.
    ENDIF.


  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_AUTHOR_CHECK_ZBUKRS
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PS_T06_ZBUKRS
*&      --> P_
*&      <-- PV_FLG_ERR
*&---------------------------------------------------------------------*
FORM frm_author_check_zbukrs  USING    pv_zbukrs TYPE zret0006-zbukrs
                                       pv_actvt TYPE activ_auth
                             CHANGING  pv_flg_err TYPE char1.

  CLEAR pv_flg_err.

  AUTHORITY-CHECK OBJECT 'ZMMAR005'
                      ID 'BUKRS' FIELD pv_zbukrs
                      ID 'ACTVT' FIELD pv_actvt.
  IF sy-subrc NE 0.
    pv_flg_err = 'E'.
  ELSE.
    pv_flg_err = 'S'.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_AUTHOR_CHECK_CANCEL
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_T06
*&      --> P_
*&      <-- LV_FLG_ERR
*&---------------------------------------------------------------------*
FORM frm_author_check_cancel  USING    ps_t06     TYPE ty_t06
                                       pv_actvt TYPE activ_auth
                             CHANGING  pv_flg_err TYPE char1.

  PERFORM frm_author_check_ekgrp USING ps_t06-ekgrp
                                       pv_actvt
                                 CHANGING pv_flg_err.
  IF pv_flg_err = 'E'.
*      MESSAGE s888(sabapdocu) WITH '您没有采购组:' && ps_t06-ekgrp && ' 的操作权限 !' DISPLAY LIKE 'E'.
  ELSE.
    PERFORM frm_author_check_zhtlx USING ps_t06-zhtlx
                                         pv_actvt
                                   CHANGING pv_flg_err.

    IF pv_flg_err = 'E'.
*        MESSAGE s888(sabapdocu) WITH '您没有合同类型:' && ps_t06-zhtlx && ' 的操作权限 !' DISPLAY LIKE 'E'.
    ELSE.
      PERFORM frm_author_check_zbukrs USING ps_t06-zbukrs
                                           pv_actvt
                                     CHANGING pv_flg_err.

      IF pv_flg_err = 'E'.
*          MESSAGE s888(sabapdocu) WITH '您没有公司代码:' && ps_t06-zbukrs && ' 的操作权限 !' DISPLAY LIKE 'E'.
      ENDIF.
    ENDIF.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_REF
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> P_ZXYREF
*&---------------------------------------------------------------------*
FORM frm_check_ref  USING pv_zxy_id TYPE zret0006-zxy_id.
  IF pv_zxy_id IS INITIAL.
    MESSAGE s888(sabapdocu) WITH '参考协议号码必填' DISPLAY LIKE 'E'.
    LEAVE LIST-PROCESSING.
  ELSE.
    SELECT SINGLE * INTO @DATA(lv_zt06)
      FROM zret0006
      WHERE zxy_id = @pv_zxy_id.
    IF sy-subrc NE 0.
      MESSAGE s888(sabapdocu) WITH '参考协议号码不存在！' DISPLAY LIKE 'E'.
      LEAVE LIST-PROCESSING.
    ENDIF.

    IF lv_zt06-zxyzt NE 'A' AND lv_zt06-zxyzt NE 'P'.
      MESSAGE s888(sabapdocu) WITH '仅能复制审批中或已审批的协议!' DISPLAY LIKE 'E'.
      LEAVE LIST-PROCESSING.
    ELSE.
      IF lv_zt06-zfllx NE p_zfllx.
        MESSAGE s888(sabapdocu) WITH '原协议类型是' lv_zt06-zfllx '与新的返利类型不一致，请检查!' DISPLAY LIKE 'E'.
        LEAVE LIST-PROCESSING.
      ENDIF.
    ENDIF.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_PRO_DATA_REF
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_pro_data_ref .

  DATA:
    ls_t06 TYPE ty_t06,
    ls_t07 TYPE ty_t07,
    ls_t39 TYPE ty_t39,
    ls_t08 TYPE ty_t08,
    ls_t10 TYPE ty_t10,
    ls_t11 TYPE ty_t11,
    ls_t12 TYPE ty_t12,
    ls_t13 TYPE ty_t13,
    ls_t38 TYPE ty_t38,
    ls_t14 TYPE ty_t14,
    ls_t37 TYPE ty_t37,
    ls_t15 TYPE ty_t15,
    ls_t16 TYPE ty_t16,
    ls_t21 TYPE ty_t21,
    ls_t44 TYPE ty_t44.

  DATA:
        lv_str TYPE char50.
  DATA:   lt_split_table TYPE TABLE OF string.

  gs_t06-zxy_id = ''.
  gs_t06-zjt_id = ''.
  gs_t06-zxyzt = 'N'.
  gs_t06-zflzff       = p_zflzff.
  gs_t06-zbukrs       = p_zbukrs.


  SPLIT gs_t06-zxy_txt AT '-' INTO TABLE lt_split_table.

  gs_t06-zxy_txt = ''.
  LOOP AT lt_split_table INTO DATA(ls_split_table).

    DATA(zxy_txt1) = ls_split_table.

    AT FIRST.
      gs_t06-zxy_txt = zxy_txt1.
      DATA(lv_flg) = 'X'.
    ENDAT.

    AT LAST.
      lv_flg = 'X'.
      gs_t06-zxy_txt = gs_t06-zxy_txt && '-'  && gs_t06-zbukrs.
    ENDAT.
    IF lv_flg <> 'X'.
      gs_t06-zxy_txt = gs_t06-zxy_txt && '-' && zxy_txt1.
    ENDIF.

    lv_flg = ''.
  ENDLOOP.
  CONDENSE gs_t06-zxy_txt.

*  gs_t06-zxy_txt = gs_t06-zxy_txt && '-'  && gs_t06-zbukrs.

  gs_t06-zxy_id = ''.
  gs_t06-zcjrq = ''.
  gs_t06-zcjsj = ''.
  gs_t06-zcjr = ''.
  gs_t06-zxgrq = ''.
  gs_t06-zxgsj = ''.
  gs_t06-zxgr = ''.
  gs_t06-zsprq = ''.
  gs_t06-zspsj = ''.
  gs_t06-zspr = ''.
  gs_t06-zqxrq = ''.
  gs_t06-zqxsj = ''.
  gs_t06-zqxr = ''.
  gs_t06-zmtpe = ''.


*  ls_t06-zxy_id = ''.
*  ls_t06-zjsbs   = ''.
*  ls_t06-zcjrq = ''.
*  ls_t06-zcjsj = ''.
*  ls_t06-zcjr = ''.
*  ls_t06-zxgrq = ''.
*  ls_t06-zxgsj = ''.
*  ls_t06-zxgr = ''.
*  ls_t06-zsprq = ''.
*  ls_t06-zspsj = ''.
*  ls_t06-zspr = ''.
*  ls_t06-zqxrq = ''.
*  ls_t06-zqxsj = ''.
*  ls_t06-zqxr = ''.
*  ls_t06-zmtpe = ''.
  MODIFY gt_t06 FROM gs_t06
    TRANSPORTING
                zxy_id
                zjsbs
                zcjrq
                zcjsj
                zcjr
                zxgrq
                zxgsj
                zxgr
                zsprq
                zspsj
                zspr
                zqxrq
                zqxsj
                zqxr
    WHERE zxy_id NE ''.

  ls_t07-zxy_id = ''.
  ls_t07-zxy_itemid = ''.
  MODIFY gt_t07 FROM ls_t07 TRANSPORTING zxy_id zxy_itemid WHERE zxy_id NE ''.

  ls_t39-zxy_id = ''.
  ls_t39-zxy_itemid = ''.
  MODIFY gt_t39 FROM ls_t39 TRANSPORTING zxy_id zxy_itemid WHERE zxy_id NE ''.

  ls_t08-zxy_id = ''.
  ls_t08-zxy_itemid = ''.
  MODIFY gt_t08 FROM ls_t08 TRANSPORTING zxy_id zxy_itemid WHERE zxy_id NE ''.

  gs_t10-zjt_id = ''.

  ls_t10-zjt_id = ''.
  MODIFY gt_t10 FROM ls_t10 TRANSPORTING zjt_id  WHERE zjt_id NE ''.

  ls_t11-zjt_id = ''.
  ls_t11-zjt_itemid = ''.
  MODIFY gt_t11 FROM ls_t11 TRANSPORTING zjt_id zjt_itemid WHERE zjt_id NE ''.

  ls_t12-zxy_id = ''.
  MODIFY gt_t12 FROM ls_t12 TRANSPORTING zxy_id  WHERE zxy_id NE ''.

  ls_t13-zxy_id = ''.
  MODIFY gt_t13 FROM ls_t13 TRANSPORTING zxy_id  WHERE zxy_id NE ''.

  ls_t38-zxy_id = ''.
  MODIFY gt_t38 FROM ls_t38 TRANSPORTING zxy_id  WHERE zxy_id NE ''.

  ls_t14-zxy_id = ''.
  MODIFY gt_t14 FROM ls_t14 TRANSPORTING zxy_id  WHERE zxy_id NE ''.

  ls_t37-zxy_id = ''.
  MODIFY gt_t37 FROM ls_t37 TRANSPORTING zxy_id  WHERE zxy_id NE ''.

  ls_t15-zxy_id = ''.
  MODIFY gt_t15 FROM ls_t15 TRANSPORTING zxy_id  WHERE zxy_id NE ''.

  ls_t16-zxy_id = ''.
  ls_t16-zjsbs = ''.
  MODIFY gt_t16 FROM ls_t16 TRANSPORTING zxy_id zjsbs WHERE zxy_id NE ''.

  ls_t21-zxy_id = ''.
  MODIFY gt_t21 FROM ls_t21 TRANSPORTING zxy_id  WHERE zxy_id NE ''.

  ls_t44-zxy_id = ''.
  MODIFY gt_t44 FROM ls_t44 TRANSPORTING zxy_id  WHERE zxy_id NE ''.


  CLEAR gt_data_9125.
  CLEAR gt_data_9125[].

*  核算子阶梯需要生成新的号码
  PERFORM frm_pro_hszjt CHANGING gt_t15
                                 gt_t10_hs
                                 gt_t11_hs.

*  返利计算子阶梯需要生成新的号码
  PERFORM frm_pro_jszjt CHANGING gt_t11
                                 gt_t10_js
                                 gt_t11_js.

*  核算周期返利计算子阶梯需要生成新的号码
  PERFORM frm_pro_jszjt CHANGING gt_t11_hs
                                 gt_t10_jsh
                                 gt_t11_jsh.

****  LOOP AT gt_t10_hs INTO ls_t10.
*****     获取阶梯编号
****    IF ls_t10-zjt_id IS NOT INITIAL.
****      PERFORM frm_get_zjt_id CHANGING ls_t10-zjt_id_tmp.
****      IF ls_t10-zjt_id_tmp IS INITIAL .
****        MESSAGE s888(sabapdocu) WITH '获取新的阶梯号码失败！' DISPLAY LIKE 'E'.
****        LEAVE LIST-PROCESSING.
****        RETURN.
****      ENDIF.
****    ENDIF.
****    MODIFY gt_t10_hs FROM ls_t10 TRANSPORTING zjt_id_tmp.
****  ENDLOOP.
****
****  SORT gt_t10_hs BY zjt_id.
****  LOOP AT gt_t11_hs INTO ls_t11.
****    READ TABLE gt_t10_hs INTO ls_t10 WITH KEY zjt_id = ls_t11-zjt_id BINARY SEARCH.
****    IF sy-subrc EQ 0.
****      ls_t11-zjt_id = ls_t10-zjt_id_tmp.
****    ENDIF.
****    MODIFY gt_t11_hs FROM ls_t11 TRANSPORTING zjt_id.
****  ENDLOOP.
****
****  LOOP AT gt_t15 INTO ls_t15.
****    READ TABLE gt_t10_hs INTO ls_t10 WITH KEY zjt_id = ls_t15-zjt_id BINARY SEARCH.
****    IF sy-subrc EQ 0.
****      ls_t15-zjt_id = ls_t10-zjt_id_tmp.
****    ENDIF.
****    MODIFY gt_t15 FROM ls_t15 TRANSPORTING zjt_id.
****
****  ENDLOOP.
****
****  LOOP AT gt_t10_hs INTO ls_t10.
****    ls_t10-zjt_id = ls_t10-zjt_id_tmp.
****    MODIFY gt_t10_hs FROM ls_t10 TRANSPORTING zjt_id.
****  ENDLOOP.

*  商品组处理
  CLEAR gs_t09.
*  新的商品组
  IF rb_spz_n = 'X'.
    PERFORM frm_pro_spzid CHANGING gt_t09
                                   gt_t20
                                   gt_t07
                                   gt_t39.
**
**    LOOP AT gt_t09 INTO DATA(ls_t09).
***     获取商品组编号
**      IF ls_t09-zspz_id IS NOT INITIAL.
**        PERFORM frm_get_zspz_id         CHANGING   ls_t09-zspz_id_tmp.
**        IF ls_t09-zspz_id_tmp IS INITIAL .
**          MESSAGE s888(sabapdocu) WITH '获取新的商品组号码失败！' DISPLAY LIKE 'E'.
**          LEAVE LIST-PROCESSING.
**          RETURN.
**        ENDIF.
**      ENDIF.
**      MODIFY gt_t09 FROM ls_t09 TRANSPORTING zspz_id_tmp.
**    ENDLOOP.
**
**    SORT gt_t09 BY zspz_id.
**    LOOP AT gt_t20 INTO DATA(ls_t20).
**      READ TABLE gt_t09 INTO ls_t09 WITH KEY zspz_id = ls_t20-zspz_id BINARY SEARCH.
**      IF sy-subrc EQ 0.
**        ls_t20-zspz_id = ls_t09-zspz_id_tmp.
**      ENDIF.
**      MODIFY gt_t20 FROM ls_t20 TRANSPORTING zspz_id.
**    ENDLOOP.
**
**    SORT gt_t09 BY zspz_id.
**    LOOP AT gt_t07 INTO ls_t07.
**      READ TABLE gt_t09 INTO ls_t09 WITH KEY zspz_id = ls_t07-zspz_id BINARY SEARCH.
**      IF sy-subrc EQ 0.
**        ls_t07-zspz_id = ls_t09-zspz_id_tmp.
**        ls_t07-zspzid_txt = ls_t09-zspzid_txt.
**      ENDIF.
**      MODIFY gt_t07 FROM ls_t07 TRANSPORTING zspz_id zspzid_txt.
**    ENDLOOP.
**
**    LOOP AT gt_t09 INTO ls_t09.
**      ls_t09-zspz_id = ls_t09-zspz_id_tmp.
**      MODIFY gt_t09 FROM ls_t09 TRANSPORTING zspz_id.
**    ENDLOOP.

  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_DATA_LISTBOX
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- CB_SPLIT
*&      <-- LT_VLS_ZJSFF
*&---------------------------------------------------------------------*
FORM frm_set_data_listbox  CHANGING pv_cb_split  TYPE char1
                                    pt_vls_zjsff  TYPE vrm_values.

  IF pv_cb_split = ''.
    DELETE pt_vls_zjsff WHERE key = 'T'.
  ELSE.
    DELETE pt_vls_zjsff WHERE key = 'R'.
  ENDIF.


ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_PRO_HSZJT
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- GT_T15
*&      <-- GT_T10_HS
*&      <-- GT_T11_HS
*&---------------------------------------------------------------------*
FORM frm_pro_hszjt  CHANGING pt_t15 TYPE tt_t15
                             pt_t10_hs  TYPE tt_t10
                             pt_t11_hs  TYPE tt_t11.
  DATA:
    ls_t15 TYPE LINE OF tt_t15,
    ls_t10 TYPE LINE OF tt_t10,
    ls_t11 TYPE LINE OF tt_t11.

  LOOP AT pt_t10_hs INTO ls_t10.
*     获取阶梯编号
    IF ls_t10-zjt_id IS NOT INITIAL.
      PERFORM frm_get_zjt_id CHANGING ls_t10-zjt_id_tmp.
      IF ls_t10-zjt_id_tmp IS INITIAL .
        MESSAGE s888(sabapdocu) WITH '获取新的阶梯号码失败！' DISPLAY LIKE 'E'.
        LEAVE LIST-PROCESSING.
        RETURN.
      ENDIF.
    ENDIF.
    MODIFY pt_t10_hs FROM ls_t10 TRANSPORTING zjt_id_tmp.
  ENDLOOP.

  SORT pt_t10_hs BY zjt_id.
  LOOP AT pt_t11_hs INTO ls_t11.
    READ TABLE pt_t10_hs INTO ls_t10 WITH KEY zjt_id = ls_t11-zjt_id BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_t11-zjt_id = ls_t10-zjt_id_tmp.
    ENDIF.
    MODIFY pt_t11_hs FROM ls_t11 TRANSPORTING zjt_id.
  ENDLOOP.

  LOOP AT pt_t15 INTO ls_t15.
    READ TABLE pt_t10_hs INTO ls_t10 WITH KEY zjt_id = ls_t15-zjt_id BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_t15-zjt_id = ls_t10-zjt_id_tmp.
    ENDIF.
    MODIFY pt_t15 FROM ls_t15 TRANSPORTING zjt_id.

  ENDLOOP.

  LOOP AT pt_t10_hs INTO ls_t10.
    ls_t10-zjt_id = ls_t10-zjt_id_tmp.
    MODIFY pt_t10_hs FROM ls_t10 TRANSPORTING zjt_id.
  ENDLOOP.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_PRO_JSZJT
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- GT_T11
*&      <-- GT_T10_JS
*&      <-- GT_T11_JS
*&---------------------------------------------------------------------*
FORM frm_pro_jszjt  CHANGING pt_t11     TYPE tt_t11
                             pt_t10_js  TYPE tt_t10
                             pt_t11_js  TYPE tt_t11.

  DATA:
    ls_t11_m TYPE LINE OF tt_t11,
    ls_t10   TYPE LINE OF tt_t10,
    ls_t11   TYPE LINE OF tt_t11.

  LOOP AT pt_t10_js INTO ls_t10.
*     获取阶梯编号
    IF ls_t10-zjt_id IS NOT INITIAL.
      PERFORM frm_get_zjt_id CHANGING ls_t10-zjt_id_tmp.
      IF ls_t10-zjt_id_tmp IS INITIAL .
        MESSAGE s888(sabapdocu) WITH '获取新的阶梯号码失败！' DISPLAY LIKE 'E'.
        LEAVE LIST-PROCESSING.
        RETURN.
      ENDIF.
    ENDIF.
    MODIFY pt_t10_js FROM ls_t10 TRANSPORTING zjt_id_tmp.
  ENDLOOP.

  SORT pt_t10_js BY zjt_id.
  LOOP AT pt_t11_js INTO ls_t11.
    READ TABLE pt_t10_js INTO ls_t10 WITH KEY zjt_id = ls_t11-zjt_id BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_t11-zjt_id = ls_t10-zjt_id_tmp.
    ENDIF.
    MODIFY pt_t11_js FROM ls_t11 TRANSPORTING zjt_id.
  ENDLOOP.

  LOOP AT pt_t11 INTO ls_t11_m.
    READ TABLE pt_t10_js INTO ls_t10 WITH KEY zjt_id = ls_t11_m-zfljt_id BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_t11_m-zfljt_id = ls_t10-zjt_id_tmp.
    ENDIF.
    MODIFY pt_t11 FROM ls_t11_m TRANSPORTING zfljt_id .

  ENDLOOP.

  LOOP AT pt_t10_js INTO ls_t10.
    ls_t10-zjt_id = ls_t10-zjt_id_tmp.
    MODIFY pt_t10_js FROM ls_t10 TRANSPORTING zjt_id.
  ENDLOOP.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_PRO_SPZID
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- GT_T09
*&      <-- GT_T20
*&      <-- GT_T07
*&      <-- GT_T39
*&---------------------------------------------------------------------*
FORM frm_pro_spzid  CHANGING pt_t09 TYPE tt_t09
                             pt_t20 TYPE tt_t20
                             pt_t07 TYPE tt_t07
                             pt_t39 TYPE tt_t39.


  LOOP AT pt_t09 INTO DATA(ls_t09).
*     获取商品组编号
    IF ls_t09-zspz_id IS NOT INITIAL.
      PERFORM frm_get_zspz_id         CHANGING   ls_t09-zspz_id_tmp.
      IF ls_t09-zspz_id_tmp IS INITIAL .
        MESSAGE s888(sabapdocu) WITH '获取新的商品组号码失败！' DISPLAY LIKE 'E'.
        LEAVE LIST-PROCESSING.
        RETURN.
      ENDIF.
    ENDIF.
    MODIFY pt_t09 FROM ls_t09 TRANSPORTING zspz_id_tmp.
  ENDLOOP.

  SORT pt_t09 BY zspz_id.
  LOOP AT pt_t20 INTO DATA(ls_t20).
    READ TABLE pt_t09 INTO ls_t09 WITH KEY zspz_id = ls_t20-zspz_id BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_t20-zspz_id = ls_t09-zspz_id_tmp.
    ENDIF.
    MODIFY pt_t20 FROM ls_t20 TRANSPORTING zspz_id.
  ENDLOOP.

  SORT pt_t09 BY zspz_id.
  LOOP AT pt_t07 INTO DATA(ls_t07).
    READ TABLE pt_t09 INTO ls_t09 WITH KEY zspz_id = ls_t07-zspz_id BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_t07-zspz_id = ls_t09-zspz_id_tmp.
      ls_t07-zspzid_txt = ls_t09-zspzid_txt.
    ENDIF.
    MODIFY pt_t07 FROM ls_t07 TRANSPORTING zspz_id zspzid_txt.
  ENDLOOP.

  SORT pt_t09 BY zspz_id.
  LOOP AT pt_t39 INTO DATA(ls_t39).
    READ TABLE pt_t09 INTO ls_t09 WITH KEY zspz_id = ls_t07-zspz_id BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_t39-zspz_id = ls_t09-zspz_id_tmp.
      ls_t39-zspzid_txt = ls_t09-zspzid_txt.
    ENDIF.
    MODIFY pt_t39 FROM ls_t39 TRANSPORTING zspz_id zspzid_txt.
  ENDLOOP.

  LOOP AT pt_t09 INTO ls_t09.
    ls_t09-zspz_id = ls_t09-zspz_id_tmp.
    MODIFY pt_t09 FROM ls_t09 TRANSPORTING zspz_id.
  ENDLOOP.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_DATA_07
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- LS_T07
*&---------------------------------------------------------------------*
FORM frm_set_data_07  CHANGING ps_t07 TYPE LINE OF tt_t07.

  IF ps_t07-zspwd = 'C'.
    CLEAR:
       ps_t07-maktx,
       ps_t07-matnr,
       ps_t07-meins,
       ps_t07-zacpr,
       ps_t07-zrate,
       ps_t07-zpeinh.
  ELSEIF ps_t07-zspwd = 'S'.
    CLEAR:
       ps_t07-zspz_id,
       ps_t07-zspzid_txt.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_ITEM
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PT_T07
*&      <-- PV_FLG_ERR
*&      <-- LT_MSGLIST
*&---------------------------------------------------------------------*
FORM frm_check_item  USING    pt_t07  TYPE tt_t07
                               ps_t10 TYPE LINE OF tt_t10
                               pt_t20 TYPE tt_t20
                     CHANGING pv_flg_err  TYPE char1
                              pt_msglist  TYPE scp1_general_errors.
  DATA:
         ls_msglist  TYPE LINE OF scp1_general_errors.

  LOOP AT pt_t07 INTO DATA(ls_t07).
    IF ls_t07-zspwd = 'C' AND ls_t07-zspz_id IS INITIAL.
      pv_flg_err = 'E'.
      ls_msglist-msgv1 = '商品组时，商品组不能为空!'.
      APPEND ls_msglist TO pt_msglist.
    ELSEIF ls_t07-zspwd = 'S' AND ls_t07-matnr IS INITIAL.
      pv_flg_err = 'E'.
      ls_msglist-msgv1 = '单个商品时，商品号码不能为空!'.
      APPEND ls_msglist TO pt_msglist.
    ENDIF.

*    IF ls_t07-zspwd = 'A' AND gv_zxybstyp = 'T'.
*      pv_flg_err = 'E'.
*      ls_msglist-msgv1 = '协议类型为T时，商品维度不允许为A!'.
*      APPEND ls_msglist TO pt_msglist.
*    ENDIF.

    IF ps_t10-zjgwd = 'A' OR ps_t10-zfljgwd = 'A'  .
      IF ls_t07-zspwd = 'S' AND ls_t07-zacpr IS INITIAL AND ls_t07-matnr IS NOT INITIAL.
        pv_flg_err = 'E'.
        ls_msglist-msgv1 = '商品的核算价不能为0!'.
        APPEND ls_msglist TO pt_msglist.
      ENDIF.

      IF ls_t07-zspwd = 'C' AND ls_t07-zspz_id IS NOT INITIAL .
        SELECT COUNT(*) FROM zret0020 WHERE zspz_id = ls_t07-zspz_id
                                        AND matnr NE ''
                                        AND zacpr = 0.
        IF sy-subrc EQ 0.
          pv_flg_err = 'E'.
          ls_msglist-msgv1 = '商品组' && ls_t07-zspz_id && '的核算价不能为0!'.
          APPEND ls_msglist TO pt_msglist.
        ENDIF.
      ENDIF.
    ENDIF.
  ENDLOOP.

**  IF ps_t10-zjgwd = 'A' OR ps_t10-zfljgwd = 'A'  .
**    LOOP AT pt_t20 TRANSPORTING NO FIELDS WHERE zacpr IS INITIAL AND matnr IS NOT INITIAL .
**      pv_flg_err = 'E'.
**      ls_msglist-msgv1 = '商品的核算价不能为0!'.
**      APPEND ls_msglist TO pt_msglist.
**
**    ENDLOOP.
**  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_PRO_ZFF
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- PS_T06
*&      <-- PT_T44
*&---------------------------------------------------------------------*
FORM frm_pro_zff  CHANGING ps_t06 TYPE ty_t06
                           pt_t44 TYPE tt_t44.

  DATA:
        ls_t44 TYPE LINE OF tt_t44.

  ls_t44-zflzff = ps_t06-zflzff.
  INSERT ls_t44 INTO pt_t44 INDEX 1.

  DELETE pt_t44       WHERE zflzff        IS INITIAL.
  SORT pt_t44 BY zflzff.
  DELETE ADJACENT DUPLICATES FROM pt_t44 COMPARING zflzff.


ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_ZMTYPE
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PT_T44
*&      <-- PS_T06
*&---------------------------------------------------------------------*
FORM frm_set_zmtype  USING    pt_t44  TYPE tt_t44
                     CHANGING ps_t06  TYPE ty_t06.

  SELECT
    *
    FROM zret0003
    INTO TABLE @DATA(lt_zret0003).
  SORT lt_zret0003 BY zhsjz.


  IF lines( pt_t44 ) <= 1 .
    ps_t06-zmtpe = 'N'.
  ELSE.
    IF ps_t06-zflhsjz IS NOT INITIAL.
      READ TABLE lt_zret0003 INTO DATA(ls_zret0003) WITH KEY zhsjz = ps_t06-zflhsjz BINARY SEARCH.
      IF ls_zret0003-zsale = 'X' OR ls_zret0003-zdist = 'X'.
        ps_t06-zmtpe = 'VA'.
      ELSEIF ls_zret0003-zpur = 'X'.
        ps_t06-zmtpe = 'SA'.
      ENDIF.
    ELSEIF ps_t06-zhsjz IS NOT INITIAL.
      READ TABLE lt_zret0003 INTO ls_zret0003 WITH KEY zhsjz = ps_t06-zhsjz BINARY SEARCH.
      IF ls_zret0003-zsale = 'X' OR ls_zret0003-zdist = 'X'.
        ps_t06-zmtpe = 'VA'.
      ELSEIF ls_zret0003-zpur = 'X'.
        ps_t06-zmtpe = 'SA'.
      ENDIF.
    ELSEIF gv_zxybstyp = 'T'.
      ps_t06-zmtpe = 'SA'.
    ENDIF.
  ENDIF.

  CLEAR ls_zret0003.

ENDFORM.