*&---------------------------------------------------------------------*
*& Include ZRED0001_M08
*&---------------------------------------------------------------------*

*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_EKORG' ITSELF
CONTROLS: tc_ekorg TYPE TABLEVIEW USING SCREEN 9906.

*&SPWIZARD: LINES OF TABLECONTROL 'TC_EKORG'
DATA:     g_tc_ekorg_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_EKORG'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: <PERSON>DA<PERSON> LINES FOR EQUIVALENT SCROLLBAR
MODULE tc_ekorg_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_ekorg LINES tc_ekorg-lines.
ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_EKORG'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL
MODULE tc_ekorg_get_lines OUTPUT.
  g_tc_ekorg_lines = sy-loopc.

  PERFORM frm_set_screen_all.
  IF  gt_ekorg[] IS INITIAL.
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
*  ELSE.
*    READ TABLE gt_ekorg TRANSPORTING NO FIELDS INDEX TC_EKORG-current_line.
*    IF sy-subrc NE 0.
*      LOOP AT SCREEN.
*        screen-input = 0.
*        MODIFY SCREEN.
*      ENDLOOP.
*    ENDIF.
  ENDIF.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_EKORG'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE
MODULE tc_ekorg_modify INPUT.
  MODIFY gt_ekorg
    FROM gs_ekorg
    INDEX tc_ekorg-current_line.
ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_EKORG'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE
MODULE tc_ekorg_mark INPUT.
  DATA: g_tc_ekorg_wa2 LIKE LINE OF gt_ekorg.
  IF tc_ekorg-line_sel_mode = 1
  AND gs_ekorg-sel = 'X'.
    LOOP AT gt_ekorg INTO g_tc_ekorg_wa2
      WHERE sel = 'X'.
      g_tc_ekorg_wa2-sel = ''.
      MODIFY gt_ekorg
        FROM g_tc_ekorg_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_ekorg
    FROM gs_ekorg
    INDEX tc_ekorg-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_EKORG'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND
MODULE tc_ekorg_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_EKORG'
                              'GT_EKORG'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.


*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_EKORG' ITSELF
CONTROLS: tc_ekorg_n TYPE TABLEVIEW USING SCREEN 9920.

*&SPWIZARD: LINES OF TABLECONTROL 'TC_EKORG'
DATA:     g_tc_ekorg_n_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_EKORG'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: UPDATE LINES FOR EQUIVALENT SCROLLBAR
MODULE tc_ekorg_n_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_ekorg_n LINES tc_ekorg_n-lines.
ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_EKORG'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL
MODULE tc_ekorg_n_get_lines OUTPUT.
  g_tc_ekorg_n_lines = sy-loopc.

  PERFORM frm_set_screen_all.
  IF  gt_ekorg_n[] IS INITIAL.
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
*  ELSE.
*    READ TABLE gt_ekorg TRANSPORTING NO FIELDS INDEX TC_EKORG-current_line.
*    IF sy-subrc NE 0.
*      LOOP AT SCREEN.
*        screen-input = 0.
*        MODIFY SCREEN.
*      ENDLOOP.
*    ENDIF.
  ENDIF.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_EKORG'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE
MODULE tc_ekorg_n_modify INPUT.
  MODIFY gt_ekorg_n
    FROM gs_ekorg_n
    INDEX tc_ekorg_n-current_line.
ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_EKORG'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE
MODULE tc_ekorg_n_mark INPUT.
  DATA: g_tc_ekorg_n_wa2 LIKE LINE OF gt_ekorg_n.
  IF tc_ekorg_n-line_sel_mode = 1
  AND gs_ekorg_n-sel = 'X'.
    LOOP AT gt_ekorg_n INTO g_tc_ekorg_n_wa2
      WHERE sel = 'X'.
      g_tc_ekorg_n_wa2-sel = ''.
      MODIFY gt_ekorg_n
        FROM g_tc_ekorg_n_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_ekorg_n
    FROM gs_ekorg_n
    INDEX tc_ekorg_n-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_EKORG'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND
MODULE tc_ekorg_n_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_EKORG_N'
                              'GT_EKORG_Ns'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.



*&---------------------------------------------------------------------*
*& Module MDL_SET_TC_DATA_9906 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE mdl_set_tc_data_9906 OUTPUT.
  PERFORM frm_set_data_9006 CHANGING gs_ekorg.
ENDMODULE.
MODULE mdl_set_tc_data_9920 OUTPUT.
  PERFORM frm_set_data_9006 CHANGING gs_ekorg_n.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_CHECK_EKORG  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_check_ekorg INPUT.
  PERFORM frm_check_ekorg USING gs_ekorg-ekorg.
ENDMODULE.
MODULE mdl_check_ekorg_n INPUT.
  PERFORM frm_check_ekorg USING gs_ekorg_n-ekorg.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_SET_DATA_EKORG  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_set_data_ekorg INPUT.
*  若存在非ALL的数据，则自动删除ALL的数据
  LOOP AT gt_ekorg TRANSPORTING NO FIELDS WHERE ekorg IS NOT INITIAL AND ekorg NE 'ALL'.
    EXIT.
  ENDLOOP.
  IF sy-subrc EQ 0.
    DELETE gt_ekorg WHERE ekorg = 'ALL'.
  ENDIF.
ENDMODULE.
MODULE mdl_set_data_ekorg_n INPUT.
*  若存在非ALL的数据，则自动删除ALL的数据
  LOOP AT gt_ekorg_n TRANSPORTING NO FIELDS WHERE ekorg IS NOT INITIAL AND ekorg NE 'ALL'.
    EXIT.
  ENDLOOP.
  IF sy-subrc EQ 0.
    DELETE gt_ekorg_n WHERE ekorg = 'ALL'.
  ENDIF.
ENDMODULE.
*&---------------------------------------------------------------------*
*& Module STATUS_9906 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_9906 OUTPUT.
  PERFORM frm_set_screen_9906.
ENDMODULE.
MODULE status_9920 OUTPUT.
  PERFORM frm_set_screen_9920.
ENDMODULE.