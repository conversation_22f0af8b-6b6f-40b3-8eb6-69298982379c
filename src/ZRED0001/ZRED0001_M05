*&---------------------------------------------------------------------*
*& Include ZRED0001_M05
*&---------------------------------------------------------------------*

*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_BUKRS' ITSELF
CONTROLS: tc_bukrs TYPE TABLEVIEW USING SCREEN 9903.

*&SPWIZARD: LINES OF TABLECONTROL 'TC_BUKRS'
DATA:     g_tc_bukrs_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_BUKRS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: UPDA<PERSON> LINES FOR EQUIVALENT SCROLLBAR
MODULE tc_bukrs_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_bukrs LINES tc_bukrs-lines.
ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_BUKRS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL
MODULE tc_bukrs_get_lines OUTPUT.
  g_tc_bukrs_lines = sy-loopc.

  PERFORM frm_set_screen_all.
  IF  gt_bukrs[] IS INITIAL.
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
*  ELSE.
*    READ TABLE gt_bukrs TRANSPORTING NO FIELDS INDEX tc_bukrs-current_line.
*    IF sy-subrc NE 0.
*      LOOP AT SCREEN.
*        screen-input = 0.
*        MODIFY SCREEN.
*      ENDLOOP.
*    ENDIF.
  ENDIF.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_BUKRS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE
MODULE tc_bukrs_modify INPUT.
  MODIFY gt_bukrs
    FROM gs_bukrs
    INDEX tc_bukrs-current_line.
ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_BUKRS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE
MODULE tc_bukrs_mark INPUT.
  DATA: g_tc_bukrs_wa2 LIKE LINE OF gt_bukrs.
  IF tc_bukrs-line_sel_mode = 1
  AND gs_bukrs-sel = 'X'.
    LOOP AT gt_bukrs INTO g_tc_bukrs_wa2
      WHERE sel = 'X'.
      g_tc_bukrs_wa2-sel = ''.
      MODIFY gt_bukrs
        FROM g_tc_bukrs_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_bukrs
    FROM gs_bukrs
    INDEX tc_bukrs-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_BUKRS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND
MODULE tc_bukrs_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_BUKRS'
                              'GT_BUKRS'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.



*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_BUKRS' ITSELF
CONTROLS: tc_bukrs_n TYPE TABLEVIEW USING SCREEN 9917.

*&SPWIZARD: LINES OF TABLECONTROL 'TC_BUKRS'
DATA:     g_tc_bukrs_n_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_BUKRS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: UPDATE LINES FOR EQUIVALENT SCROLLBAR
MODULE tc_bukrs_n_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_bukrs_n LINES tc_bukrs_n-lines.
ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_BUKRS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL
MODULE tc_bukrs_n_get_lines OUTPUT.
  g_tc_bukrs_n_lines = sy-loopc.

  PERFORM frm_set_screen_all.
  IF  gt_bukrs_n[] IS INITIAL.
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
*  ELSE.
*    READ TABLE gt_bukrs TRANSPORTING NO FIELDS INDEX tc_bukrs-current_line.
*    IF sy-subrc NE 0.
*      LOOP AT SCREEN.
*        screen-input = 0.
*        MODIFY SCREEN.
*      ENDLOOP.
*    ENDIF.
  ENDIF.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_BUKRS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE
MODULE tc_bukrs_n_modify INPUT.
  MODIFY gt_bukrs_n
    FROM gs_bukrs_n
    INDEX tc_bukrs_n-current_line.
ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_BUKRS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE
MODULE tc_bukrs_n_mark INPUT.
  DATA: g_tc_bukrs_n_wa2 LIKE LINE OF gt_bukrs_n.
  IF tc_bukrs_n-line_sel_mode = 1
  AND gs_bukrs_n-sel = 'X'.
    LOOP AT gt_bukrs_n INTO g_tc_bukrs_n_wa2
      WHERE sel = 'X'.
      g_tc_bukrs_n_wa2-sel = ''.
      MODIFY gt_bukrs_n
        FROM g_tc_bukrs_n_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_bukrs_n
    FROM gs_bukrs_n
    INDEX tc_bukrs_n-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_BUKRS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND
MODULE tc_bukrs_n_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_BUKRS_N'
                              'GT_BUKRS_N'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.


*----------------------------------------------------------------------*
*   INCLUDE TABLECONTROL_FORMS                                         *
*----------------------------------------------------------------------*

*&---------------------------------------------------------------------*
*&      Form  USER_OK_TC                                               *
*&---------------------------------------------------------------------*
FORM user_ok_tc USING    p_tc_name TYPE dynfnam
                         p_table_name
                         p_mark_name
                CHANGING p_ok      LIKE sy-ucomm.

*&SPWIZARD: BEGIN OF LOCAL DATA----------------------------------------*
  DATA: l_ok     TYPE sy-ucomm,
        l_offset TYPE i.
*&SPWIZARD: END OF LOCAL DATA------------------------------------------*

*&SPWIZARD: Table control specific operations                          *
*&SPWIZARD: evaluate TC name and operations                            *
  SEARCH p_ok FOR p_tc_name.
  IF sy-subrc <> 0.
    EXIT.
  ENDIF.
  l_offset = strlen( p_tc_name ) + 1.
  l_ok = p_ok+l_offset.
*&SPWIZARD: execute general and TC specific operations                 *
  CASE l_ok.
    WHEN 'INSR'.                      "insert row

*      IF NOT (
*        rb_dis = 'X' OR
*         rb_rles = 'X' OR
*        gv_flg_comm = 'COMT' ).

      PERFORM fcode_insert_row USING    p_tc_name
                                        p_table_name.

*      ENDIF.
      CLEAR p_ok.

    WHEN 'DELE'.                      "delete row

*      IF NOT (
*        rb_dis = 'X' OR
*         rb_rles = 'X' OR
*        gv_flg_comm = 'COMT' ).


      PERFORM fcode_delete_row USING    p_tc_name
                                        p_table_name
                                        p_mark_name.
*      ENDIF.
      CLEAR p_ok.

    WHEN 'P--' OR                     "top of list
         'P-'  OR                     "previous page
         'P+'  OR                     "next page
         'P++'.                       "bottom of list
      PERFORM compute_scrolling_in_tc USING p_tc_name
                                            l_ok.
      CLEAR p_ok.
*     WHEN 'L--'.                       "total left
*       PERFORM FCODE_TOTAL_LEFT USING P_TC_NAME.
*
*     WHEN 'L-'.                        "column left
*       PERFORM FCODE_COLUMN_LEFT USING P_TC_NAME.
*
*     WHEN 'R+'.                        "column right
*       PERFORM FCODE_COLUMN_RIGHT USING P_TC_NAME.
*
*     WHEN 'R++'.                       "total right
*       PERFORM FCODE_TOTAL_RIGHT USING P_TC_NAME.
*
    WHEN 'MARK'.                      "mark all filled lines
      PERFORM fcode_tc_mark_lines USING p_tc_name
                                        p_table_name
                                        p_mark_name   .
      CLEAR p_ok.

    WHEN 'DMRK'.                      "demark all filled lines
      PERFORM fcode_tc_demark_lines USING p_tc_name
                                          p_table_name
                                          p_mark_name .
      CLEAR p_ok.

*     WHEN 'SASCEND'   OR
*          'SDESCEND'.                  "sort column
*       PERFORM FCODE_SORT_TC USING P_TC_NAME
*                                   l_ok.

  ENDCASE.

ENDFORM.                              " USER_OK_TC

*&---------------------------------------------------------------------*
*&      Form  FCODE_INSERT_ROW                                         *
*&---------------------------------------------------------------------*
FORM fcode_insert_row
              USING    p_tc_name           TYPE dynfnam
                       p_table_name             .

*&SPWIZARD: BEGIN OF LOCAL DATA----------------------------------------*
  DATA l_lines_name       LIKE feld-name.
  DATA l_selline          LIKE sy-stepl.
  DATA l_lastline         TYPE i.
  DATA l_line             TYPE i.
  DATA l_table_name       LIKE feld-name.
  FIELD-SYMBOLS <tc>                 TYPE cxtab_control.
  FIELD-SYMBOLS <table>              TYPE STANDARD TABLE.
  FIELD-SYMBOLS <lines>              TYPE i.
*&SPWIZARD: END OF LOCAL DATA------------------------------------------*

  ASSIGN (p_tc_name) TO <tc>.

*&SPWIZARD: get the table, which belongs to the tc                     *
  CONCATENATE p_table_name '[]' INTO l_table_name. "table body
  ASSIGN (l_table_name) TO <table>.                "not headerline

*&SPWIZARD: get looplines of TableControl                              *
  CONCATENATE 'G_' p_tc_name '_LINES' INTO l_lines_name.
  ASSIGN (l_lines_name) TO <lines>.

*&SPWIZARD: get current line                                           *
  GET CURSOR LINE l_selline.
  IF sy-subrc <> 0.                   " append line to table
    l_selline = <tc>-lines + 1.
*&SPWIZARD: set top line                                               *
    IF l_selline > <lines>.
      <tc>-top_line = l_selline - <lines> + 1 .
    ELSE.
      <tc>-top_line = 1.
    ENDIF.
  ELSE.                               " insert line into table
    l_selline = <tc>-top_line + l_selline - 1.
    l_lastline = <tc>-top_line + <lines> - 1.
  ENDIF.
*&SPWIZARD: set new cursor line                                        *
  l_line = l_selline - <tc>-top_line + 1.

*&SPWIZARD: insert initial line                                        *
  INSERT INITIAL LINE INTO <table> INDEX l_selline.
  <tc>-lines = <tc>-lines + 1.
*&SPWIZARD: set cursor                                                 *
  SET CURSOR LINE l_line.

ENDFORM.                              " FCODE_INSERT_ROW

*&---------------------------------------------------------------------*
*&      Form  FCODE_DELETE_ROW                                         *
*&---------------------------------------------------------------------*
FORM fcode_delete_row
              USING    p_tc_name           TYPE dynfnam
                       p_table_name
                       p_mark_name   .

*&SPWIZARD: BEGIN OF LOCAL DATA----------------------------------------*
  DATA l_table_name       LIKE feld-name.

  FIELD-SYMBOLS <tc>         TYPE cxtab_control.
  FIELD-SYMBOLS <table>      TYPE STANDARD TABLE.
  FIELD-SYMBOLS <wa>.
  FIELD-SYMBOLS <mark_field>.
*&SPWIZARD: END OF LOCAL DATA------------------------------------------*

  ASSIGN (p_tc_name) TO <tc>.

*&SPWIZARD: get the table, which belongs to the tc                     *
  CONCATENATE p_table_name '[]' INTO l_table_name. "table body
  ASSIGN (l_table_name) TO <table>.                "not headerline

*&SPWIZARD: delete marked lines                                        *
  DESCRIBE TABLE <table> LINES <tc>-lines.

  LOOP AT <table> ASSIGNING <wa>.

*&SPWIZARD: access to the component 'FLAG' of the table header         *
    ASSIGN COMPONENT p_mark_name OF STRUCTURE <wa> TO <mark_field>.

    IF <mark_field> = 'X'.
      DELETE <table> INDEX syst-tabix.
      IF sy-subrc = 0.
        <tc>-lines = <tc>-lines - 1.
      ENDIF.
    ENDIF.
  ENDLOOP.

ENDFORM.                              " FCODE_DELETE_ROW

*&---------------------------------------------------------------------*
*&      Form  COMPUTE_SCROLLING_IN_TC
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
*      -->P_TC_NAME  name of tablecontrol
*      -->P_OK       ok code
*----------------------------------------------------------------------*
FORM compute_scrolling_in_tc USING    p_tc_name
                                      p_ok.
*&SPWIZARD: BEGIN OF LOCAL DATA----------------------------------------*
  DATA l_tc_new_top_line     TYPE i.
  DATA l_tc_name             LIKE feld-name.
  DATA l_tc_lines_name       LIKE feld-name.
  DATA l_tc_field_name       LIKE feld-name.

  FIELD-SYMBOLS <tc>         TYPE cxtab_control.
  FIELD-SYMBOLS <lines>      TYPE i.
*&SPWIZARD: END OF LOCAL DATA------------------------------------------*

  ASSIGN (p_tc_name) TO <tc>.
*&SPWIZARD: get looplines of TableControl                              *
  CONCATENATE 'G_' p_tc_name '_LINES' INTO l_tc_lines_name.
  ASSIGN (l_tc_lines_name) TO <lines>.


*&SPWIZARD: is no line filled?                                         *
  IF <tc>-lines = 0.
*&SPWIZARD: yes, ...                                                   *
    l_tc_new_top_line = 1.
  ELSE.
*&SPWIZARD: no, ...                                                    *
    CALL FUNCTION 'SCROLLING_IN_TABLE'
      EXPORTING
        entry_act      = <tc>-top_line
        entry_from     = 1
        entry_to       = <tc>-lines
        last_page_full = 'X'
        loops          = <lines>
        ok_code        = p_ok
        overlapping    = 'X'
      IMPORTING
        entry_new      = l_tc_new_top_line
      EXCEPTIONS
*       NO_ENTRY_OR_PAGE_ACT  = 01
*       NO_ENTRY_TO    = 02
*       NO_OK_CODE_OR_PAGE_GO = 03
        OTHERS         = 0.
  ENDIF.

*&SPWIZARD: get actual tc and column                                   *
  GET CURSOR FIELD l_tc_field_name
             AREA  l_tc_name.

  IF syst-subrc = 0.
    IF l_tc_name = p_tc_name.
*&SPWIZARD: et actual column                                           *
      SET CURSOR FIELD l_tc_field_name LINE 1.
    ENDIF.
  ENDIF.

*&SPWIZARD: set the new top line                                       *
  <tc>-top_line = l_tc_new_top_line.


ENDFORM.                              " COMPUTE_SCROLLING_IN_TC

*&---------------------------------------------------------------------*
*&      Form  FCODE_TC_MARK_LINES
*&---------------------------------------------------------------------*
*       marks all TableControl lines
*----------------------------------------------------------------------*
*      -->P_TC_NAME  name of tablecontrol
*----------------------------------------------------------------------*
FORM fcode_tc_mark_lines USING p_tc_name
                               p_table_name
                               p_mark_name.
*&SPWIZARD: EGIN OF LOCAL DATA-----------------------------------------*
  DATA l_table_name       LIKE feld-name.

  FIELD-SYMBOLS <tc>         TYPE cxtab_control.
  FIELD-SYMBOLS <table>      TYPE STANDARD TABLE.
  FIELD-SYMBOLS <wa>.
  FIELD-SYMBOLS <mark_field>.
*&SPWIZARD: END OF LOCAL DATA------------------------------------------*

  ASSIGN (p_tc_name) TO <tc>.

*&SPWIZARD: get the table, which belongs to the tc                     *
  CONCATENATE p_table_name '[]' INTO l_table_name. "table body
  ASSIGN (l_table_name) TO <table>.                "not headerline

*&SPWIZARD: mark all filled lines                                      *
  LOOP AT <table> ASSIGNING <wa>.

*&SPWIZARD: access to the component 'FLAG' of the table header         *
    ASSIGN COMPONENT p_mark_name OF STRUCTURE <wa> TO <mark_field>.

    <mark_field> = 'X'.
  ENDLOOP.
ENDFORM.                                          "fcode_tc_mark_lines

*&---------------------------------------------------------------------*
*&      Form  FCODE_TC_DEMARK_LINES
*&---------------------------------------------------------------------*
*       demarks all TableControl lines
*----------------------------------------------------------------------*
*      -->P_TC_NAME  name of tablecontrol
*----------------------------------------------------------------------*
FORM fcode_tc_demark_lines USING p_tc_name
                                 p_table_name
                                 p_mark_name .
*&SPWIZARD: BEGIN OF LOCAL DATA----------------------------------------*
  DATA l_table_name       LIKE feld-name.

  FIELD-SYMBOLS <tc>         TYPE cxtab_control.
  FIELD-SYMBOLS <table>      TYPE STANDARD TABLE.
  FIELD-SYMBOLS <wa>.
  FIELD-SYMBOLS <mark_field>.
*&SPWIZARD: END OF LOCAL DATA------------------------------------------*

  ASSIGN (p_tc_name) TO <tc>.

*&SPWIZARD: get the table, which belongs to the tc                     *
  CONCATENATE p_table_name '[]' INTO l_table_name. "table body
  ASSIGN (l_table_name) TO <table>.                "not headerline

*&SPWIZARD: demark all filled lines                                    *
  LOOP AT <table> ASSIGNING <wa>.

*&SPWIZARD: access to the component 'FLAG' of the table header         *
    ASSIGN COMPONENT p_mark_name OF STRUCTURE <wa> TO <mark_field>.

    <mark_field> = space.
  ENDLOOP.
ENDFORM.                                          "fcode_tc_mark_lines
*&---------------------------------------------------------------------*
*& Module MDL_SET_TC_DATA_9903 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE mdl_set_tc_data_9903 OUTPUT.
  PERFORM frm_set_data_9003 CHANGING gs_bukrs.
ENDMODULE.
MODULE mdl_set_tc_data_9917 OUTPUT.
  PERFORM frm_set_data_9003 CHANGING gs_bukrs_N.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_CHECK_BUKRS  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_check_bukrs INPUT.
  PERFORM frm_check_bukrs CHANGING gs_bukrs-bukrs.

ENDMODULE.
MODULE mdl_check_bukrs_N INPUT.
  PERFORM frm_check_bukrs CHANGING gs_bukrs_N-bukrs.

ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_SET_DATA_BUKRS  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_set_data_bukrs INPUT.
*  若存在非ALL的数据，则自动删除ALL的数据
  LOOP AT gt_bukrs TRANSPORTING NO FIELDS WHERE bukrs IS NOT INITIAL AND bukrs NE 'ALL'.
    EXIT.
  ENDLOOP.
  IF sy-subrc EQ 0.
    DELETE gt_bukrs WHERE bukrs = 'ALL'.
  ENDIF.

ENDMODULE.
MODULE mdl_set_data_bukrs_N INPUT.
*  若存在非ALL的数据，则自动删除ALL的数据
  LOOP AT gt_bukrs_N  TRANSPORTING NO FIELDS WHERE bukrs IS NOT INITIAL AND bukrs NE 'ALL'.
    EXIT.
  ENDLOOP.
  IF sy-subrc EQ 0.
    DELETE gt_bukrs_N  WHERE bukrs = 'ALL'.
  ENDIF.

ENDMODULE.
*&---------------------------------------------------------------------*
*& Module STATUS_9903 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_9903 OUTPUT.
  PERFORM frm_set_screen_9903.
ENDMODULE.
MODULE status_9917 OUTPUT.
  PERFORM frm_set_screen_9917.
ENDMODULE.