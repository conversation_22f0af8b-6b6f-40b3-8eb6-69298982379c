*&---------------------------------------------------------------------*
*& 包含               ZRED0001_S01
*&---------------------------------------------------------------------*


SELECTION-SCREEN BEGIN OF BLOCK b01 WITH FRAME TITLE TEXT-001.

PARAMETERS:

  p_zfllx  TYPE zret0002-zfllx AS LISTBOX  VISIBLE LENGTH 20 OBLIGATORY DEFAULT 'RB01' USER-COMMAND uc01 MODIF ID m01,
  p_zhtlx  TYPE zret0006-zhtlx AS LISTBOX  VISIBLE LENGTH 20  DEFAULT '' USER-COMMAND uc02 MODIF ID m01,
  p_zbukrs TYPE zret0006-zbukrs MODIF ID m01 MATCHCODE OBJECT c_t001,
*  p_zflsqf TYPE t001-bukrs  MODIF ID m01,
  p_zflzff TYPE lfa1-lifnr  MODIF ID m01.

PARAMETERS:
  cb_split TYPE char1 AS CHECKBOX MODIF ID M05 .

PARAMETERS:
  p_zxy_id TYPE zret0006-zxy_id MODIF ID m02 MATCHCODE OBJECT zresh0005 MEMORY ID zxyid.

SELECTION-SCREEN END OF BLOCK b01.


SELECTION-SCREEN BEGIN OF BLOCK b02 WITH FRAME TITLE TEXT-002 .

PARAMETERS:
  rb_add  TYPE char1 RADIOBUTTON GROUP g1 USER-COMMAND uc01 MODIF ID m03,      "新增
  rb_edit TYPE char1 RADIOBUTTON GROUP g1 MODIF ID m03,                        "修改
  rb_dis  TYPE char1 RADIOBUTTON GROUP g1 MODIF ID m03,                        "显示
  rb_rles TYPE char1 RADIOBUTTON GROUP g1 MODIF ID m03.                        "审批

SELECTION-SCREEN END OF BLOCK b02.


SELECTION-SCREEN BEGIN OF BLOCK b03 WITH FRAME TITLE TEXT-003 .

PARAMETERS:
  rb_nref TYPE char1 RADIOBUTTON GROUP g2 DEFAULT 'X' USER-COMMAND uc02 MODIF ID m01,
  rb_ref  TYPE char1 RADIOBUTTON GROUP g2 MODIF ID m01.
PARAMETERS:
  p_zxyref TYPE zret0006-zxy_id MATCHCODE OBJECT zresh0005 MODIF ID m04.
PARAMETERS:
  rb_spz_o TYPE char1 RADIOBUTTON GROUP g3 DEFAULT 'X'  USER-COMMAND uc03 MODIF ID m04,
  rb_spz_n TYPE char1 RADIOBUTTON GROUP g3  MODIF ID m04.

SELECTION-SCREEN END OF BLOCK b03.

*SELECTION-SCREEN BEGIN OF BLOCK b04 WITH FRAME TITLE TEXT-004 .
*
*SELECTION-SCREEN END OF BLOCK b04.

INITIALIZATION.

  PERFORM frm_screen_init.

  PERFORM frm_get_data_list_box        CHANGING   Gt_vls_zfllx
                                                  gt_vls_zJSFF
                                                  gt_vls_zhszq
                                                  gt_vls_zjszq
                                                  gt_vls_zhsjz
                                                  gt_vls_zhtlx.

  PERFORM frm_set_list_box             USING      'P_ZFLLX'
                                                  gt_vls_zfllx.

  PERFORM frm_set_list_box             USING      'P_ZHTLX'
                                                  gt_vls_zhtlx.

AT SELECTION-SCREEN OUTPUT.

  PERFORM frm_set_screen.

AT SELECTION-SCREEN.

START-OF-SELECTION.

  PERFORM frm_check_screen .

  PERFORM frm_author_check.

  PERFORM frm_main.