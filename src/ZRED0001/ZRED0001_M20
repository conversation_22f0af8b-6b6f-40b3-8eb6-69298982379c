*&---------------------------------------------------------------------*
*& Include ZRED0001_M20
*&---------------------------------------------------------------------*

*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_ITEM_DM' ITSELF
CONTROLS: tc_item_dm TYPE TABLEVIEW USING SCREEN 9230.

*&SPWIZARD: LINES OF TABLECONTROL 'TC_ITEM_DM'
DATA:     g_tc_item_dm_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_ITEM_DM'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: <PERSON><PERSON><PERSON> LINES FOR EQUIVALENT SCROLLBAR
MODULE tc_item_dm_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_t08 LINES tc_item_dm-lines.
ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_ITEM_DM'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL
MODULE tc_item_dm_get_lines OUTPUT.
  g_tc_item_dm_lines = sy-loopc.

  PERFORM frm_set_screen_all.

  IF  gt_t08[] IS INITIAL.
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.

  IF gv_zxybstyp = 'F' AND ( gs_t06-zdffs = 'C' OR gs_t06-zdffs = 'A' ).
    LOOP AT SCREEN.
      IF screen-name = 'GS_T08-ZMWSKZ' .
        screen-input = 0.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.

ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_ITEM_DM'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE
MODULE tc_item_dm_modify INPUT.
  MODIFY gt_t08
    FROM gs_t08
    INDEX tc_item_dm-current_line.
ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_ITEM_DM'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE
MODULE tc_item_dm_mark INPUT.
  DATA: g_tc_item_dm_wa2 LIKE LINE OF gt_t08.
  IF tc_item_dm-line_sel_mode = 1
  AND gs_t08-sel = 'X'.
    LOOP AT gt_t08 INTO g_tc_item_dm_wa2
      WHERE sel = 'X'.
      g_tc_item_dm_wa2-sel = ''.
      MODIFY gt_t08
        FROM g_tc_item_dm_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_t08
    FROM gs_t08
    INDEX tc_item_dm-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_ITEM_DM'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND
MODULE tc_item_dm_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_ITEM_DM'
                              'GT_T08'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.

*----------------------------------------------------------------------*
*   INCLUDE TABLECONTROL_FORMS                                         *
*----------------------------------------------------------------------*
*
**&---------------------------------------------------------------------*
**&      Form  USER_OK_TC                                               *
**&---------------------------------------------------------------------*
* FORM USER_OK_TC USING    P_TC_NAME TYPE DYNFNAM
*                          P_TABLE_NAME
*                          P_MARK_NAME
*                 CHANGING P_OK      LIKE SY-UCOMM.
*
**&SPWIZARD: BEGIN OF LOCAL DATA----------------------------------------*
*   DATA: L_OK              TYPE SY-UCOMM,
*         L_OFFSET          TYPE I.
**&SPWIZARD: END OF LOCAL DATA------------------------------------------*
*
**&SPWIZARD: Table control specific operations                          *
**&SPWIZARD: evaluate TC name and operations                            *
*   SEARCH P_OK FOR P_TC_NAME.
*   IF SY-SUBRC <> 0.
*     EXIT.
*   ENDIF.
*   L_OFFSET = STRLEN( P_TC_NAME ) + 1.
*   L_OK = P_OK+L_OFFSET.
**&SPWIZARD: execute general and TC specific operations                 *
*   CASE L_OK.
*     WHEN 'INSR'.                      "insert row
*       PERFORM FCODE_INSERT_ROW USING    P_TC_NAME
*                                         P_TABLE_NAME.
*       CLEAR P_OK.
*
*     WHEN 'DELE'.                      "delete row
*       PERFORM FCODE_DELETE_ROW USING    P_TC_NAME
*                                         P_TABLE_NAME
*                                         P_MARK_NAME.
*       CLEAR P_OK.
*
*     WHEN 'P--' OR                     "top of list
*          'P-'  OR                     "previous page
*          'P+'  OR                     "next page
*          'P++'.                       "bottom of list
*       PERFORM COMPUTE_SCROLLING_IN_TC USING P_TC_NAME
*                                             L_OK.
*       CLEAR P_OK.
**     WHEN 'L--'.                       "total left
**       PERFORM FCODE_TOTAL_LEFT USING P_TC_NAME.
**
**     WHEN 'L-'.                        "column left
**       PERFORM FCODE_COLUMN_LEFT USING P_TC_NAME.
**
**     WHEN 'R+'.                        "column right
**       PERFORM FCODE_COLUMN_RIGHT USING P_TC_NAME.
**
**     WHEN 'R++'.                       "total right
**       PERFORM FCODE_TOTAL_RIGHT USING P_TC_NAME.
**
*     WHEN 'MARK'.                      "mark all filled lines
*       PERFORM FCODE_TC_MARK_LINES USING P_TC_NAME
*                                         P_TABLE_NAME
*                                         P_MARK_NAME   .
*       CLEAR P_OK.
*
*     WHEN 'DMRK'.                      "demark all filled lines
*       PERFORM FCODE_TC_DEMARK_LINES USING P_TC_NAME
*                                           P_TABLE_NAME
*                                           P_MARK_NAME .
*       CLEAR P_OK.
*
**     WHEN 'SASCEND'   OR
**          'SDESCEND'.                  "sort column
**       PERFORM FCODE_SORT_TC USING P_TC_NAME
**                                   l_ok.
*
*   ENDCASE.
*
* ENDFORM.                              " USER_OK_TC
*
**&---------------------------------------------------------------------*
**&      Form  FCODE_INSERT_ROW                                         *
**&---------------------------------------------------------------------*
* FORM fcode_insert_row
*               USING    P_TC_NAME           TYPE DYNFNAM
*                        P_TABLE_NAME             .
*
**&SPWIZARD: BEGIN OF LOCAL DATA----------------------------------------*
*   DATA L_LINES_NAME       LIKE FELD-NAME.
*   DATA L_SELLINE          LIKE SY-STEPL.
*   DATA L_LASTLINE         TYPE I.
*   DATA L_LINE             TYPE I.
*   DATA L_TABLE_NAME       LIKE FELD-NAME.
*   FIELD-SYMBOLS <TC>                 TYPE CXTAB_CONTROL.
*   FIELD-SYMBOLS <TABLE>              TYPE STANDARD TABLE.
*   FIELD-SYMBOLS <LINES>              TYPE I.
**&SPWIZARD: END OF LOCAL DATA------------------------------------------*
*
*   ASSIGN (P_TC_NAME) TO <TC>.
*
**&SPWIZARD: get the table, which belongs to the tc                     *
*   CONCATENATE P_TABLE_NAME '[]' INTO L_TABLE_NAME. "table body
*   ASSIGN (L_TABLE_NAME) TO <TABLE>.                "not headerline
*
**&SPWIZARD: get looplines of TableControl                              *
*   CONCATENATE 'G_' P_TC_NAME '_LINES' INTO L_LINES_NAME.
*   ASSIGN (L_LINES_NAME) TO <LINES>.
*
**&SPWIZARD: get current line                                           *
*   GET CURSOR LINE L_SELLINE.
*   IF SY-SUBRC <> 0.                   " append line to table
*     L_SELLINE = <TC>-LINES + 1.
**&SPWIZARD: set top line                                               *
*     IF L_SELLINE > <LINES>.
*       <TC>-TOP_LINE = L_SELLINE - <LINES> + 1 .
*     ELSE.
*       <TC>-TOP_LINE = 1.
*     ENDIF.
*   ELSE.                               " insert line into table
*     L_SELLINE = <TC>-TOP_LINE + L_SELLINE - 1.
*     L_LASTLINE = <TC>-TOP_LINE + <LINES> - 1.
*   ENDIF.
**&SPWIZARD: set new cursor line                                        *
*   L_LINE = L_SELLINE - <TC>-TOP_LINE + 1.
*
**&SPWIZARD: insert initial line                                        *
*   INSERT INITIAL LINE INTO <TABLE> INDEX L_SELLINE.
*   <TC>-LINES = <TC>-LINES + 1.
**&SPWIZARD: set cursor                                                 *
*   SET CURSOR LINE L_LINE.
*
* ENDFORM.                              " FCODE_INSERT_ROW
*
**&---------------------------------------------------------------------*
**&      Form  FCODE_DELETE_ROW                                         *
**&---------------------------------------------------------------------*
* FORM fcode_delete_row
*               USING    P_TC_NAME           TYPE DYNFNAM
*                        P_TABLE_NAME
*                        P_MARK_NAME   .
*
**&SPWIZARD: BEGIN OF LOCAL DATA----------------------------------------*
*   DATA L_TABLE_NAME       LIKE FELD-NAME.
*
*   FIELD-SYMBOLS <TC>         TYPE cxtab_control.
*   FIELD-SYMBOLS <TABLE>      TYPE STANDARD TABLE.
*   FIELD-SYMBOLS <WA>.
*   FIELD-SYMBOLS <MARK_FIELD>.
**&SPWIZARD: END OF LOCAL DATA------------------------------------------*
*
*   ASSIGN (P_TC_NAME) TO <TC>.
*
**&SPWIZARD: get the table, which belongs to the tc                     *
*   CONCATENATE P_TABLE_NAME '[]' INTO L_TABLE_NAME. "table body
*   ASSIGN (L_TABLE_NAME) TO <TABLE>.                "not headerline
*
**&SPWIZARD: delete marked lines                                        *
*   DESCRIBE TABLE <TABLE> LINES <TC>-LINES.
*
*   LOOP AT <TABLE> ASSIGNING <WA>.
*
**&SPWIZARD: access to the component 'FLAG' of the table header         *
*     ASSIGN COMPONENT P_MARK_NAME OF STRUCTURE <WA> TO <MARK_FIELD>.
*
*     IF <MARK_FIELD> = 'X'.
*       DELETE <TABLE> INDEX SYST-TABIX.
*       IF SY-SUBRC = 0.
*         <TC>-LINES = <TC>-LINES - 1.
*       ENDIF.
*     ENDIF.
*   ENDLOOP.
*
* ENDFORM.                              " FCODE_DELETE_ROW
*
**&---------------------------------------------------------------------*
**&      Form  COMPUTE_SCROLLING_IN_TC
**&---------------------------------------------------------------------*
**       text
**----------------------------------------------------------------------*
**      -->P_TC_NAME  name of tablecontrol
**      -->P_OK       ok code
**----------------------------------------------------------------------*
* FORM COMPUTE_SCROLLING_IN_TC USING    P_TC_NAME
*                                       P_OK.
**&SPWIZARD: BEGIN OF LOCAL DATA----------------------------------------*
*   DATA L_TC_NEW_TOP_LINE     TYPE I.
*   DATA L_TC_NAME             LIKE FELD-NAME.
*   DATA L_TC_LINES_NAME       LIKE FELD-NAME.
*   DATA L_TC_FIELD_NAME       LIKE FELD-NAME.
*
*   FIELD-SYMBOLS <TC>         TYPE cxtab_control.
*   FIELD-SYMBOLS <LINES>      TYPE I.
**&SPWIZARD: END OF LOCAL DATA------------------------------------------*
*
*   ASSIGN (P_TC_NAME) TO <TC>.
**&SPWIZARD: get looplines of TableControl                              *
*   CONCATENATE 'G_' P_TC_NAME '_LINES' INTO L_TC_LINES_NAME.
*   ASSIGN (L_TC_LINES_NAME) TO <LINES>.
*
*
**&SPWIZARD: is no line filled?                                         *
*   IF <TC>-LINES = 0.
**&SPWIZARD: yes, ...                                                   *
*     L_TC_NEW_TOP_LINE = 1.
*   ELSE.
**&SPWIZARD: no, ...                                                    *
*     CALL FUNCTION 'SCROLLING_IN_TABLE'
*          EXPORTING
*               ENTRY_ACT             = <TC>-TOP_LINE
*               ENTRY_FROM            = 1
*               ENTRY_TO              = <TC>-LINES
*               LAST_PAGE_FULL        = 'X'
*               LOOPS                 = <LINES>
*               OK_CODE               = P_OK
*               OVERLAPPING           = 'X'
*          IMPORTING
*               ENTRY_NEW             = L_TC_NEW_TOP_LINE
*          EXCEPTIONS
**              NO_ENTRY_OR_PAGE_ACT  = 01
**              NO_ENTRY_TO           = 02
**              NO_OK_CODE_OR_PAGE_GO = 03
*               OTHERS                = 0.
*   ENDIF.
*
**&SPWIZARD: get actual tc and column                                   *
*   GET CURSOR FIELD L_TC_FIELD_NAME
*              AREA  L_TC_NAME.
*
*   IF SYST-SUBRC = 0.
*     IF L_TC_NAME = P_TC_NAME.
**&SPWIZARD: et actual column                                           *
*       SET CURSOR FIELD L_TC_FIELD_NAME LINE 1.
*     ENDIF.
*   ENDIF.
*
**&SPWIZARD: set the new top line                                       *
*   <TC>-TOP_LINE = L_TC_NEW_TOP_LINE.
*
*
* ENDFORM.                              " COMPUTE_SCROLLING_IN_TC
*
**&---------------------------------------------------------------------*
**&      Form  FCODE_TC_MARK_LINES
**&---------------------------------------------------------------------*
**       marks all TableControl lines
**----------------------------------------------------------------------*
**      -->P_TC_NAME  name of tablecontrol
**----------------------------------------------------------------------*
*FORM FCODE_TC_MARK_LINES USING P_TC_NAME
*                               P_TABLE_NAME
*                               P_MARK_NAME.
**&SPWIZARD: EGIN OF LOCAL DATA-----------------------------------------*
*  DATA L_TABLE_NAME       LIKE FELD-NAME.
*
*  FIELD-SYMBOLS <TC>         TYPE cxtab_control.
*  FIELD-SYMBOLS <TABLE>      TYPE STANDARD TABLE.
*  FIELD-SYMBOLS <WA>.
*  FIELD-SYMBOLS <MARK_FIELD>.
**&SPWIZARD: END OF LOCAL DATA------------------------------------------*
*
*  ASSIGN (P_TC_NAME) TO <TC>.
*
**&SPWIZARD: get the table, which belongs to the tc                     *
*   CONCATENATE P_TABLE_NAME '[]' INTO L_TABLE_NAME. "table body
*   ASSIGN (L_TABLE_NAME) TO <TABLE>.                "not headerline
*
**&SPWIZARD: mark all filled lines                                      *
*  LOOP AT <TABLE> ASSIGNING <WA>.
*
**&SPWIZARD: access to the component 'FLAG' of the table header         *
*     ASSIGN COMPONENT P_MARK_NAME OF STRUCTURE <WA> TO <MARK_FIELD>.
*
*     <MARK_FIELD> = 'X'.
*  ENDLOOP.
*ENDFORM.                                          "fcode_tc_mark_lines
*
**&---------------------------------------------------------------------*
**&      Form  FCODE_TC_DEMARK_LINES
**&---------------------------------------------------------------------*
**       demarks all TableControl lines
**----------------------------------------------------------------------*
**      -->P_TC_NAME  name of tablecontrol
**----------------------------------------------------------------------*
*FORM FCODE_TC_DEMARK_LINES USING P_TC_NAME
*                                 P_TABLE_NAME
*                                 P_MARK_NAME .
**&SPWIZARD: BEGIN OF LOCAL DATA----------------------------------------*
*  DATA L_TABLE_NAME       LIKE FELD-NAME.
*
*  FIELD-SYMBOLS <TC>         TYPE cxtab_control.
*  FIELD-SYMBOLS <TABLE>      TYPE STANDARD TABLE.
*  FIELD-SYMBOLS <WA>.
*  FIELD-SYMBOLS <MARK_FIELD>.
**&SPWIZARD: END OF LOCAL DATA------------------------------------------*
*
*  ASSIGN (P_TC_NAME) TO <TC>.
*
**&SPWIZARD: get the table, which belongs to the tc                     *
*   CONCATENATE P_TABLE_NAME '[]' INTO L_TABLE_NAME. "table body
*   ASSIGN (L_TABLE_NAME) TO <TABLE>.                "not headerline
*
**&SPWIZARD: demark all filled lines                                    *
*  LOOP AT <TABLE> ASSIGNING <WA>.
*
**&SPWIZARD: access to the component 'FLAG' of the table header         *
*     ASSIGN COMPONENT P_MARK_NAME OF STRUCTURE <WA> TO <MARK_FIELD>.
*
*     <MARK_FIELD> = SPACE.
*  ENDLOOP.
*ENDFORM.                                          "fcode_tc_mark_lines
*&---------------------------------------------------------------------*
*&      Module  MDL_CHECK_LIFNR_9230  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_check_lifnr_9230 INPUT.
  PERFORM frm_check_lifnr USING gs_t08-lifnr.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_CHECK_MATNR_9230  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_check_matnr_9230 INPUT.
  PERFORM frm_check_matnr USING gs_t08-matnr.
ENDMODULE.
*&---------------------------------------------------------------------*
*& Module MDL_SET_DATA_9230 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE mdl_set_data_tc_item_dm OUTPUT.
  PERFORM frm_set_data_tc_item_dm   USING    gs_t06
                                    CHANGING gs_t08 .
ENDMODULE.
*&---------------------------------------------------------------------*
*& Module STATUS_9230 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_9230 OUTPUT.
  PERFORM frm_set_screen_9230.
  PERFORM frm_set_data_9230 CHANGING gt_t08.
ENDMODULE.