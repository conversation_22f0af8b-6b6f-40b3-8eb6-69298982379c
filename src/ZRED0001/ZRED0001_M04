*----------------------------------------------------------------------*
***INCLUDE ZRED0001_M04.
*----------------------------------------------------------------------*
*&---------------------------------------------------------------------*
*& Module STATUS_9125 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_9125 OUTPUT.
* SET PF-STATUS 'xxxxxxxx'.
* SET TITLEBAR 'xxx'.
  PERFORM frm_alv_init_9125.
ENDMODULE.
*&---------------------------------------------------------------------*
*& Form FRM_ALV_INIT_9125
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_alv_init_9125 .


  IF  go_container_9125 IS INITIAL.

    CREATE OBJECT go_container_9125
      EXPORTING
        container_name = gv_con_name_9125.
    IF go_alv_9125 IS INITIAL.
      CREATE OBJECT go_alv_9125
        EXPORTING
          i_parent = go_container_9125.
    ENDIF.

    PERFORM frm_alv_display .
  ELSE.

    PERFORM frm_alv_refresh .
  ENDIF.

ENDFORM.

FORM frm_alv_display .

  DATA: lt_fieldcat TYPE lvc_t_fcat,
        ls_fieldcat TYPE lvc_s_fcat,
        ls_layout   TYPE lvc_s_layo,
        lt_ex_fcode TYPE ui_functions,
        ls_variant  TYPE disvariant.

  PERFORM frm_set_catalog CHANGING lt_fieldcat.

  PERFORM frm_set_layout CHANGING ls_layout.

  PERFORM frm_set_ex_fcode TABLES lt_ex_fcode.

*ALV显示
  PERFORM frm_set_alv USING
                            'GT_DATA_9125'
                            ls_variant
                            ls_layout
                            lt_ex_fcode
                      CHANGING
                            lt_fieldcat
                            go_alv_9125.


ENDFORM.

*&---------------------------------------------------------------------*
*&      FORM  FRM_SET_CATALOG
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->PT_FIELDCAT  TEXT
*----------------------------------------------------------------------*
FORM frm_set_catalog  CHANGING   pt_fieldcat TYPE lvc_t_fcat.
  DATA: ls_fieldcat TYPE lvc_s_fcat.
  "定义
  DEFINE add_fcat.
    CLEAR ls_fieldcat.

    IF ls_fieldcat IS INITIAL.
      ls_fieldcat-col_pos = 1.
    ELSE.
      ADD 1 TO ls_fieldcat-col_pos.
    ENDIF.
    ls_fieldcat-outputlen = &1.                 "
    ls_fieldcat-fieldname = &2.                 "
    ls_fieldcat-coltext = &3.                 "

    CASE &2.
*      WHEN 'WERKS'.
*      ls_fieldcat-ref_table = 'T001W'.
*      ls_fieldcat-ref_field = 'WERKS'.
*      WHEN 'ZSHDZT'  .
*      ls_fieldcat-convexit = 'ZSHDZ'.

      WHEN OTHERS.
    ENDCASE.

    APPEND ls_fieldcat TO pt_fieldcat.
    CLEAR ls_fieldcat.
  END-OF-DEFINITION.

  add_fcat  '10' 'REQUESTID'    '审批流程ID '  .
  add_fcat  '10' 'ZCJRQ'    '日期'  .
  add_fcat  '10' 'ZCJSJ'    '时间'  .

  add_fcat  '10' 'ZNAME'    '审批人员工号 '  .
  add_fcat  '10' 'ZSPR'    '审批人'  .
  add_fcat  '10' 'ZPASS'    '是否通过审批'  .
  add_fcat  '10' 'ZTEXT'    '审批意见 '  .
ENDFORM.                    " FRM_SET_CATALOG
*&---------------------------------------------------------------------*
*&      FORM  FRM_SET_LAYOUT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->P_LAYOUT   TEXT
*----------------------------------------------------------------------*
FORM frm_set_layout CHANGING ps_layout TYPE lvc_s_layo.
  CLEAR:
        ps_layout.
  ps_layout-box_fname = 'SEL_ALV'.
  ps_layout-sel_mode = 'D'.
  ps_layout-zebra     = 'X'.
*  IF RD_ADD = 'X' OR RD_EDIT = 'X'.
*    PS_LAYOUT-EXCP_FNAME = 'STATUS'.
*  ENDIF.
  ps_layout-cwidth_opt  = 'X'.

ENDFORM.                    "FRM_SET_LAYOUT


*&---------------------------------------------------------------------*
*&      FORM  FRM_SET_EX_FCODE
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->PT_EXCLUDE TEXT
*----------------------------------------------------------------------*
FORM frm_set_ex_fcode TABLES pt_exclude.

  DATA ls_exclude TYPE ui_func.
  ls_exclude = cl_gui_alv_grid=>mc_fc_detail.
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_print .
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_print_prev .
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_graph .
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_check .
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_mb_view .
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_help .
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_info .
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_refresh.
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_mb_paste.
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_loc_undo.
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_loc_cut.
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_loc_copy.
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_loc_copy_row.
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_loc_append_row.
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_loc_move_row.
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_loc_insert_row .
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_loc_delete_row .
  APPEND ls_exclude TO pt_exclude.


ENDFORM.                    "FRM_SET_EX_FCODE


*&---------------------------------------------------------------------*
*&      FORM  FRM_SET_ALV
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->P_LS_VARIANT  TEXT
*      -->P_LS_LAYOUT  TEXT
*      -->P_LT_EX_FCODE  TEXT
*      <--P_LT_FIELDCAT  TEXT
*      <--P_'GT_DATA_T'  TEXT
*----------------------------------------------------------------------*
FORM frm_set_alv  USING
                           pv_tname TYPE any
                           ps_variant TYPE disvariant
                           ps_layout TYPE lvc_s_layo
                           pt_ex_fcode TYPE ui_functions
                  CHANGING pt_fieldcat TYPE lvc_t_fcat
                           po_alv_grid TYPE REF TO cl_gui_alv_grid.


  DATA lv_table  LIKE feld-name.
  FIELD-SYMBOLS <lt_table> TYPE STANDARD TABLE.

  CONCATENATE pv_tname '[]' INTO lv_table.
  ASSIGN (lv_table) TO <lt_table>.

  CALL METHOD po_alv_grid->set_table_for_first_display
    EXPORTING
*     I_BUFFER_ACTIVE               =
*     I_BYPASSING_BUFFER            =
*     I_CONSISTENCY_CHECK           =
*     I_STRUCTURE_NAME              = 'IT_ITAB'
      is_variant                    = ps_variant
      i_save                        = 'A'
*     I_DEFAULT                     = 'X'
      is_layout                     = ps_layout
*     IS_PRINT                      =
*     IT_SPECIAL_GROUPS             =
      it_toolbar_excluding          = pt_ex_fcode
*     IT_HYPERLINK                  =
*     IT_ALV_GRAPHICS               =
*     IT_EXCEPT_QINFO               =
*     IR_SALV_ADAPTER               =
    CHANGING
      it_outtab                     = <lt_table>
      it_fieldcatalog               = pt_fieldcat
*     IT_SORT                       =
*     IT_FILTER                     =
    EXCEPTIONS
      invalid_parameter_combination = 1
      program_error                 = 2
      too_many_lines                = 3
      OTHERS                        = 4.
  IF sy-subrc <> 0.
    MESSAGE ID sy-msgid TYPE sy-msgty NUMBER sy-msgno
               WITH sy-msgv1 sy-msgv2 sy-msgv3 sy-msgv4.
  ENDIF.

ENDFORM.

FORM frm_alv_refresh .

  DATA:
  ls_stable TYPE lvc_s_stbl.
  DATA:
        ls_layout   TYPE lvc_s_layo.

  ls_stable-row = 'X'.
  ls_stable-col = 'X'.

  IF go_alv_9125 IS NOT INITIAL .

    PERFORM frm_set_layout CHANGING ls_layout.

    CALL METHOD go_alv_9125->set_frontend_layout
      EXPORTING
        is_layout = ls_layout.

    CALL METHOD go_alv_9125->refresh_table_display
      EXPORTING
        is_stable = ls_stable
*       I_SOFT_REFRESH = ''
      EXCEPTIONS
        finished  = 1
        OTHERS    = 2.
    IF sy-subrc <> 0.
      MESSAGE ID sy-msgid TYPE sy-msgty NUMBER sy-msgno
                 WITH sy-msgv1 sy-msgv2 sy-msgv3 sy-msgv4.

    ENDIF.
  ENDIF.

ENDFORM.