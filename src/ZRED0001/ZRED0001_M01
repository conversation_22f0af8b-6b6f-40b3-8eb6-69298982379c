*&---------------------------------------------------------------------*
*& 包含               ZRED0001_M01
*&---------------------------------------------------------------------*
*&---------------------------------------------------------------------*
*& MODULE STATUS_9100 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_9999 OUTPUT.
  SET PF-STATUS 'G9999'.
*  CASE GV_ZXYBSTYP.
*    WHEN .
*    WHEN .
*    WHEN OTHERS.
*  ENDCASE.
  SET TITLEBAR 'T9999' WITH gv_title_01 gv_title_02.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      MODULE  USER_COMMAND_9100  INPUT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
MODULE user_command_9999 INPUT.

  PERFORM frm_code_pro CHANGING   ok_code
                                  lv_code.

  CASE lv_code.
    WHEN 'BACK' OR 'EXIT' OR 'CANCEL'.
      LEAVE TO SCREEN 0.
    WHEN OTHERS.
  ENDCASE.
ENDMODULE.
*&---------------------------------------------------------------------*
*& MODULE STATUS_9100 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_9100 OUTPUT.
  PERFORM frm_set_screen_9100.

ENDMODULE.
*&---------------------------------------------------------------------*
*&      MODULE  USER_COMMAND_9100  INPUT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
MODULE user_command_9100 INPUT.


  DATA:
    lv_flg_err TYPE char1.

  CLEAR:
        lv_flg_err.

  PERFORM frm_code_pro CHANGING   ok_code
                                  lv_code.

  CASE lv_code.
    WHEN 'BACK' OR 'EXIT' OR 'CANCEL'.
      LEAVE TO SCREEN 0.
*    渠道供应商清单
    WHEN 'BT_LIFNR'.                    CALL SCREEN 9911.
*    供货方
    WHEN 'BT_ZGHF'.                     CALL SCREEN 9912.
*    供货方
    WHEN 'BT_ZGHF_N'.                   CALL SCREEN 9921.
*    生产商
    WHEN 'BT_ZCJ'.                      CALL SCREEN 9909.
*    核算期间
    WHEN 'BT_HSQJ'.                     CALL SCREEN 9907.
*    结算期间
    WHEN 'BT_JSQJ'.                     CALL SCREEN 9908.
*    公司代码
    WHEN 'BT_BUKRS'.                    CALL SCREEN 9903.
*    DC代码
    WHEN 'BT_DCWRK'.                    CALL SCREEN 9904.
*    门店代码
    WHEN 'BT_WERKS'.                    CALL SCREEN 9905.
*    采购组织
    WHEN 'BT_EKORG'.                    CALL SCREEN 9906.

*    公司代码
    WHEN 'BT_BUKRS_N'.                  CALL SCREEN 9917.
*    DC代码
    WHEN 'BT_DCWRK_N'.                  CALL SCREEN 9918.
*    门店代码
    WHEN 'BT_WERKS_N'.                  CALL SCREEN 9919.
*    采购组织
    WHEN 'BT_EKORG_N'.                  CALL SCREEN 9920.

*    商品组清单-任务
    WHEN 'BT_DTL_SPZ' .
      PERFORM frm_bt_dtl_spz_command USING '9913'
                                         'TC_ITEM_D'
                                         gv_cursor_line
                                     CHANGING gt_t07
                                              gt_t39
                                              gs_t09
                                              gt_t09
                                              gt_t20
                                              gt_t20_sub.

*    商品组清单-返利
    WHEN 'BT_DTL_SPZ_N' .
      PERFORM frm_bt_dtl_spz_command USING '9913'
                                         'TC_ITEM_D_N'
                                         gv_cursor_line
                                     CHANGING gt_t07
                                              gt_t39
                                              gs_t09
                                              gt_t09
                                              gt_t20
                                              gt_t20_sub.
*    保存
    WHEN 'BT_SAVE'.
      PERFORM frm_check_before_save      USING    gs_t06
                                                  gs_t10
                                                  gs_t10_js
                                                  gt_t11
                                                  gt_t11_hs
                                                  gt_t11_js
                                                  gt_t11_jsh
                                                  gt_t07
                                                  gt_t39
                                                  gt_t20
                                                  gt_t13
                                                  gt_t38
                                                  gt_bukrs
                                                  gt_dcwrk
                                                  gt_werks
                                                  gt_ekorg
                                                  gt_bukrs_n
                                                  gt_dcwrk_n
                                                  gt_werks_n
                                                  gt_ekorg_n
                                        CHANGING  lv_flg_err.
      IF lv_flg_err = 'S'.
        PERFORM frm_save_all_data USING 'SAVE'.
      ELSE.
        MESSAGE s888(sabapdocu) WITH '数据检查未通过，操作已终止!' DISPLAY LIKE 'E'.
      ENDIF.

*    提交
    WHEN 'BT_COMT'.
      PERFORM frm_check_before_comt            USING    gs_t06
                                                        gs_t10
                                                        gs_t10_js
                                                        gt_t11
                                                        gt_t11_hs
                                                        gt_t11_js
                                                        gt_t11_jsh
                                                        gt_t07
                                                        gt_t39
                                                        gt_t20
                                                        gt_t08
                                                        gt_t13
                                                        gt_t38
                                                        gt_bukrs
                                                        gt_dcwrk
                                                        gt_werks
                                                        gt_ekorg
                                                        gt_bukrs_n
                                                        gt_dcwrk_n
                                                        gt_werks_n
                                                        gt_ekorg_n
                                                        gt_t12
                                                        gt_t21
                                               CHANGING lv_flg_err.
      IF lv_flg_err = 'S'.
        PERFORM frm_save_all_data USING 'COMT'.
        gv_flg_comm = 'COMT'.
      ELSE.
        MESSAGE s888(sabapdocu) WITH '数据检查未通过，操作已终止!' DISPLAY LIKE 'E'.
      ENDIF.

*    审批
    WHEN 'BT_RLES'.
      IF gs_t06-zxyzt = 'A'.
        MESSAGE s888(sabapdocu) WITH '协议已经审批，不允许重复审批！' DISPLAY LIKE 'E'.
      ELSEIF gs_t06-ztk_id IS NOT INITIAL.
        MESSAGE s888(sabapdocu) WITH '请使用新版返利平台进行审批！' DISPLAY LIKE 'E'.
      ELSE.
        PERFORM frm_save_all_data USING 'RLES'.
        gv_flg_comm = 'RLES'.
      ENDIF.

*    取消审批
    WHEN 'BT_RLES_C'.
      IF gs_t06-ztk_id IS NOT INITIAL. .
        MESSAGE s888(sabapdocu) WITH '请使用新版返利平台进行取消审批！' DISPLAY LIKE 'E'.
      ELSE.

        PERFORM frm_check_before_rles_c      USING    gs_t06
                                             CHANGING lv_flg_err.
        IF lv_flg_err = 'S'.
          PERFORM frm_save_all_data USING 'RLES_C'.
          gv_flg_comm = 'RLES_C'.
        ENDIF.
      ENDIF.
*    作废
    WHEN 'BT_CANCEL'.
      IF gs_t06-ztk_id IS NOT INITIAL.
        MESSAGE s888(sabapdocu) WITH '请使用新版返利平台进行作废！' DISPLAY LIKE 'E'.
      ELSE.

        PERFORM frm_check_before_cancel      USING    gs_t06
                                             CHANGING lv_flg_err.
        IF lv_flg_err = 'S'.
          PERFORM frm_save_all_data USING 'CANCEL'.
          gv_flg_comm = 'CANCEL'.
        ENDIF.

      ENDIF.
*    检查
    WHEN 'BT_CHECK'.
      PERFORM frm_data_check_main    USING      gs_t06
                                                gs_t10
                                                gs_t10_js
                                                gt_t11
                                                gt_t11_hs
                                                gt_t11_js
                                                gt_t11_jsh
                                                gt_t07
                                                gt_t39
                                                gt_t20
                                                gt_t13
                                                gt_t38
                                                gt_bukrs
                                                gt_dcwrk
                                                gt_werks
                                                gt_ekorg
                                                gt_bukrs_n
                                                gt_dcwrk_n
                                                gt_werks_n
                                                gt_ekorg_n
                                                gt_t12
                                                gt_t21
                                     CHANGING   lv_flg_err.
      IF lv_flg_err = 'S'.
        MESSAGE s888(sabapdocu) WITH '检查无错误！'.
      ELSE.
        MESSAGE s888(sabapdocu) WITH '数据检查未通过，操作已终止!' DISPLAY LIKE 'E'.
      ENDIF.
*    累计明细
    WHEN 'BT_LJMX'.

      SET PARAMETER ID 'ZXYID' FIELD gs_t06-zxy_id.
      SET PARAMETER ID 'ZFLSQF' FIELD gs_t06-zflsqf.
      SET PARAMETER ID 'BUK' FIELD gs_t06-zbukrs.
      SET PARAMETER ID 'EKG' FIELD gs_t06-ekgrp.
      SET PARAMETER ID 'ZHTLX' FIELD gs_t06-zhtlx.
      CALL TRANSACTION 'ZRER0001' AND SKIP FIRST SCREEN.
      SET PARAMETER ID 'ZXYID' FIELD ''.

*    结算单明细
    WHEN 'BT_JSDMX'.

      SET PARAMETER ID 'ZXYID' FIELD gs_t06-zxy_id.
      CALL TRANSACTION 'ZRED0010' AND SKIP FIRST SCREEN.
      SET PARAMETER ID 'ZXYID' FIELD ''.
*    返利计算子阶梯
    WHEN 'BT_DTL_JS'.
      PERFORM frm_bt_dtl_jt_command USING '9915'
                                          'TC_ZJT'
                                          gv_cursor_line
                                          gs_t10_js
                                    CHANGING
                                          gt_t15
                                          gt_t11
                                          gt_t11_js
                                          gt_t11_js_sub.
*     返利支付方清单
    WHEN 'BT_ZFF'.
      PERFORM frm_pro_zff CHANGING gs_t06
                                   gt_t44.

      CALL SCREEN 9922.


      CLEAR gs_t44.
      READ TABLE gt_t44 INTO gs_t44  INDEX 1.
      gs_t06-zflzff = gs_t44-zflzff.

    WHEN OTHERS.

  ENDCASE.

*  IF lv_flg_err = 'E'.
*    MESSAGE s888(sabapdocu) WITH '数据检查未通过，操作已终止!' DISPLAY LIKE 'E'.
*  ENDIF.
ENDMODULE.
*&---------------------------------------------------------------------*
*& MODULE STATUS_9110 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_9110 OUTPUT.

  PERFORM frm_set_screen_9110.                           .
  PERFORM frm_set_list_box USING 'GS_T06-ZFLLX'
                                  gt_vls_zfllx.
ENDMODULE.
*&---------------------------------------------------------------------*
*& MODULE STATUS_9121 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_9121 OUTPUT.
  PERFORM frm_set_screen_9121.
  PERFORM frm_set_data_9121 USING gt_t12
                                  gt_t13
                                  gt_t38
                                  gt_t21
                            CHANGING gs_t06.
ENDMODULE.
*&---------------------------------------------------------------------*
*& MODULE STATUS_9122 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_9122 OUTPUT.
  PERFORM frm_set_screen_9122.

  PERFORM frm_set_data_9122 USING gt_bukrs
                                  gt_dcwrk
                                  gt_werks
                                  gt_ekorg
                                  gt_bukrs_n
                                  gt_dcwrk_n
                                  gt_werks_n
                                  gt_ekorg_n
                            CHANGING
                                  gs_t06.
ENDMODULE.
*&---------------------------------------------------------------------*
*& MODULE STATUS_9123 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_9123 OUTPUT.
  PERFORM frm_set_screen_9123.
  PERFORM frm_set_list_box USING 'GS_T06-ZHSJZ'
                                  gt_vls_zhsjz.
  PERFORM frm_set_list_box USING 'GS_T06-ZFLHSJZ'
                                  gt_vls_zhsjz.
  PERFORM frm_set_list_box USING 'GS_T06-ZJSZQ'
                                  gt_vls_zjszq.
  PERFORM frm_set_list_box USING 'GS_T06-ZHSZQ'
                                  gt_vls_zhszq.
ENDMODULE.
*&---------------------------------------------------------------------*
*& MODULE STATUS_9124 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_9124 OUTPUT.
  PERFORM frm_set_screen_9124.
ENDMODULE.
*&---------------------------------------------------------------------*
*& MODULE STATUS_9131 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_9131 OUTPUT.

*  PERFORM frm_set_data_listbox  CHANGING     cb_split
*                                             gt_vls_zjsff.

  DATA(lt_vls_zjsff_9131) = gt_vls_zjsff[].

  IF cb_split = ''.
    DELETE lt_vls_zjsff_9131 WHERE key = 'T'.
  ELSE.
    DELETE lt_vls_zjsff_9131 WHERE key = 'R'.
  ENDIF.

  PERFORM frm_set_list_box             USING      'GS_T10-ZJSFF'
                                                  lt_vls_zjsff_9131.

  PERFORM frm_set_screen_9131.
ENDMODULE.

MODULE status_9134 OUTPUT.


  DATA(lt_vls_zjsff_9134) = gt_vls_zjsff[].
  DELETE lt_vls_zjsff_9134 WHERE key = 'T'.

  PERFORM frm_set_list_box             USING      'GS_T10_JS-ZJSFF'
                                                  lt_vls_zjsff_9134.

  PERFORM frm_set_screen_9134.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      MODULE  MDL_GET_CURSOR_LINE  INPUT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
MODULE mdl_get_cursor_line INPUT.
  GET CURSOR LINE gv_cursor_line.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      MODULE  USER_COMMAND_9907  INPUT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
MODULE user_command_9907 INPUT.

  PERFORM frm_code_pro CHANGING   ok_code
                                  lv_code.

  CASE lv_code.
    WHEN 'BACK' OR 'EXIT' OR 'CANCEL'.
      LEAVE TO SCREEN 0.
*    统一集成到 frm_bt_dtl_jt_command 中
*    WHEN 'BT_DTL_ZJT'.      PERFORM frm_bt_dtl_zjt_command.
*    核算子阶梯
    WHEN 'BT_DTL_ZJT'.
      PERFORM frm_bt_dtl_jt_command USING '9914'
                                          'TC_ZHSQJ'
                                          gv_cursor_line
                                          gs_t10
                                    CHANGING
                                          gt_t15
                                          gt_t11
                                          gt_t11_hs
                                          gt_t11_hs_sub.
    WHEN OTHERS.
  ENDCASE.
ENDMODULE.
*&---------------------------------------------------------------------*
*& MODULE STATUS_9221 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_9221 OUTPUT.
  PERFORM frm_set_screen_9221.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_CHECK_ZCJ  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_check_zcj INPUT.
  PERFORM frm_check_zcj USING gs_t21-zcj.

*  SELECT SINGLE zcjms    FROM zmmt0039    WHERE zcj = @gs_t06-zcj
*    INTO @gs_t06-zcj_t.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_SET_ZHSQJ  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_set_zhsqj INPUT.

  PERFORM frm_set_zhsqj USING gs_t06
                        CHANGING gt_t15
                                 gt_t10
                                 gt_t11.

ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_SET_ZJSQJ  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_set_zjsqj INPUT.
  PERFORM frm_set_zjsqj USING gs_t06
                        CHANGING gt_t16.

ENDMODULE.
*&---------------------------------------------------------------------*
*& Module STATUS_9200 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_9200 OUTPUT.
  PERFORM frm_set_screen_9100.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_GET_ZFLZFF_T  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_get_zflzff_t INPUT.
  SELECT SINGLE name1 INTO gs_t06-zflzff_t FROM lfa1
    WHERE lifnr = gs_t06-zflzff.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_GET_ZFLSQF_T  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_get_zflsqf_t INPUT.
  SELECT SINGLE butxt INTO gs_t06-zflsqf_t FROM t001
    WHERE bukrs = gs_t06-zflsqf.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_GET_EKGRP_T  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_get_ekgrp_t INPUT.
  DATA:
    lv_flg_err1 TYPE char1.

  SELECT SINGLE eknam INTO gs_t06-ekgrp_t FROM t024
    WHERE ekgrp = gs_t06-ekgrp.

  CLEAR lv_flg_err1.
  PERFORM frm_author_check_ekgrp USING gs_t06-ekgrp
                                       gv_actvt
                                 CHANGING lv_flg_err1.
  IF lv_flg_err1 = 'E'.
    MESSAGE s888(sabapdocu) WITH '您没有采购组:' && gs_t06-ekgrp && ' 的权限 !' DISPLAY LIKE 'E'.
  ENDIF.
ENDMODULE.