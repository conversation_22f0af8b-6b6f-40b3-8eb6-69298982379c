*----------------------------------------------------------------------*
***INCLUDE ZRED0001_F02.
*----------------------------------------------------------------------*
*&---------------------------------------------------------------------*
*& Form FRM_DOWN_PDF
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_T06
*&      --> GT_T06
*&      --> GT_T07
*&      --> GT_T08
*&      --> GT_T09
*&      --> GT_T10
*&      --> GT_T11
*&      --> GT_T10_HS
*&      --> GT_T11_HS
*&      --> GT_T12
*&      --> GT_T13
*&      --> GT_T14
*&      --> GT_T15
*&      --> GT_T16
*&      --> GT_T20
*&      --> GT_T21 structure
*&---------------------------------------------------------------------*
***FORM frm_down_pdf  TABLES
***                                               pt_t06 STRUCTURE zret0006
***                                               pt_t07 STRUCTURE zret0007
***                                               pt_t08 STRUCTURE zret0008
***                                               pt_t09 STRUCTURE zret0009
***                                               pt_t10 STRUCTURE zret0010
***                                               pt_t11 STRUCTURE zret0011
***                                               pt_t12 STRUCTURE zret0012
***                                               pt_t13 STRUCTURE zret0013
***                                               pt_t14 STRUCTURE zret0014
***                                               pt_t15 STRUCTURE zret0015
***                                               pt_t16 STRUCTURE zret0016
***                                               pt_t20 STRUCTURE zret0020
***                                               pt_t21 STRUCTURE zret0021
***                    CHANGING                   pv_mtype TYPE bapi_mtype
***                                               pv_msg   TYPE bapi_msg.
***
***  DATA:
***    lt_t24 TYPE TABLE OF zret0024,
***    lt_t20 TYPE TABLE OF zret0020,
***    lt_t09 TYPE TABLE OF zret0009.
***
***
****  商品组明细需要单独取，因为直接在屏幕更新了
***  IF pt_t07[] IS NOT INITIAL.
***    SELECT
***      *
***      INTO CORRESPONDING FIELDS OF TABLE lt_t20
***      FROM zret0020
***      FOR ALL ENTRIES IN pt_t07
***      WHERE zspz_id = pt_t07-zspz_id.
***
***    SELECT
***      *
***      INTO CORRESPONDING FIELDS OF TABLE lt_t09
***      FROM zret0009
***      FOR ALL ENTRIES IN pt_t07
***      WHERE zspz_id = pt_t07-zspz_id.
***  ENDIF.
***
***
***  CALL FUNCTION 'ZREFM0008'
***    IMPORTING
***      ev_mtype    = pv_mtype
***      ev_msg      = pv_msg
***    TABLES
***      it_zret0006 = pt_t06
***      it_zret0007 = pt_t07
***      it_zret0008 = pt_t08
***      it_zret0009 = lt_t09
***      it_zret0010 = pt_t10
***      it_zret0011 = pt_t11
***      it_zret0012 = pt_t12
***      it_zret0013 = pt_t13
***      it_zret0014 = pt_t14
***      it_zret0015 = pt_t15
***      it_zret0016 = pt_t16
***      it_zret0020 = lt_t20
***      it_zret0021 = pt_t21
***      et_zret0024 = lt_t24.
***
***  IF pv_mtype = 'S'.
***    MODIFY zret0024 FROM TABLE lt_t24.
***    PERFORM frm_send_to_srm TABLES   pt_t06
***                                     lt_t24
***                            CHANGING pv_mtype
***                                     pv_msg.
***  ENDIF.
***ENDFORM.
****&---------------------------------------------------------------------*
****& Form FRM_SEND_TO_SRM
****&---------------------------------------------------------------------*
****& text
****&---------------------------------------------------------------------*
****&      --> LT_T24
****&      <-- PV_MTYPE
****&      <-- PV_MSG
****&---------------------------------------------------------------------*
***FORM frm_send_to_srm  TABLES   pt_t06   STRUCTURE zret0006
***                               pt_t24   STRUCTURE zret0024
***                      CHANGING pv_mtype TYPE bapi_mtype
***                               pv_msg   TYPE bapi_msg.
***
***  DATA:
***    ls_input TYPE zres0006,
***    lt_item  TYPE TABLE OF zres0007,
***    ls_item  TYPE zres0007.
***
***  READ TABLE pt_t06 INTO DATA(ls_t06) INDEX 1.
***
***  ls_input-zxy_id = ls_t06-zxy_id.
***  ls_input-zfllx = ls_t06-zfllx.
***  ls_input-zxy_txt = ls_t06-zxy_txt.
***  ls_input-zhtlx = ls_t06-zhtlx.
***  ls_input-zbukrs = ls_t06-zbukrs.
***  ls_input-zflsqf = ls_t06-zflsqf.
***  ls_input-zflzff = ls_t06-zflzff.
***  ls_input-ekgrp = ls_t06-ekgrp.
***  ls_input-ztzhtbh = ls_t06-zhtid.
***  ls_input-zbegin = ls_t06-zbegin.
***  ls_input-zend = ls_t06-zend.
***  ls_input-zcjr = ls_t06-zcjr.
***  ls_input-zcjrq = ls_t06-zcjrq.
***  ls_input-zcjsj = ls_t06-zcjsj.
***
***  LOOP AT pt_t24 INTO DATA(ls_t24).
***    CLEAR ls_item.
***    ls_item-zfile = ls_t24-zfile.
***    APPEND ls_item TO lt_item.
***  ENDLOOP.
***
***  ls_input-zitem[] = lt_item[].
***
***
***  CALL FUNCTION 'ZREFM0007'
***    EXPORTING
***      i_input          = ls_input
***    IMPORTING
***      ev_type          = pv_mtype
***      ev_message       = pv_msg
***    EXCEPTIONS
***      system_exception = 1
***      OTHERS           = 2.
***  IF sy-subrc <> 0.
**** Implement suitable error handling here
***  ENDIF.
***
***ENDFORM.