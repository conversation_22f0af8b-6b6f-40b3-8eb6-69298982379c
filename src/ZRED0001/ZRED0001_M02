*&---------------------------------------------------------------------*
*& Include ZRED0001_M02
*&---------------------------------------------------------------------*

*&SPWIZARD: FUNCTION CODES FOR TABSTRIP 'TG_HEAD'
CONSTANTS: BEGIN OF C_TG_HEAD,
             TAB1 LIKE SY-UCOMM VALUE 'TG_HEAD_FC1',
             TAB2 LIKE SY-UCOMM VALUE 'TG_HEAD_FC2',
             TAB3 LIKE SY-UCOMM VALUE 'TG_HEAD_FC3',
             TAB4 LIKE SY-UCOMM VALUE 'TG_HEAD_FC4',
             TAB5 LIKE SY-UCOMM VALUE 'TG_HEAD_FC5',
           END OF C_TG_HEAD.
*&SPWIZARD: DATA FOR TABSTRIP 'TG_HEAD'
CONTROLS:  TG_HEAD TYPE TABSTRIP.
DATA:      BEGIN OF G_TG_HEAD,
             SUBSCREEN   LIKE SY-DYNNR,
             PROG        LIKE SY-REPID VALUE 'ZRED0001',
             PRESSED_TAB LIKE SY-UCOMM VALUE C_TG_HEAD-TAB1,
           END OF G_TG_HEAD.
*DATA:      OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TS 'TG_HEAD'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: SETS ACTIVE TAB
MODULE TG_HEAD_ACTIVE_TAB_SET OUTPUT.
  TG_HEAD-ACTIVETAB = G_TG_HEAD-PRESSED_TAB.
  CASE G_TG_HEAD-PRESSED_TAB.
    WHEN C_TG_HEAD-TAB1.
      G_TG_HEAD-SUBSCREEN = '9121'.
    WHEN C_TG_HEAD-TAB2.
      G_TG_HEAD-SUBSCREEN = '9122'.
    WHEN C_TG_HEAD-TAB3.
      G_TG_HEAD-SUBSCREEN = '9123'.
    WHEN C_TG_HEAD-TAB4.
      G_TG_HEAD-SUBSCREEN = '9124'.
    WHEN C_TG_HEAD-TAB5.
      G_TG_HEAD-SUBSCREEN = '9125'.
    WHEN OTHERS.
*&SPWIZARD:      DO NOTHING
  ENDCASE.
ENDMODULE.


*&SPWIZARD: INPUT MODULE FOR TS 'TG_HEAD'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GETS ACTIVE TAB
MODULE TG_HEAD_ACTIVE_TAB_GET INPUT.
  OK_CODE = SY-UCOMM.
  CASE OK_CODE.
    WHEN C_TG_HEAD-TAB1.
      G_TG_HEAD-PRESSED_TAB = C_TG_HEAD-TAB1.
    WHEN C_TG_HEAD-TAB2.
      G_TG_HEAD-PRESSED_TAB = C_TG_HEAD-TAB2.
    WHEN C_TG_HEAD-TAB3.
      G_TG_HEAD-PRESSED_TAB = C_TG_HEAD-TAB3.
    WHEN C_TG_HEAD-TAB4.
      G_TG_HEAD-PRESSED_TAB = C_TG_HEAD-TAB4.
    WHEN C_TG_HEAD-TAB5.
      G_TG_HEAD-PRESSED_TAB = C_TG_HEAD-TAB5.
    WHEN OTHERS.
*&SPWIZARD:      DO NOTHING
  ENDCASE.
ENDMODULE.