*&---------------------------------------------------------------------*
*& 包含               ZRED0001_T01
*&---------------------------------------------------------------------*

TYPE-POOLS:slis,vrm.

TABLES:
  zret0012,
  zret0013,
  t001w,
  t001,
  lfa1.

*协议抬头数据
TYPES:
  BEGIN OF ty_t06.
    INCLUDE TYPE zret0006.
TYPES:
  zghf_t     TYPE char20,
  lifnr_t    TYPE char20,
  bukrs_t    TYPE char20,
  dcwrk_t    TYPE char20,
  werks_t    TYPE char20,
  ekorg_t    TYPE char20,
  zcj_t      TYPE char20,

  zflzff_t   TYPE char35,
  zflsqf_t   TYPE char35,
  ekgrp_t    TYPE char35,
  zbukrs_t   TYPE char35,

  zghf_t_n   TYPE char20,
  lifnr_t_n  TYPE char20,
  bukrs_t_n  TYPE char20,
  dcwrk_t_n  TYPE char20,
  werks_t_n  TYPE char20,
  ekorg_t_n  TYPE char20,

  zflzff_t_n TYPE char35,
  zflsqf_t_n TYPE char35,
  ekgrp_t_n  TYPE char35,
  zbukrs_t_n TYPE char35,

  END OF ty_t06,
  tt_t06 TYPE TABLE OF ty_t06.

*协议明细数据-任务动态协议
TYPES:
  BEGIN OF ty_t07.
    INCLUDE TYPE zret0007.
TYPES:
  maktx      TYPE makt-maktx,
  meins      TYPE mara-meins,
  zspzid_txt TYPE zret0009-zspzid_txt,
  sel        TYPE char1,
  END OF ty_t07,
  tt_t07 TYPE TABLE OF ty_t07.

*协议明细数据-返利动态协议
TYPES:
  BEGIN OF ty_t39.
    INCLUDE TYPE zret0039.
TYPES:
  maktx      TYPE makt-maktx,
  meins      TYPE mara-meins,
  zspzid_txt TYPE zret0009-zspzid_txt,
  sel        TYPE char1,
  END OF ty_t39,
  tt_t39 TYPE TABLE OF ty_t39.

*协议明细数据-固定协议
TYPES:
  BEGIN OF ty_t08.
    INCLUDE TYPE zret0008.
TYPES:
  sel   TYPE char1,
  maktx TYPE makt-maktx,
  meins TYPE mara-meins,
  name1 TYPE lfa1-name1,
  END OF ty_t08,
  tt_t08 TYPE TABLE OF ty_t08.

*商品组抬头数据
TYPES:
  BEGIN OF ty_t09.
    INCLUDE TYPE zret0009.
TYPES:
  sel         TYPE char1,
  lines_sum   TYPE i,
  lines_cur   TYPE i,
  zspz_id_tmp TYPE zret0009-zspz_id,
  END OF ty_t09,
  tt_t09 TYPE TABLE OF ty_t09.

*阶梯抬头数据
TYPES:
  BEGIN OF ty_t10.
    INCLUDE TYPE zret0010.
TYPES:
  zjt_id_tmp TYPE zret0010-zjt_id,
  END OF ty_t10,
  tt_t10 TYPE TABLE OF ty_t10.

*阶梯明细数据
TYPES:
  BEGIN OF ty_t11.
    INCLUDE TYPE zret0011.
TYPES:
  sel      TYPE char1,
  zflg_1st TYPE char1,  "首行标识
  zflg_del TYPE char1,  "删除标识
  END OF ty_t11,
  tt_t11 TYPE TABLE OF ty_t11.

*渠道供应商数据
TYPES:
  BEGIN OF ty_t12.
    INCLUDE TYPE zret0012.
TYPES:
  sel   TYPE char1,
  name1 TYPE lfa1-name1,
  END OF ty_t12,
  tt_t12 TYPE TABLE OF ty_t12.

*供货方数据（任务）
TYPES:
  BEGIN OF ty_t13.
    INCLUDE TYPE zret0013.
TYPES:
  sel   TYPE char1,
  name1 TYPE lfa1-name1,
  END OF ty_t13,
  tt_t13 TYPE TABLE OF ty_t13.

*供货方数据(返利）
TYPES:
  BEGIN OF ty_t38.
    INCLUDE TYPE zret0038.
TYPES:
  sel   TYPE char1,
  name1 TYPE lfa1-name1,
  END OF ty_t38,
  tt_t38 TYPE TABLE OF ty_t38.

*组织结构表(任务）
TYPES:
  BEGIN OF ty_t14.
    INCLUDE TYPE zret0014.
TYPES:
END OF ty_t14,
tt_t14 TYPE TABLE OF ty_t14.

*组织结构表（返利)
TYPES:
  BEGIN OF ty_t37.
    INCLUDE TYPE zret0037.
TYPES:
END OF ty_t37,
tt_t37 TYPE TABLE OF ty_t37.

*核算期间表
TYPES:
  BEGIN OF ty_t15.
    INCLUDE TYPE zret0015.
TYPES:
  sel TYPE char1,
  END OF ty_t15,
  tt_t15 TYPE TABLE OF ty_t15.

*结算期间表
TYPES:
  BEGIN OF ty_t16.
    INCLUDE TYPE zret0016.
TYPES:
  sel TYPE char1,
  END OF ty_t16,
  tt_t16 TYPE TABLE OF ty_t16.

*商品组明细
TYPES:
  BEGIN OF ty_t20.
    INCLUDE TYPE zret0020.
TYPES:
  sel   TYPE char1,
  maktx TYPE makt-maktx,
  meins TYPE mara-meins,
  END OF ty_t20,
  tt_t20 TYPE TABLE OF ty_t20.

*生产商数据
TYPES:
  BEGIN OF ty_t21.
    INCLUDE TYPE zret0021.
TYPES:
  sel   TYPE char1,
  zcjms TYPE zmmt0039-zcjms,
  END OF ty_t21,
  tt_t21 TYPE TABLE OF ty_t21.

*公司代码
TYPES:
  BEGIN OF ty_bukrs,
    sel     TYPE char1,
    bukrs   TYPE t001-bukrs,
    butxt   TYPE t001-butxt,
    exclude TYPE char1,
  END OF ty_bukrs,
  tt_bukrs TYPE TABLE OF ty_bukrs,

*  DC代码
  BEGIN OF ty_dcwrk,
    sel     TYPE char1,
    dcwrk   TYPE t001w-werks,
    name1   TYPE t001w-name1,
    exclude TYPE char1,
  END OF ty_dcwrk,
  tt_dcwrk TYPE TABLE OF ty_dcwrk,

*  门店代码
  BEGIN OF ty_werks,
    sel     TYPE char1,
    werks   TYPE t001w-werks,
    name1   TYPE t001w-name1,
    exclude TYPE char1,
  END OF ty_werks,
  tt_werks TYPE TABLE OF ty_werks,

*  采购组织
  BEGIN OF ty_ekorg,
    sel     TYPE char1,
    ekorg   TYPE t024e-ekorg,
    ekotx   TYPE t024e-ekotx,
    exclude TYPE char1,
  END OF ty_ekorg,
  tt_ekorg TYPE TABLE OF ty_ekorg.

*  期间表
TYPES:
  BEGIN OF ty_zqj,
    zbegin TYPE zret0015-zbegin,
    zend   TYPE zret0015-zend,
  END OF ty_zqj,
  tt_zqj TYPE TABLE OF ty_zqj.

*  组织结构层级检查表
TYPES:
  BEGIN OF ty_wrk_check,
    werks   TYPE t001w-werks,
    exclude TYPE char1,
  END OF ty_wrk_check,
  tt_wrk_check TYPE TABLE OF ty_wrk_check.

*  支付方清单
TYPES:
  BEGIN OF ty_t44.
    INCLUDE TYPE zret0044.
TYPES:
  name1 TYPE lfa1-name1,
  sel   TYPE char1,
  END OF ty_t44,
  tt_t44 TYPE TABLE OF ty_t44.


*----------------------------------------------------------------------*
*BEGIN INSERT BY  XYLIU1  19.05.2020 11:15:17
*<NOTES>  返利三期新增
DATA:
  gt_bukrs_n     TYPE tt_bukrs,
  gs_bukrs_n     TYPE LINE OF tt_bukrs,
  gt_dcwrk_n     TYPE tt_dcwrk,
  gs_dcwrk_n     TYPE LINE OF tt_dcwrk,
  gt_werks_n     TYPE tt_werks,
  gs_werks_n     TYPE LINE OF tt_werks,
  gt_ekorg_n     TYPE tt_ekorg,
  gs_ekorg_n     TYPE LINE OF tt_ekorg,

  gt_t10_js      TYPE tt_t10,      "返利计算子阶梯抬头表
  gt_t10_js_sub  TYPE tt_t10,      "返利计算子阶梯屏幕表（对应单个阶梯行项目）
  gt_t11_js      TYPE tt_t11,      "返利计算子阶梯明细表
  gt_t11_js_sub  TYPE tt_t11,      "返利计算子阶梯明细表（对应单个阶梯行项目）

  gt_t10_jsh     TYPE tt_t10,      "核算周期对应的计算阶梯抬头表
  gt_t10_jsh_sub TYPE tt_t10,      "核算周期对应的计算阶梯屏幕表（对应单个阶梯行项目）
  gt_t11_jsh     TYPE tt_t11,      "核算周期对应的计算阶梯明细表
  gt_t11_jsh_sub TYPE tt_t11,      "核算周期对应的计算阶梯明细表（对应单个阶梯行项目）

  gs_t10_js      TYPE ty_t10,
  gs_t10_js_sub  TYPE ty_t10,
  gs_t11_js      TYPE ty_t11,
  gs_t11_js_sub  TYPE ty_t11,
  gs_t10_jsh     TYPE ty_t10,
  gs_t10_jsh_sub TYPE ty_t10,
  gs_t11_jsh     TYPE ty_t11,
  gs_t11_jsh_sub TYPE ty_t11.

*END INSERT BY XYLIU1
*----------------------------------------------------------------------*



DATA:
  gt_t06        TYPE tt_t06,
  gt_t07        TYPE tt_t07,
  gt_t39        TYPE tt_t39,
  gt_t08        TYPE tt_t08,
  gt_t09        TYPE tt_t09,
  gt_t10        TYPE tt_t10,
  gt_t10_null   TYPE tt_t10,
  gt_t10_hs     TYPE tt_t10,      "核算子阶梯抬头表
  gt_t10_hs_sub TYPE tt_t10,      "核算子阶梯屏幕表（对应单个核算期间行项目）
  gt_t11        TYPE tt_t11,
  gt_t11_hs     TYPE tt_t11,      "核算子阶梯明细表
  gt_t11_hs_sub TYPE tt_t11,      "核算子阶梯明细表（对应单个核算期间行项目）
  gt_t12        TYPE tt_t12,
  gt_t13        TYPE tt_t13,
  gt_t38        TYPE tt_t38,
  gt_t14        TYPE tt_t14,
  gt_t37        TYPE tt_t37,
  gt_t15        TYPE tt_t15,
  gt_t16        TYPE tt_t16,
  gt_t20        TYPE tt_t20,
  gt_t20_sub    TYPE tt_t20,
  gt_t21        TYPE tt_t21.

DATA:
  gs_t06        TYPE ty_t06,
  gs_t07        TYPE ty_t07,
  gs_t39        TYPE ty_t39,
  gs_t08        TYPE ty_t08,
  gs_t09        TYPE ty_t09,
  gs_t10        TYPE ty_t10,
  gs_t11        TYPE ty_t11,
  gs_t10_hs     TYPE ty_t10,
  gs_t10_hs_sub TYPE ty_t10,
  gs_t11_hs     TYPE ty_t11,
  gs_t11_hs_sub TYPE ty_t11,
  gs_t12        TYPE ty_t12,
  gs_t13        TYPE ty_t13,
  gs_t38        TYPE ty_t38,
  gs_t14        TYPE ty_t14,
  gs_t37        TYPE ty_t37,
  gs_t15        TYPE ty_t15,
  gs_t16        TYPE ty_t16,
  gs_t20        TYPE ty_t20,
  gs_t20_sub    TYPE ty_t20,
  gs_t21        TYPE ty_t21.

DATA:
  gt_t44 TYPE tt_t44,
  gs_t44 TYPE ty_t44.

DATA:
  gt_bukrs TYPE tt_bukrs,
  gs_bukrs TYPE LINE OF tt_bukrs,

  gt_dcwrk TYPE tt_dcwrk,
  gs_dcwrk TYPE LINE OF tt_dcwrk,

  gt_werks TYPE tt_werks,
  gs_werks TYPE LINE OF tt_werks,

  gt_ekorg TYPE tt_ekorg,
  gs_ekorg TYPE LINE OF tt_ekorg.

*  下拉框数据
DATA:
  gt_vls_zjsff TYPE vrm_values,
  gt_vls_zhtlx TYPE vrm_values,
  gt_vls_zfllx TYPE vrm_values,
  gt_vls_zhszq TYPE vrm_values,
  gt_vls_zjszq TYPE vrm_values,
  gt_vls_zhsjz TYPE vrm_values.


DATA:
  ok_code          TYPE sy-ucomm,                          "
  lv_code          TYPE sy-ucomm,                          "
  gv_zjt_fz_t      TYPE char10,                            "分子
  gv_zjt_fm_t      TYPE char10,                            "分母
  gv_zxybstyp      TYPE zret0006-zxybstyp,                 "协议类型
*  gv_flg_err            TYPE char1,                             "
*  gv_msg                TYPE char30,                            "
  gv_cursor_line   TYPE i,                                 "光标行
  gv_index_line    TYPE i,                                 "内表行
  gv_flg_comm      TYPE char10,                            "command 标识
  gv_flg_spz_exist TYPE char1,                             "商品组新增标识
  gv_title         TYPE char20,                            "程序标题
  gv_title_01      TYPE char20,                            "主屏幕标题1
  gv_title_02      TYPE char20.                            "主屏幕标题2

CONSTANTS:
  gc_max_dec TYPE  zret0011-zjt_by VALUE '999999999999999', "阶梯 至最大值
  gc_all     TYPE c LENGTH 4 VALUE 'ALL'.           "组织机构 写入ALL值

DATA:
    gv_actvt    TYPE activ_auth.
DATA:
  gv_icon    TYPE icon-id,
  gv_icon_hs TYPE icon-id.
DATA:
  gv_ght_t   TYPE char6,
  gv_lifnr_t TYPE char6.

DATA:

  gt_msglist TYPE scp1_general_errors,
  gv_flg_err TYPE char1.



TYPES:
  BEGIN OF ty_data_9125.
*&&&&&    INCLUDE TYPE zret0025.
TYPES:
  sel TYPE char1,
  END OF ty_data_9125,
  tt_data_9125 TYPE TABLE OF ty_data_9125.
DATA:
      gt_data_9125 TYPE tt_data_9125.