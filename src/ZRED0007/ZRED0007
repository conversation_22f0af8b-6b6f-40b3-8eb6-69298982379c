*&---------------------------------------------------------------------*
*& Report ZRED0007
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
report zred0007.
tables:zret0046,zret0047,zret0048,zret0052.
*&---------------------------------------------------------------------*
* SELECTION-SCREEN
*&---------------------------------------------------------------------*
data:lv_answer(1).
data:lv_text(100).


selection-screen begin of block a1 with frame title text-001.
parameters: p_0046 type c radiobutton group  a.
parameters: p_0047 type c radiobutton group  a.
parameters: p_0048 type c radiobutton group  a.
parameters: p_0052 type c radiobutton group  a.
parameters: p_0000 type c radiobutton group  a.
parameters: p_0026 type c radiobutton group  a.
selection-screen end of block a1.

selection-screen begin of block a2 with frame title text-002.
select-options: s_batid for zret0046-batid modif id b1 .
select-options: s_budat for zret0046-budat modif id b1 .
select-options: s_erdat for zret0048-erdat modif id b2 .
selection-screen end of block a2.




case  'X'.
  when p_0046.
    perform frm_0046.
  when p_0047.
    perform frm_0047.
  when p_0048.
    perform frm_0048.
  when p_0052.
    perform frm_0052.
  when p_0000.
    perform frm_47to46.
  when p_0026.
    perform frm_0026.
  when others.
endcase.




*&---------------------------------------------------------------------*
*& Form FRM_ASK_CONFIRM
*&---------------------------------------------------------------------*
*& 再次确认数据:弹出框
*&---------------------------------------------------------------------*
*&      --> P_
*&      <-- LV_ANSWER
*&---------------------------------------------------------------------*
form frm_ask_confirm    using  lv_text
                      changing lv_answer.

  call function 'POPUP_TO_CONFIRM'                "弹出小窗口
    exporting
      titlebar      = '确认'
      text_question = lv_text
    importing
      answer        = lv_answer.

endform.
*&---------------------------------------------------------------------*
*& Form FRM_0046
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
form frm_0046 .
  data:lt_zret0046 type table of zret0046.

  select * into table lt_zret0046 from zret0046 where batid in  s_batid and  budat in  s_budat.
  if lt_zret0046[] is not initial .
    lv_text = |确认：删除表ZRET0046数据，符合条件数据{ lines( lt_zret0046 ) }条？|.
    perform frm_ask_confirm    using lv_text
                            changing lv_answer.

    if lv_answer = '1' .
      delete zret0046 from table lt_zret0046.
      commit work and wait.
      message '删除成功！' type 'I'.
    else.
      message '删除已取消！' type 'I'.
    endif.
  else.
    message '无符合条件的数据！' type 'I'.
  endif.
endform.
*&---------------------------------------------------------------------*
*& Form FRM_0047
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
form frm_0047 .
  data:lt_zret0047 type table of zret0047.

  select * into table lt_zret0047 from zret0047 where batid in  s_batid and  budat in  s_budat.
  if lt_zret0047[] is not initial .
    lv_text = |确认：删除表ZRET0046数据，符合条件数据{ lines( lt_zret0047 ) }条？|.
    perform frm_ask_confirm    using lv_text
                            changing lv_answer.

    if lv_answer = '1' .
      delete zret0047 from table lt_zret0047.
      commit work and wait.
      message '删除成功！' type 'I'.
    else.
      message '删除已取消！' type 'I'.
    endif.
  else.
    message '无符合条件的数据！' type 'I'.
  endif.
endform.
*&---------------------------------------------------------------------*
*& Form FRM_0048
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
form frm_0048 .

  data:lt_zret0048 type table of zret0048.

  select * into table lt_zret0048 from zret0048 where batid in  s_batid and  budat in  s_budat.
  if lt_zret0048[] is not initial .
    lv_text = |确认：删除表ZRET0046数据，符合条件数据{ lines( lt_zret0048 ) }条？|.
    perform frm_ask_confirm    using lv_text
                            changing lv_answer.

    if lv_answer = '1' .
      delete zret0048 from table lt_zret0048.
      commit work and wait.
      message '删除成功！' type 'I'.
    else.
      message '删除已取消！' type 'I'.
    endif.
  else.
    message '无符合条件的数据！' type 'I'.
  endif.
endform.
*&---------------------------------------------------------------------*
*& Form FRM_0052
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
form frm_0052 .
  data:lt_zret0052 type table of zret0052.

  select * into table lt_zret0052 from zret0052 where batid in  s_batid and  budat in  s_budat.
  if lt_zret0052[] is not initial .
    lv_text = |确认：删除表ZRET0046数据，符合条件数据{ lines( lt_zret0052 ) }条？|.
    perform frm_ask_confirm    using lv_text
                            changing lv_answer.

    if lv_answer = '1' .
      delete zret0052 from table lt_zret0052.
      commit work and wait.
      message '删除成功！' type 'I'.
    else.
      message '删除已取消！' type 'I'.
    endif.
  else.
    message '无符合条件的数据！' type 'I'.
  endif.
endform.
*&---------------------------------------------------------------------*
*& Form FRM_47TO46
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
form frm_47to46 .
  data:
    ls_zret0048 type zret0048,
    lt_zret0048 type table of zret0048,
    lt_zret0047 type table of zret0047,
    lt_zret0046 type table of zret0046,
    rt_budat    type range of zres0036-budat,
    rt_zbsart   type range of zres0036-zbsart,
    rt_bk01     type range of zres0036-bk01,
    lv_error    type  c.
  select  *
    into table lt_zret0048
    from zret0048
   where batid in s_batid
     and erdat in s_erdat  .

  loop at lt_zret0048 into  ls_zret0048 .

    refresh:rt_budat,
            rt_zbsart,
            rt_bk01.

    if ls_zret0048-acc_re >= ls_zret0048-tot_re.
      if ls_zret0048-budat is not initial .
        rt_budat = value #(  ( sign = 'I' option = 'EQ'  low = ls_zret0048-budat  high = '' )   ).
      endif.
      if ls_zret0048-zbsart is not initial .
        rt_zbsart = value #(  ( sign = 'I' option = 'EQ'  low = ls_zret0048-zbsart  high = '' )   ).
      endif.
      if ls_zret0048-bk01 is not initial .
        rt_bk01 = value #(  ( sign = 'I' option = 'EQ'  low = ls_zret0048-bk01  high = '' )   ).
      endif.
    endif.

    select *
         into corresponding fields of table lt_zret0046
         from zret0047
        where batid  eq ls_zret0048-batid
          and budat  in rt_budat
          and zbsart in rt_zbsart
          and bk01   in rt_bk01 .

    delete from  zret0046 where budat  in rt_budat
                            and zbsart in rt_zbsart
                            and bk01   in rt_bk01 .

    modify zret0046 from table lt_zret0046.

    if sy-subrc <> 0.
      lv_error = 'X'.
    endif.

    if lv_error is initial.
      delete from zret0047 where batid =  ls_zret0048-batid.
      if sy-subrc <> 0.
        lv_error = 'X'.
      endif.
    endif.

    if lv_error is initial.
      commit work.
    else.
      rollback work.
    endif.

  endloop.


endform.
*&---------------------------------------------------------------------*
*& Form FRM_0026
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
form frm_0026 .

  select  count(*)
    into @data(lv_count)
    from zret0026 where zzsl = 0
                          and zje = 0
                          and zje2 = 0
                          and zyssl_s = 0
                          and zysje_s = 0.

  if lv_count is not initial .
    lv_text = |确认：删除表ZRET0026数据，符合条件数据{ lv_count }条？|.
    perform frm_ask_confirm    using lv_text
                            changing lv_answer.

    if lv_answer = '1' .

      delete from zret0026 where zzsl = 0
                              and zje = 0
                              and zje2 = 0
                              and zyssl_s = 0
                              and zysje_s = 0.

      commit work and wait.
      message '删除成功！' type 'I'.
    else.
      message '删除已取消！' type 'I'.
    endif.
  else.
    message '无符合条件的数据！' type 'I'.
  endif.


endform.