*&---------------------------------------------------------------------*
*& Subroutinenpool ZBCS0002
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
PROGRAM zbcs0002.
*&---------------------------------------------------------------------*
*& Form FRM_AGAIN_FL
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_T09_SUB
*&---------------------------------------------------------------------*
FORM frm_again_fl  USING    pv_zspz_id TYPE zret0009-zspz_id .

  SELECT * INTO TABLE @DATA(lt_zreta002) FROM zreta002 WHERE zspz_id = @pv_zspz_id.

  CHECK lt_zreta002[] IS NOT INITIAL.

  LOOP AT lt_zreta002 INTO DATA(ls_zreta0002) .
    PERFORM frm_data_calculate USING ls_zreta0002-ztk_id ''.
    IF ls_zreta0002-zxybstyp = 'P'.
      PERFORM frm_pro_data_101_102 USING ls_zreta0002-ztk_id.
      PERFORM frm_pro_data_bdp USING ls_zreta0002-ztk_id ls_zreta0002-zbegin ls_zreta0002-zend.
    ELSE.
      PERFORM frm_pro_data_116_117 USING ls_zreta0002-ztk_id.
    ENDIF.
  ENDLOOP.


ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_DATA_CALCULATE
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PS_TA02_ZTK_ID
*&---------------------------------------------------------------------*
FORM frm_data_calculate  USING    pv_ztk_id TYPE zreta002-ztk_id
                                  pv_zxy_id TYPE zret0006-zxy_id.

  DATA:lv_job_name TYPE tbtcjob-jobname.
  DATA:lv_guid32 TYPE guid_32 .


  CALL FUNCTION '/SAPSLL/GUID_CREATE'
    IMPORTING
      ev_guid_32 = lv_guid32.

  lv_job_name = sy-cprog && '_' && lv_guid32+28(4) && sy-datum && sy-uzeit.

  CALL FUNCTION 'ZREFM0028'
    STARTING NEW TASK lv_job_name
    EXPORTING
      iv_ztk_id = pv_ztk_id
      iv_zxy_id = pv_zxy_id.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_PRO_DATA_101_102
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PS_TA02_ZTK_ID
*&---------------------------------------------------------------------*
FORM frm_pro_data_101_102  USING    pv_ztk_id TYPE zreta002-ztk_id.

  DATA:
    ls_zres0078 TYPE zres0078,
    lt_zres0078 TYPE TABLE OF zres0078.

  ls_zres0078-ztk_id = pv_ztk_id.
  APPEND ls_zres0078 TO lt_zres0078.


  CALL FUNCTION 'ZREFM0043'
*   IMPORTING
*     OV_TYPE         =
*     OV_MSG          =
    TABLES
      it_ztk_id = lt_zres0078
*     et_t101   =
    .

  CALL FUNCTION 'ZREFM0044'
* IMPORTING
*   OV_TYPE         =
*   OV_MSG          =
    TABLES
      it_ztk_id = lt_zres0078
*     et_t102   =
    .

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_PRO_DATA_101_102
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PS_TA02_ZTK_ID
*&---------------------------------------------------------------------*
FORM frm_pro_data_116_117  USING    pv_ztk_id TYPE zreta002-ztk_id.

  DATA:
    ls_zres0078 TYPE zres0078,
    lt_zres0078 TYPE TABLE OF zres0078.

  ls_zres0078-ztk_id = pv_ztk_id.
  APPEND ls_zres0078 TO lt_zres0078.

  CALL FUNCTION 'ZREFM0082'
*   IMPORTING
*     OV_TYPE         =
*     OV_MSG          =
    TABLES
      it_ztk_id = lt_zres0078
*     et_t116   =
    .
ENDFORM.
FORM frm_pro_data_bdp  USING    pv_ztk_id TYPE zreta002-ztk_id
                                pv_zbegin TYPE zreta002-zbegin
                                pv_zend TYPE zreta002-zend.

  DATA:
        lv_date TYPE d.

*  IF NOT ( pv_zbegin < sy-datum AND pv_zbegin(6) NE sy-datum(6) ).
  IF NOT ( pv_zbegin < sy-datum  ).
    RETURN.
  ENDIF.

  CHECK pv_ztk_id IS NOT INITIAL.

  lv_date = sy-datum(6) && '01'.
  lv_date = lv_date - 1.
  IF lv_date >= pv_zend.
    lv_date = pv_zend.
  ENDIF.


*  PERFORM frm_pro_data_bdp_job USING pv_ztk_id pv_zbegin lv_date.
  PERFORM frm_pro_data_bdp_job USING pv_ztk_id pv_zbegin pv_zend.

ENDFORM.
FORM frm_pro_data_bdp_job  USING    pv_ztk_id TYPE zreta002-ztk_id
                                pv_zbegin TYPE zreta002-zbegin
                                pv_zend TYPE zreta002-zend.


  DATA: lv_job_name     LIKE tbtco-jobname,
        lv_job_nr       LIKE tbtco-jobcount,
        lv_job_released TYPE c.
  DATA:
        lv_guid32 TYPE guid_32.

  DATA:
    lv_time      TYPE t VALUE  '000500',
    lv_sdlstrtdt TYPE d,
    lv_sdlstrttm TYPE t.


  RANGES:
        lr_ztk_id FOR zreta002-ztk_id,
        lr_date   FOR mara-ersda.

  lr_ztk_id-sign = 'I'.
  lr_ztk_id-option = 'EQ'.
  lr_ztk_id-low = pv_ztk_id.
  APPEND lr_ztk_id.


  lr_date-sign = 'I'.
  lr_date-option = 'EQ'.
  lr_date-low = pv_zbegin.
  lr_date-high = pv_zend.
  APPEND lr_date.



  PERFORM frm_get_guid32(zbcs0001) CHANGING lv_guid32.

  lv_job_name = 'ZRED0054' && lv_guid32+10(22).

  CALL FUNCTION 'JOB_OPEN'
    EXPORTING
      jobname          = lv_job_name
    IMPORTING
      jobcount         = lv_job_nr
    EXCEPTIONS
      cant_create_job  = 1
      invalid_job_data = 2
      jobname_missing  = 3
      OTHERS           = 4.
  IF syst-subrc <> 0.
    MESSAGE e001(00) WITH '打开后台作业出错'.
  ENDIF.

  SUBMIT zred0054
    WITH s_ztk_id IN lr_ztk_id
    WITH s_datum IN lr_date
    USER syst-uname
    VIA JOB lv_job_name NUMBER lv_job_nr AND RETURN.
  IF sy-subrc <> 0.
    MESSAGE e001(00) WITH '定义后台作业出错，已经停止运行'.
  ENDIF.


*  CALL FUNCTION 'C14B_ADD_TIME'
*    EXPORTING
*      i_starttime = sy-uzeit
*      i_startdate = sy-datum
*      i_addtime   = lv_time
*    IMPORTING
*      e_endtime   = lv_sdlstrttm
*      e_enddate   = lv_sdlstrtdt.

* ERP-12747
*促销条款审批时，如果审批时间在0:00至7:00，在34小时候启动后台，获取条款开始日期至当前日期的数据；
*               其它时间审批的，24小时候后启动后台，获取条款开始日期至当期日期的数据；

  lv_sdlstrtdt = sy-datum.
  lv_sdlstrttm = sy-uzeit.

  SELECT SINGLE *
    INTO @DATA(ls_zretcm09)
    FROM zretcm09
   WHERE zjbtyp = '1'
     AND ztimfm <= @sy-uzeit
     AND ztimto >= @sy-uzeit.
  IF sy-subrc = 0 AND ls_zretcm09-zlaghs > 0 .
    DO  .
      IF ls_zretcm09-zlaghs >= 24 .
        PERFORM frm_date_calc(zbcs0001) USING 0 0 1 '+' CHANGING lv_sdlstrtdt.
        ls_zretcm09-zlaghs = ls_zretcm09-zlaghs - 24.
      ELSE.
        lv_time+(2)  = ls_zretcm09-zlaghs.
        lv_time+2(4) = '0000'.
        CALL FUNCTION 'C14B_ADD_TIME'
          EXPORTING
            i_starttime = sy-uzeit
            i_startdate = lv_sdlstrtdt
            i_addtime   = lv_time
          IMPORTING
            e_endtime   = lv_sdlstrttm
            e_enddate   = lv_sdlstrtdt.
        EXIT.
      ENDIF.
    ENDDO.
  ELSE.

    IF sy-uzeit <= '070000'.
      PERFORM frm_date_calc(zbcs0001) USING 0 0 1 '+' CHANGING lv_sdlstrtdt.
      lv_time = '100000'.
      CALL FUNCTION 'C14B_ADD_TIME'
        EXPORTING
          i_starttime = sy-uzeit
          i_startdate = lv_sdlstrtdt
          i_addtime   = lv_time
        IMPORTING
          e_endtime   = lv_sdlstrttm
          e_enddate   = lv_sdlstrtdt.
    ELSE.
      PERFORM frm_date_calc(zbcs0001) USING 0 0 1 '+' CHANGING lv_sdlstrtdt.
    ENDIF.
  ENDIF.

  CALL FUNCTION 'JOB_CLOSE'
    EXPORTING
      jobcount             = lv_job_nr
      jobname              = lv_job_name
      sdlstrtdt            = lv_sdlstrtdt
      sdlstrttm            = lv_sdlstrttm
      strtimmed            = 'X'
    IMPORTING
      job_was_released     = lv_job_released
    EXCEPTIONS
      cant_start_immediate = 1
      invalid_startdate    = 2
      jobname_missing      = 3
      job_close_failed     = 4
      job_nosteps          = 5
      job_notex            = 6
      lock_failed          = 7
      OTHERS               = 8.
  IF syst-subrc <> 0.
    MESSAGE '关闭后台作业出错' TYPE 'E'.
  ELSE.
*    MESSAGE s001(00) WITH '后台作业' lv_job_nr '已经从开始运行'.
  ENDIF.

ENDFORM.