*&---------------------------------------------------------------------*
*& 包含               ZRED0006I01
*&---------------------------------------------------------------------*
*&---------------------------------------------------------------------*
*&      Mo<PERSON>le  USER_COMMAND_9000  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE user_command_9000 INPUT.
  CASE ok_code.
    WHEN '&F03' OR '&F15' OR '&F12' .
      LEAVE PROGRAM.
    WHEN OTHERS.
      gv_tcode = ok_code.
      CALL FUNCTION 'ABAP4_CALL_TRANSACTION'
        STARTING NEW TASK 'A'
        EXPORTING
          tcode                   = gv_tcode
*         skip_screen             = ' '
*         mode_val                = 'A'
*         update_val              = 'A'
*        importing
*         subrc                   =
*        tables
*         using_tab               =
*         spagpa_tab              =
*         mess_tab                =
        EXCEPTIONS
          call_transaction_denied = 1
          tcode_invalid           = 2
          OTHERS                  = 3.
  ENDCASE.
ENDMODULE.