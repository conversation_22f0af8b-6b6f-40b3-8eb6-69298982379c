'use client'

import React from 'react'

export default function Diagram10() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-slate-900 p-4">
      <div className="max-w-[2200px] mx-auto h-screen flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center gap-3">
            <h1 className="text-3xl font-bold text-white">返利系统技术方案决策对比</h1>
            <div className="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full text-white text-sm font-semibold">
              领导决策专用 - 技术方案选型
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-10 h-10 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center">
              <div className="w-7 h-7 bg-white rounded-full flex items-center justify-center">
                <div className="w-4 h-4 bg-gradient-to-r from-red-500 to-pink-500 rounded-full"></div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-red-400 font-bold text-lg">商济健康</div>
              <div className="text-red-300 text-sm">COWELL HEALTH</div>
            </div>
          </div>
        </div>

        {/* Main Comparison Diagram */}
        <div className="flex-1 bg-gradient-to-br from-slate-800/50 to-gray-800/50 rounded-3xl p-8 border border-slate-600/50 relative overflow-hidden">
          {/* Architecture SVG */}
          <svg viewBox="0 0 2200 900" className="w-full h-full">
            <defs>
              {/* Gradients */}
              <linearGradient id="solution1Grad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#1d4ed8" stopOpacity="0.9"/>
              </linearGradient>
              <linearGradient id="solution2Grad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#059669" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#047857" stopOpacity="0.9"/>
              </linearGradient>
              <linearGradient id="hanaGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#dc2626" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#991b1b" stopOpacity="0.9"/>
              </linearGradient>
              <linearGradient id="mysqlGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#f59e0b" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#d97706" stopOpacity="0.9"/>
              </linearGradient>
              <linearGradient id="bdpGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#7c3aed" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#5b21b6" stopOpacity="0.9"/>
              </linearGradient>

              {/* Arrow markers */}
              <marker id="dataArrow" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
                <polygon points="0 0, 12 4, 0 8" fill="#0891b2"/>
              </marker>
              <marker id="processArrow" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
                <polygon points="0 0, 12 4, 0 8" fill="#059669"/>
              </marker>
            </defs>

            {/* Background grid */}
            <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
              <path d="M 50 0 L 0 0 0 50" fill="none" stroke="#374151" strokeWidth="1" opacity="0.2"/>
            </pattern>
            <rect width="2200" height="900" fill="url(#grid)"/>

            {/* Title */}
            <text x="1100" y="40" textAnchor="middle" fontSize="32" fontWeight="bold" fill="#f8fafc">
              返利系统技术方案决策对比分析
            </text>
            <text x="1100" y="65" textAnchor="middle" fontSize="16" fill="#94a3b8">
              方案一：HANA+MySQL混合架构 vs 方案二：MySQL+微服务架构
            </text>

            {/* Solution Headers */}
            <g id="solutionHeaders">
              <rect x="50" y="100" width="1000" height="60" rx="12" fill="url(#solution1Grad)" stroke="#3b82f6" strokeWidth="3"/>
              <text x="550" y="125" textAnchor="middle" fontSize="24" fontWeight="bold" fill="white">方案一：HANA+MySQL 混合架构</text>
              <text x="550" y="145" textAnchor="middle" fontSize="14" fill="white">复用HANA数据源 + BDP预处理天推HANA + MySQL规则配置</text>
              
              <rect x="1150" y="100" width="1000" height="60" rx="12" fill="url(#solution2Grad)" stroke="#059669" strokeWidth="3"/>
              <text x="1650" y="125" textAnchor="middle" fontSize="24" fontWeight="bold" fill="white">方案二：纯MySQL+微服务架构</text>
              <text x="1650" y="145" textAnchor="middle" fontSize="14" fill="white">[HANA+DBP抽取] + [BDP预处理推MySQL] → MySQL统一库 → 微服务引擎 → 返利结果</text>
            </g>

            {/* Solution 1 Architecture */}
            <g id="solution1">
              {/* Data Sources Block - HANA and BDP side by side */}
              <rect x="70" y="180" width="450" height="160" rx="10" fill="rgba(55, 65, 81, 0.3)" stroke="#6b7280" strokeWidth="2" strokeDasharray="5,5"/>
              <text x="295" y="200" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">数据源层 (并列)</text>

              {/* SAP HANA - Top */}
              <rect x="90" y="210" width="360" height="50" rx="8" fill="url(#hanaGrad)" stroke="#dc2626" strokeWidth="2"/>
              <text x="270" y="230" textAnchor="middle" fontSize="15" fontWeight="bold" fill="white">SAP HANA 核心数据源</text>
              <text x="100" y="245" fontSize="10" fill="white">• 采购单据EKKO/EKPO • 配送单据LIKP/LIPS • 销售数据 • 实时业务数据</text>

              {/* BDP Platform - Bottom */}
              <rect x="90" y="270" width="360" height="50" rx="8" fill="url(#bdpGrad)" stroke="#7c3aed" strokeWidth="2"/>
              <text x="270" y="290" textAnchor="middle" fontSize="15" fontWeight="bold" fill="white">BDP数据平台 预处理</text>
              <text x="100" y="305" fontSize="10" fill="white">• 数据清洗整合 • 业务规则处理 • 质量检查校验 • 数据标准化</text>

              {/* MySQL Rules Block */}
              <rect x="540" y="200" width="200" height="120" rx="10" fill="url(#mysqlGrad)" stroke="#f59e0b" strokeWidth="2"/>
              <text x="640" y="225" textAnchor="middle" fontSize="16" fontWeight="bold" fill="white">MySQL</text>
              <text x="640" y="245" textAnchor="middle" fontSize="12" fill="white">规则配置层</text>
              <text x="550" y="265" fontSize="11" fill="white">• 返利规则配置</text>
              <text x="550" y="280" fontSize="11" fill="white">• 算法参数设置</text>
              <text x="550" y="295" fontSize="11" fill="white">• 条款协议管理</text>
              <text x="550" y="310" fontSize="11" fill="white">• 计算逻辑存储</text>

              {/* Hybrid Computing Block */}
              <rect x="760" y="200" width="200" height="120" rx="10" fill="rgba(59, 130, 246, 0.8)" stroke="#3b82f6" strokeWidth="2"/>
              <text x="860" y="225" textAnchor="middle" fontSize="16" fontWeight="bold" fill="white">混合计算引擎</text>
              <text x="860" y="245" textAnchor="middle" fontSize="12" fill="white">HANA+MySQL</text>
              <text x="770" y="265" fontSize="11" fill="white">• 跨数据库查询</text>
              <text x="770" y="280" fontSize="11" fill="white">• 返利算法计算</text>
              <text x="770" y="295" fontSize="11" fill="white">• 结果输出MySQL</text>
              <text x="770" y="310" fontSize="11" fill="white">• 报表数据生成</text>


              {/* Solution 1 Advantages */}
              <rect x="70" y="350" width="950" height="80" rx="10" fill="rgba(34, 197, 94, 0.2)" stroke="#22c55e" strokeWidth="2"/>
              <text x="545" y="380" textAnchor="middle" fontSize="24" fontWeight="bold" fill="#22c55e">方案一优势</text>
              <text x="545" y="405" textAnchor="middle" fontSize="18" fill="white">实施简单 • 风险可控 • 快速见效 • 兼容性强</text>

              {/* Solution 1 Disadvantages */}
              <rect x="70" y="450" width="950" height="80" rx="10" fill="rgba(239, 68, 68, 0.2)" stroke="#ef4444" strokeWidth="2"/>
              <text x="545" y="480" textAnchor="middle" fontSize="24" fontWeight="bold" fill="#ef4444">方案一劣势</text>
              <text x="545" y="505" textAnchor="middle" fontSize="18" fill="white">技术债务 • 性能瓶颈 • 扩展限制 • 运维负担</text>

              {/* Solution 1 Metrics */}
              <rect x="70" y="550" width="950" height="100" rx="10" fill="rgba(55, 65, 81, 0.5)" stroke="#6b7280" strokeWidth="2"/>
              <text x="545" y="575" textAnchor="middle" fontSize="16" fontWeight="bold" fill="white">方案一关键指标</text>
              <text x="80" y="595" fontSize="12" fill="white">📊 技术风险：低 | 团队适应性：高 | 业务中断风险：极低</text>
              <text x="80" y="615" fontSize="12" fill="white">📊 性能提升：30% | 扩展性：有限 | 数据一致性：中等</text>
              <text x="80" y="635" fontSize="12" fill="white">📊 运维复杂度：中等 | 技术债务：高 | 未来升级难度：高</text>

              {/* Data Flow Arrows for Solution 1 */}
              <path d="M 520 260 L 540 260" stroke="#0891b2" strokeWidth="4" fill="none" markerEnd="url(#dataArrow)"/>
              <path d="M 740 260 L 760 260" stroke="#0891b2" strokeWidth="4" fill="none" markerEnd="url(#dataArrow)"/>
            </g>

            {/* Solution 2 Architecture */}
            <g id="solution2">
              {/* Combined Data Processing Block */}
              <rect x="1170" y="200" width="440" height="140" rx="10" fill="rgba(55, 65, 81, 0.3)" stroke="#6b7280" strokeWidth="2" strokeDasharray="5,5"/>
              <text x="1390" y="220" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">数据源+抽取+预处理</text>

              {/* SAP HANA - First row */}
              <rect x="1180" y="230" width="200" height="30" rx="6" fill="url(#hanaGrad)" stroke="#dc2626" strokeWidth="2"/>
              <text x="1280" y="250" textAnchor="middle" fontSize="12" fontWeight="bold" fill="white">SAP HANA 核心数据源</text>

              {/* DBP Sync - Second row */}
              <rect x="1180" y="265" width="200" height="30" rx="6" fill="rgba(8, 145, 178, 0.8)" stroke="#0891b2" strokeWidth="2"/>
              <text x="1280" y="285" textAnchor="middle" fontSize="12" fontWeight="bold" fill="white">DBP抽取同步 T-1增量</text>

              {/* BDP Processing - Third row */}
              <rect x="1180" y="300" width="200" height="30" rx="6" fill="url(#bdpGrad)" stroke="#7c3aed" strokeWidth="2"/>
              <text x="1280" y="320" textAnchor="middle" fontSize="12" fontWeight="bold" fill="white">BDP预处理 数据加工</text>

              {/* Details on the right */}
              <text x="1400" y="245" fontSize="10" fill="white">• EKKO/EKPO采购 • LIKP/LIPS配送</text>
              <text x="1400" y="260" fontSize="10" fill="white">• 销售数据 • 实时业务数据</text>
              <text x="1400" y="280" fontSize="10" fill="white">• 定时抽取 • 增量推送MySQL</text>
              <text x="1400" y="295" fontSize="10" fill="white">• 页面手动实时同步</text>
              <text x="1400" y="315" fontSize="10" fill="white">• 数据清洗整合 • 业务规则处理</text>
              <text x="1400" y="330" fontSize="10" fill="white">• 质量检查校验 • 数据标准化</text>

              {/* MySQL Unified Block */}
              <rect x="1630" y="200" width="180" height="140" rx="10" fill="url(#mysqlGrad)" stroke="#f59e0b" strokeWidth="2"/>
              <text x="1720" y="225" textAnchor="middle" fontSize="16" fontWeight="bold" fill="white">MySQL统一库</text>
              <text x="1720" y="245" textAnchor="middle" fontSize="12" fill="white">增量+全量表</text>
              <text x="1640" y="265" fontSize="11" fill="white">• 增量表接收</text>
              <text x="1640" y="280" fontSize="11" fill="white">• 全量表更新</text>
              <text x="1640" y="295" fontSize="11" fill="white">• 返利规则存储</text>
              <text x="1640" y="310" fontSize="11" fill="white">• 插入/更新机制</text>
              <text x="1640" y="325" fontSize="11" fill="white">• 数据一致性校验</text>

              {/* Microservice Computing Block */}
              <rect x="1830" y="200" width="180" height="140" rx="10" fill="rgba(5, 150, 105, 0.8)" stroke="#059669" strokeWidth="2"/>
              <text x="1920" y="225" textAnchor="middle" fontSize="16" fontWeight="bold" fill="white">微服务引擎</text>
              <text x="1920" y="245" textAnchor="middle" fontSize="12" fill="white">纯MySQL计算</text>
              <text x="1840" y="265" fontSize="11" fill="white">• 分布式计算</text>
              <text x="1840" y="280" fontSize="11" fill="white">• 弹性扩容</text>
              <text x="1840" y="295" fontSize="11" fill="white">• 返利算法计算</text>
              <text x="1840" y="310" fontSize="11" fill="white">• 结果实时输出</text>
              <text x="1840" y="325" fontSize="11" fill="white">• 报表数据生成</text>

              {/* Results Flow Arrow */}
              <path d="M 2010 270 L 2030 270" stroke="#22c55e" strokeWidth="3" fill="none" markerEnd="url(#processArrow)"/>

              {/* Results Storage */}
              <rect x="2030" y="200" width="160" height="140" rx="10" fill="rgba(34, 197, 94, 0.3)" stroke="#22c55e" strokeWidth="2"/>
              <text x="2110" y="225" textAnchor="middle" fontSize="16" fontWeight="bold" fill="white">返利结果</text>
              <text x="2110" y="245" textAnchor="middle" fontSize="12" fill="white">实时存储</text>
              <text x="2040" y="270" fontSize="11" fill="white">• MySQL统一存储</text>
              <text x="2040" y="285" fontSize="11" fill="white">• 实时报表生成</text>
              <text x="2040" y="300" fontSize="11" fill="white">• 数据实时更新</text>
              <text x="2040" y="315" fontSize="11" fill="white">• 多维度分析</text>
              <text x="2040" y="330" fontSize="11" fill="white">• 业务决策支持</text>

              {/* Solution 2 Advantages */}
              <rect x="1170" y="350" width="1020" height="80" rx="10" fill="rgba(34, 197, 94, 0.2)" stroke="#22c55e" strokeWidth="2"/>
              <text x="1680" y="380" textAnchor="middle" fontSize="24" fontWeight="bold" fill="#22c55e">方案二优势</text>
              <text x="1680" y="405" textAnchor="middle" fontSize="18" fill="white">架构先进 • 性能卓越 • 成本优化 • 扩展性强</text>

              {/* Solution 2 Disadvantages */}
              <rect x="1170" y="450" width="1020" height="80" rx="10" fill="rgba(239, 68, 68, 0.2)" stroke="#ef4444" strokeWidth="2"/>
              <text x="1680" y="480" textAnchor="middle" fontSize="24" fontWeight="bold" fill="#ef4444">方案二劣势</text>
              <text x="1680" y="505" textAnchor="middle" fontSize="18" fill="white">实施复杂 • 技术挑战 • 切换风险 • 学习成本</text>

              {/* Solution 2 Metrics */}
              <rect x="1170" y="550" width="1020" height="100" rx="10" fill="rgba(55, 65, 81, 0.5)" stroke="#6b7280" strokeWidth="2"/>
              <text x="1680" y="575" textAnchor="middle" fontSize="16" fontWeight="bold" fill="white">方案二关键指标</text>
              <text x="1180" y="595" fontSize="12" fill="white">📊 数据同步：复用DBP抽取 | EKKO/EKPO/LIKP/LIPS | T-1增量稳定</text>
              <text x="1180" y="615" fontSize="12" fill="white">📊 性能提升：1000% | 扩展性：优秀 | 数据一致性：优秀</text>
              <text x="1180" y="635" fontSize="12" fill="white">📊 实时性：手动触发当日数据 | 增量全量表机制 | 插入更新高效</text>

              {/* Data Flow Arrows for Solution 2 */}
              {/* From Combined Block to MySQL */}
              <path d="M 1610 270 L 1630 270" stroke="#0891b2" strokeWidth="4" fill="none" markerEnd="url(#dataArrow)"/>
              {/* From MySQL to Microservice */}
              <path d="M 1810 270 L 1830 270" stroke="#0891b2" strokeWidth="4" fill="none" markerEnd="url(#dataArrow)"/>
            </g>

            {/* Architecture Evolution Path */}
            <g id="evolutionPath">
              <rect x="70" y="680" width="2080" height="100" rx="15" fill="rgba(99, 102, 241, 0.1)" stroke="#6366f1" strokeWidth="2" strokeDasharray="8,4"/>
              <text x="1110" y="705" textAnchor="middle" fontSize="20" fontWeight="bold" fill="#6366f1">架构演进路径：逐步迁移到MySQL统一架构</text>

              {/* Evolution Steps */}
              <rect x="100" y="720" width="480" height="50" rx="8" fill="rgba(59, 130, 246, 0.2)" stroke="#3b82f6" strokeWidth="1"/>
              <text x="340" y="740" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">第一阶段：方案一实施</text>
              <text x="340" y="755" textAnchor="middle" fontSize="11" fill="white">返利计算结果和报表数据存储到MySQL，为后续迁移奠定基础</text>

              <path d="M 580 745 L 620 745" stroke="#6366f1" strokeWidth="3" fill="none" markerEnd="url(#processArrow)"/>

              <rect x="620" y="720" width="480" height="50" rx="8" fill="rgba(139, 92, 246, 0.2)" stroke="#8b5cf6" strokeWidth="1"/>
              <text x="860" y="740" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">第二阶段：数据逐步迁移</text>
              <text x="860" y="755" textAnchor="middle" fontSize="11" fill="white">业务数据逐步从HANA同步到MySQL，建立双写验证机制</text>

              <path d="M 1100 745 L 1140 745" stroke="#6366f1" strokeWidth="3" fill="none" markerEnd="url(#processArrow)"/>

              <rect x="1140" y="720" width="480" height="50" rx="8" fill="rgba(5, 150, 105, 0.2)" stroke="#059669" strokeWidth="1"/>
              <text x="1380" y="740" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">第三阶段：方案二完成</text>
              <text x="1380" y="755" textAnchor="middle" fontSize="11" fill="white">完全切换到纯MySQL+微服务架构，实现技术现代化升级</text>

              <path d="M 1620 745 L 1660 745" stroke="#6366f1" strokeWidth="3" fill="none" markerEnd="url(#processArrow)"/>

              <rect x="1660" y="720" width="420" height="50" rx="8" fill="rgba(34, 197, 94, 0.2)" stroke="#22c55e" strokeWidth="1"/>
              <text x="1870" y="740" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">最终目标：统一架构</text>
              <text x="1870" y="755" textAnchor="middle" fontSize="11" fill="white">所有返利计算、结果存储、报表生成统一依赖MySQL集群</text>
            </g>




          </svg>
        </div>
      </div>
    </div>
  )
}
