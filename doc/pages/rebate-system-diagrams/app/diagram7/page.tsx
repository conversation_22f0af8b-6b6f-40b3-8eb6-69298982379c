'use client'

import React from 'react'

export default function Diagram9() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-slate-900 p-4">
      <div className="max-w-[2000px] mx-auto h-screen flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center gap-3">
            <h1 className="text-3xl font-bold text-white">返利系统宏观技术架构演进图</h1>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-10 h-10 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center">
              <div className="w-7 h-7 bg-white rounded-full flex items-center justify-center">
                <div className="w-4 h-4 bg-gradient-to-r from-red-500 to-pink-500 rounded-full"></div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-red-400 font-bold text-lg">商济健康</div>
              <div className="text-red-300 text-sm">COWELL HEALTH</div>
            </div>
          </div>
        </div>

        {/* Main Architecture Evolution Diagram */}
        <div className="flex-1 bg-gradient-to-br from-slate-800/50 to-gray-800/50 rounded-3xl p-8 border border-slate-600/50 relative overflow-hidden">
          {/* Architecture SVG */}
          <svg viewBox="0 0 2400 1100" className="w-full h-full">
            <defs>
              {/* Gradients */}
              <linearGradient id="currentGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#dc2626" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#991b1b" stopOpacity="0.9"/>
              </linearGradient>
              <linearGradient id="phase1Grad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#0891b2" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#0e7490" stopOpacity="0.9"/>
              </linearGradient>
              <linearGradient id="phase2Grad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#059669" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#047857" stopOpacity="0.9"/>
              </linearGradient>
              <linearGradient id="phase3Grad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#7c3aed" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#5b21b6" stopOpacity="0.9"/>
              </linearGradient>
              <linearGradient id="targetGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#f59e0b" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#d97706" stopOpacity="0.9"/>
              </linearGradient>

              {/* Arrow markers */}
              <marker id="evolutionArrow" markerWidth="15" markerHeight="10" refX="14" refY="5" orient="auto">
                <polygon points="0 0, 15 5, 0 10" fill="#7c3aed"/>
              </marker>
              <marker id="dataArrow" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
                <polygon points="0 0, 12 4, 0 8" fill="#0891b2"/>
              </marker>
            </defs>

            {/* Background grid */}
            <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
              <path d="M 50 0 L 0 0 0 50" fill="none" stroke="#374151" strokeWidth="1" opacity="0.2"/>
            </pattern>
            <rect width="2000" height="1100" fill="url(#grid)"/>
            {/* Evolution Timeline */}
            <g id="evolutionTimeline">
              <rect x="50" y="90" width="2000" height="45" rx="12" fill="url(#targetGrad)" stroke="#f59e0b" strokeWidth="3"/>
              <text x="1000" y="110" textAnchor="middle" fontSize="18" fontWeight="bold" fill="white">返利系统技术架构演进图 - 完全替换SAP目标架构</text>
              <text x="1000" y="125" textAnchor="middle" fontSize="11" fill="white">SAP+HANA → 双写并行 → 计算引擎 → 完整微服务 → 智能返利引擎 | 四期渐进式改造 | 零业务中断 | 最终完全替换SAP</text>
            </g>

            {/* Phase Headers */}
            <g id="phaseHeaders">
              <rect x="50" y="155" width="380" height="50" rx="8" fill="url(#currentGrad)" stroke="#dc2626" strokeWidth="2"/>
              <text x="240" y="185" textAnchor="middle" fontSize="18" fontWeight="bold" fill="white">现有架构 (SAP+HANA)</text>

              <rect x="450" y="155" width="380" height="50" rx="8" fill="url(#phase1Grad)" stroke="#0891b2" strokeWidth="2"/>
              <text x="640" y="185" textAnchor="middle" fontSize="18" fontWeight="bold" fill="white">一期改造 (双写并行)</text>

              <rect x="850" y="155" width="380" height="50" rx="8" fill="url(#phase2Grad)" stroke="#059669" strokeWidth="2"/>
              <text x="1040" y="185" textAnchor="middle" fontSize="18" fontWeight="bold" fill="white">二期改造 (计算引擎)</text>

              <rect x="1250" y="155" width="380" height="50" rx="8" fill="url(#phase3Grad)" stroke="#7c3aed" strokeWidth="2"/>
              <text x="1440" y="185" textAnchor="middle" fontSize="18" fontWeight="bold" fill="white">三期改造 (结算报表)</text>

              <rect x="1650" y="155" width="380" height="50" rx="8" fill="url(#targetGrad)" stroke="#f59e0b" strokeWidth="2"/>
              <text x="1840" y="185" textAnchor="middle" fontSize="18" fontWeight="bold" fill="white">四期目标 (返利引擎替换SAP返利模块)</text>
            </g>

            {/* Current Architecture */}
            <g id="currentArchitecture">
              <rect x="50" y="225" width="380" height="740" rx="15" fill="url(#currentGrad)" stroke="#dc2626" strokeWidth="3"/>
              
              {/* SAP GUI Layer */}
              <rect x="70" y="280" width="340" height="80" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="240" y="305" textAnchor="middle" fontSize="16" fontWeight="bold" fill="white">SAP操作痛点</text>
              <text x="80" y="325" fontSize="12" fill="white">• 传统桌面客户端 • 用户体验陈旧</text>
              <text x="80" y="340" fontSize="12" fill="white">• 响应时间较慢 • 批导功能不完善</text>
              <text x="80" y="355" fontSize="12" fill="white">• 界面操作复杂 • 扩展性受限</text>

              {/* ABAP Business Logic */}
              <rect x="70" y="380" width="340" height="220" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="240" y="405" textAnchor="middle" fontSize="16" fontWeight="bold" fill="white">SAP ABAP 业务逻辑层</text>

              {/* First Row - Contract, Clause, Agreement */}
              <rect x="80" y="420" width="100" height="70" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="130" y="440" textAnchor="middle" fontSize="11" fontWeight="bold" fill="white">ZRED0040</text>
              <text x="130" y="455" textAnchor="middle" fontSize="10" fill="white">合同管理</text>
              <text x="85" y="470" fontSize="9" fill="white">• 合同创建</text>
              <text x="85" y="480" fontSize="9" fill="white">• 审批流程</text>

              <rect x="190" y="420" width="100" height="70" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="240" y="440" textAnchor="middle" fontSize="11" fontWeight="bold" fill="white">ZRED0041</text>
              <text x="240" y="455" textAnchor="middle" fontSize="10" fill="white">条款管理</text>
              <text x="195" y="470" fontSize="9" fill="white">• 条款配置</text>
              <text x="195" y="480" fontSize="9" fill="white">• 算法设置</text>

              <rect x="300" y="420" width="100" height="70" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="350" y="440" textAnchor="middle" fontSize="11" fontWeight="bold" fill="white">ZRED0056</text>
              <text x="350" y="455" textAnchor="middle" fontSize="10" fill="white">协议管理</text>
              <text x="305" y="470" fontSize="9" fill="white">• 协议生成</text>
              <text x="305" y="480" fontSize="9" fill="white">• 批量处理</text>

              {/* Second Row - Calculation and Settlement */}
              <rect x="80" y="500" width="100" height="90" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="130" y="520" textAnchor="middle" fontSize="11" fontWeight="bold" fill="white">ZRED0050</text>
              <text x="130" y="535" textAnchor="middle" fontSize="10" fill="white">返利计算</text>
              <text x="85" y="550" fontSize="9" fill="white">• 返利分摊</text>
              <text x="85" y="565" fontSize="9" fill="white">• 门店+商品</text>
              <text x="85" y="580" fontSize="9" fill="white">• 渠道+商品</text>

              <rect x="190" y="500" width="100" height="90" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="240" y="520" textAnchor="middle" fontSize="11" fontWeight="bold" fill="white">ZRED0060</text>
              <text x="240" y="535" textAnchor="middle" fontSize="10" fill="white">结算管理</text>
              <text x="195" y="550" fontSize="9" fill="white">• 结算单-内部</text>
              <text x="195" y="565" fontSize="9" fill="white">• 结算单-外部</text>
              <text x="195" y="580" fontSize="9" fill="white">• 结算单-加盟</text>

              <rect x="300" y="500" width="100" height="90" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="350" y="520" textAnchor="middle" fontSize="11" fontWeight="bold" fill="white">ZRED0070</text>
              <text x="350" y="535" textAnchor="middle" fontSize="10" fill="white">兑付管理</text>
              <text x="305" y="550" fontSize="9" fill="white">• 返利兑付-返现</text>
              <text x="305" y="565" fontSize="9" fill="white">• 返利兑付-票折</text>
              <text x="305" y="580" fontSize="9" fill="white">• 返利兑付-账扣</text>

              {/* SAP HANA Database */}
              <rect x="70" y="620" width="340" height="80" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="240" y="640" textAnchor="middle" fontSize="16" fontWeight="bold" fill="white">SAP HANA 数据库</text>
              <text x="80" y="660" fontSize="11" fill="white">• ZRETA001(合同) • ZRETA002(条款) • ZRET0006(协议)</text>
              <text x="80" y="675" fontSize="11" fill="white">• 集中式存储 • 高性能内存计算 • 实时数据处理</text>
              <text x="80" y="690" fontSize="11" fill="white">• 数据完整性约束 • 事务一致性保障</text>

            </g>

            {/* Phase 1 Architecture - Dual Write Parallel */}
            <g id="phase1Architecture">
              <rect x="450" y="225" width="380" height="740" rx="15" fill="url(#phase1Grad)" stroke="#0891b2" strokeWidth="3"/>

              {/* Microservices Layer */}
              <rect x="470" y="300" width="340" height="160" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="640" y="325" textAnchor="middle" fontSize="16" fontWeight="bold" fill="white">微服务改造</text>

              <rect x="480" y="340" width="100" height="110" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="530" y="360" textAnchor="middle" fontSize="12" fontWeight="bold" fill="white">合同管理</text>
              <text x="485" y="390" fontSize="9" fill="white">• REST API</text>
              <text x="485" y="405" fontSize="9" fill="white">• 业务逻辑</text>
              <text x="485" y="420" fontSize="9" fill="white">• 数据校验</text>
              <text x="485" y="435" fontSize="9" fill="white">• 审批流程</text>

              <rect x="590" y="340" width="100" height="110" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="640" y="360" textAnchor="middle" fontSize="11" fontWeight="bold" fill="white">条款/协议管理</text>
              <text x="595" y="390" fontSize="9" fill="white">• 返利规则</text>
              <text x="595" y="405" fontSize="9" fill="white">• 计算方式</text>
              <text x="595" y="420" fontSize="9" fill="white">• 执行细节</text>
              <text x="595" y="435" fontSize="9" fill="white">• 结算方式</text>

              <rect x="700" y="340" width="100" height="110" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="750" y="360" textAnchor="middle" fontSize="12" fontWeight="bold" fill="white">数据同步</text>
              <text x="705" y="390" fontSize="9" fill="white">• 实时同步</text>
              <text x="705" y="405" fontSize="9" fill="white">• 一致性校验</text>
              <text x="705" y="420" fontSize="9" fill="white">• 异常处理</text>
              <text x="705" y="435" fontSize="9" fill="white">• 监控告警</text>

              {/* Dual Database Architecture */}
              <rect x="470" y="480" width="340" height="120" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="640" y="505" textAnchor="middle" fontSize="16" fontWeight="bold" fill="white">双数据库架构</text>

              <rect x="480" y="520" width="150" height="70" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="555" y="540" textAnchor="middle" fontSize="12" fontWeight="bold" fill="white">SAP HANA</text>
              <text x="555" y="555" textAnchor="middle" fontSize="10" fill="white">(主数据库)</text>
              <text x="485" y="570" fontSize="9" fill="white">• 保持现有结构</text>
              <text x="485" y="585" fontSize="9" fill="white">• DB视角看不推荐双写? 资源视角?</text>

              <rect x="650" y="520" width="150" height="70" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="725" y="540" textAnchor="middle" fontSize="12" fontWeight="bold" fill="white">MySQL 8.0</text>
              <text x="725" y="555" textAnchor="middle" fontSize="10" fill="white">(目标数据库)</text>
              <text x="655" y="570" fontSize="9" fill="white">• 异构表映射</text>
              <text x="655" y="585" fontSize="9" fill="white">• 定时/手动同步</text>

              {/* Phase 1 Benefits */}
              <rect x="470" y="620" width="340" height="120" rx="8" fill="rgba(34, 197, 94, 0.2)" stroke="#22c55e" strokeWidth="2"/>
              <text x="640" y="645" textAnchor="middle" fontSize="16" fontWeight="bold" fill="#22c55e">一期改造收益</text>
              <text x="480" y="665" fontSize="12" fill="white">✅ 用户体验提升：响应时间大幅优化</text>
              <text x="480" y="685" fontSize="12" fill="white">✅ 开发效率提升：微服务+低代码开发模式</text>
              <text x="480" y="705" fontSize="12" fill="white">✅ 扩展性增强：模块化架构设计</text>
              <text x="480" y="725" fontSize="12" fill="white">✅ 运维优化：标准化部署和监控</text>

              {/* Phase 1 Strategy */}
              <rect x="470" y="760" width="340" height="120" rx="8" fill="rgba(59, 130, 246, 0.2)" stroke="#3b82f6" strokeWidth="2"/>
              <text x="640" y="785" textAnchor="middle" fontSize="16" fontWeight="bold" fill="#3b82f6">一期核心策略</text>
              <text x="480" y="805" fontSize="12" fill="white">🔄 双写并行：SAP HANA + MySQL 实时同步</text>
              <text x="480" y="825" fontSize="12" fill="white">🔄 数据校验：99.99%一致性保障</text>
              <text x="480" y="845" fontSize="12" fill="white">🔄 零停机：业务无感知切换，快速回滚</text>
              <text x="480" y="865" fontSize="12" fill="white">🔄 渐进式：分模块逐步迁移</text>


            </g>

            {/* Phase 2 Architecture - Calculation Engine */}
            <g id="phase2Architecture">
              <rect x="850" y="225" width="380" height="740" rx="15" fill="url(#phase2Grad)" stroke="#059669" strokeWidth="3"/>


              {/* Calculation Engine */}
              <rect x="870" y="300" width="340" height="180" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="1040" y="325" textAnchor="middle" fontSize="16" fontWeight="bold" fill="white">返利计算引擎集群</text>

              <rect x="880" y="340" width="100" height="130" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="930" y="360" textAnchor="middle" fontSize="12" fontWeight="bold" fill="white">返利计算引擎</text>
              <text x="885" y="390" fontSize="9" fill="white">• 返利算法</text>
              <text x="885" y="405" fontSize="9" fill="white">• 阶梯计算</text>
              <text x="885" y="420" fontSize="9" fill="white">• 促销返利</text>
              <text x="885" y="435" fontSize="9" fill="white">• 年度累计</text>
              <text x="885" y="450" fontSize="9" fill="white">• 多线程批量处理</text>

              <rect x="990" y="340" width="100" height="130" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1040" y="360" textAnchor="middle" fontSize="12" fontWeight="bold" fill="white">算法版本</text>
              <text x="995" y="390" fontSize="9" fill="white">• 业务规则</text>
              <text x="995" y="405" fontSize="9" fill="white">• 条件判断</text>
              <text x="995" y="420" fontSize="9" fill="white">• 动态配置</text>
              <text x="995" y="435" fontSize="9" fill="white">• 规则验证</text>

              <rect x="1100" y="340" width="100" height="130" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1150" y="360" textAnchor="middle" fontSize="12" fontWeight="bold" fill="white">数据分析</text>
              <text x="1105" y="390" fontSize="9" fill="white">• 数据统计</text>
              <text x="1105" y="405" fontSize="9" fill="white">• 趋势分析</text>
              <text x="1105" y="420" fontSize="9" fill="white">• 对比测算</text>
              <text x="1105" y="435" fontSize="9" fill="white">• 智能报表</text>
              <text x="1105" y="450" fontSize="9" fill="white">• 决策支持</text>

              {/* Enhanced Database */}
              <rect x="870" y="500" width="340" height="100" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="1040" y="525" textAnchor="middle" fontSize="16" fontWeight="bold" fill="white">增强数据架构</text>
              <text x="880" y="545" fontSize="12" fill="white">• MySQL主从集群 • Redis分布式缓存</text>
              <text x="880" y="565" fontSize="12" fill="white">• 数据分片策略 • 读写分离优化</text>
              <text x="880" y="585" fontSize="12" fill="white">• 实时数据同步 • 备份恢复机制</text>

              {/* Phase 2 Benefits */}
              <rect x="870" y="620" width="340" height="120" rx="8" fill="rgba(34, 197, 94, 0.2)" stroke="#22c55e" strokeWidth="2"/>
              <text x="1040" y="645" textAnchor="middle" fontSize="16" fontWeight="bold" fill="#22c55e">二期改造收益</text>
              <text x="880" y="665" fontSize="12" fill="white">✅ 计算性能：高性能计算，支持复杂算法</text>
              <text x="880" y="685" fontSize="12" fill="white">✅ 业务敏捷：规则配置实时生效</text>
              <text x="880" y="705" fontSize="12" fill="white">✅ 算法灵活：多版本算法并存</text>
              <text x="880" y="725" fontSize="12" fill="white">✅ 扩展能力：弹性伸缩支持</text>

              {/* Phase 2 Strategy */}
              <rect x="870" y="760" width="340" height="120" rx="8" fill="rgba(59, 130, 246, 0.2)" stroke="#3b82f6" strokeWidth="2"/>
              <text x="1040" y="785" textAnchor="middle" fontSize="16" fontWeight="bold" fill="#3b82f6">二期核心策略</text>
              <text x="880" y="805" fontSize="12" fill="white">🚀 计算引擎：微服务化，支持弹性扩容</text>
              <text x="880" y="825" fontSize="12" fill="white">🚀 实时性：高性能响应，实时计算</text>
              <text x="880" y="845" fontSize="12" fill="white">🚀 版本管理：多算法版本并行运行</text>
              <text x="880" y="865" fontSize="12" fill="white">🚀 数据优化：缓存策略和分片设计</text>

            </g>

            {/* Phase 3 Architecture - Settlement & Reporting */}
            <g id="phase3Architecture">
              <rect x="1250" y="225" width="380" height="740" rx="15" fill="url(#phase3Grad)" stroke="#7c3aed" strokeWidth="3"/>


              {/* Complete Microservices */}
              <rect x="1270" y="300" width="340" height="180" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="1440" y="325" textAnchor="middle" fontSize="16" fontWeight="bold" fill="white">微服务生态</text>

              <rect x="1280" y="340" width="100" height="130" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1330" y="360" textAnchor="middle" fontSize="11" fontWeight="bold" fill="white">结算功能</text>
              <text x="1285" y="390" fontSize="9" fill="white">• 自动结算</text>
              <text x="1285" y="405" fontSize="9" fill="white">• 对账处理</text>
              <text x="1285" y="420" fontSize="9" fill="white">• 发票管理</text>
              <text x="1285" y="435" fontSize="9" fill="white">• 付款流程</text>
              <text x="1285" y="450" fontSize="9" fill="white">• 财务集成</text>
              <text x="1285" y="465" fontSize="9" fill="white">• 风险控制</text>

              <rect x="1390" y="340" width="100" height="130" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1440" y="360" textAnchor="middle" fontSize="11" fontWeight="bold" fill="white">数享报表</text>
              <text x="1395" y="390" fontSize="9" fill="white">• 动态报表</text>
              <text x="1395" y="405" fontSize="9" fill="white">• 数据可视化</text>
              <text x="1395" y="420" fontSize="9" fill="white">• 自助分析</text>
              <text x="1395" y="435" fontSize="9" fill="white">• 定时推送</text>
              <text x="1395" y="450" fontSize="9" fill="white">• 权限控制</text>
              <text x="1395" y="465" fontSize="9" fill="white">• 导出功能</text>

              <rect x="1500" y="340" width="100" height="130" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1550" y="360" textAnchor="middle" fontSize="11" fontWeight="bold" fill="white">串联外部系统</text>
              <text x="1505" y="390" fontSize="9" fill="white">• 财务开票</text>
              <text x="1505" y="405" fontSize="9" fill="white">• 客商结算</text>
              <text x="1505" y="420" fontSize="9" fill="white">• 丰货（管报）</text>
              <text x="1505" y="435" fontSize="9" fill="white">• 智店通</text>
              <text x="1505" y="450" fontSize="9" fill="white">• 发票校验</text>

              {/* Advanced Data Platform */}
              <rect x="1270" y="500" width="340" height="100" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="1440" y="525" textAnchor="middle" fontSize="16" fontWeight="bold" fill="white">高级数据平台</text>
              <text x="1280" y="545" fontSize="12" fill="white">• 复用数享 • 实时数据流</text>
              <text x="1280" y="565" fontSize="12" fill="white">• 多维分析 • 方便决策</text>
              <text x="1280" y="585" fontSize="12" fill="white">• 智能预测 • 自动化运营</text>

              {/* Phase 3 Benefits */}
              <rect x="1270" y="620" width="340" height="120" rx="8" fill="rgba(34, 197, 94, 0.2)" stroke="#22c55e" strokeWidth="2"/>
              <text x="1440" y="645" textAnchor="middle" fontSize="16" fontWeight="bold" fill="#22c55e">三期改造收益</text>
              <text x="1280" y="665" fontSize="12" fill="white">✅ 决策实时化：快速数据更新</text>
              <text x="1280" y="685" fontSize="12" fill="white">✅ 成本优化：运维成本大幅降低</text>
              <text x="1280" y="705" fontSize="12" fill="white">✅ 智能运营：自动化流程管理</text>
              <text x="1280" y="725" fontSize="12" fill="white">✅ 数据洞察：多维度分析决策</text>

              {/* Phase 3 Strategy */}
              <rect x="1270" y="760" width="340" height="120" rx="8" fill="rgba(59, 130, 246, 0.2)" stroke="#3b82f6" strokeWidth="2"/>
              <text x="1440" y="785" textAnchor="middle" fontSize="16" fontWeight="bold" fill="#3b82f6">三期核心策略</text>
              <text x="1280" y="805" fontSize="12" fill="white">📊 数据驱动：全链路数据分析</text>
              <text x="1280" y="825" fontSize="12" fill="white">📊 自动化：端到端流程自动化</text>
              <text x="1280" y="845" fontSize="12" fill="white">📊 智能化：AI辅助决策支持</text>
              <text x="1280" y="865" fontSize="12" fill="white">📊 生态化：完整业务闭环</text>


            </g>

            {/* Phase 4 Architecture - Target Complete Architecture */}
            <g id="phase4Architecture">
              <rect x="1650" y="225" width="380" height="740" rx="15" fill="url(#targetGrad)" stroke="#f59e0b" strokeWidth="3"/>

              {/* New Rebate Engine Core */}
              <rect x="1670" y="250" width="340" height="180" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="1840" y="275" textAnchor="middle" fontSize="16" fontWeight="bold" fill="white">新返利引擎核心</text>

              <rect x="1680" y="290" width="100" height="130" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1730" y="310" textAnchor="middle" fontSize="11" fontWeight="bold" fill="white">灵活拓展合同</text>
              <text x="1730" y="325" textAnchor="middle" fontSize="10" fill="white">引擎规则</text>
              <text x="1685" y="355" fontSize="9" fill="white">• 新增模式</text>
              <text x="1685" y="370" fontSize="9" fill="white">• 条例测算</text>
              <text x="1685" y="385" fontSize="9" fill="white">• 合规检查</text>
              <text x="1685" y="400" fontSize="9" fill="white">• 版本管理</text>
              <text x="1685" y="415" fontSize="9" fill="white">• 生命周期</text>

              <rect x="1790" y="290" width="100" height="130" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1840" y="310" textAnchor="middle" fontSize="11" fontWeight="bold" fill="white">高性能</text>
              <text x="1840" y="325" textAnchor="middle" fontSize="10" fill="white">计算引擎</text>
              <text x="1795" y="340" fontSize="9" fill="white">• 分布式计算</text>
              <text x="1795" y="355" fontSize="9" fill="white">• 实时处理</text>
              <text x="1795" y="370" fontSize="9" fill="white">• 多算法并行</text>
              <text x="1795" y="385" fontSize="9" fill="white">• 弹性扩容</text>
              <text x="1795" y="400" fontSize="9" fill="white">• 性能监控</text>
              <text x="1795" y="415" fontSize="9" fill="white">• 负载均衡</text>

              <rect x="1900" y="290" width="100" height="130" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1950" y="310" textAnchor="middle" fontSize="11" fontWeight="bold" fill="white">智能决策</text>
              <text x="1950" y="325" textAnchor="middle" fontSize="10" fill="white">支持系统</text>
              <text x="1905" y="340" fontSize="9" fill="white">• 数据挖掘</text>
              <text x="1905" y="355" fontSize="9" fill="white">• 预测分析</text>
              <text x="1905" y="370" fontSize="9" fill="white">• 智能推荐</text>
              <text x="1905" y="385" fontSize="9" fill="white">• 异常检测</text>
              <text x="1905" y="400" fontSize="9" fill="white">• 业务洞察</text>
              <text x="1905" y="415" fontSize="9" fill="white">• 决策优化</text>

              {/* External System Integration */}
              <rect x="1670" y="450" width="340" height="100" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="1840" y="475" textAnchor="middle" fontSize="16" fontWeight="bold" fill="white">外部系统集成层</text>
              <text x="1680" y="495" fontSize="12" fill="white">• BDP数据源：销售/采购/配送数据实时接入</text>
              <text x="1680" y="515" fontSize="12" fill="white">• 金蝶系统：开票/财务数据双向同步</text>
              <text x="1680" y="535" fontSize="12" fill="white">• SRM平台：勾票对账/发票校验/供应商管理</text>

              {/* Cloud Native Data Platform */}
              <rect x="1670" y="570" width="340" height="120" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="1840" y="595" textAnchor="middle" fontSize="16" fontWeight="bold" fill="white">云原生数据平台</text>
              <text x="1680" y="615" fontSize="12" fill="white">• MySQL集群：主从分离、读写分离、分库分表</text>
              <text x="1680" y="635" fontSize="12" fill="white">• Redis集群：分布式缓存、会话管理、实时计算</text>
              <text x="1680" y="655" fontSize="12" fill="white">• 消息队列：异步处理、事件驱动、削峰填谷</text>
              <text x="1680" y="675" fontSize="12" fill="white">• 数据湖：历史数据、分析挖掘、机器学习</text>

              {/* Phase 4 Benefits */}
              <rect x="1670" y="710" width="340" height="120" rx="8" fill="rgba(34, 197, 94, 0.2)" stroke="#22c55e" strokeWidth="2"/>
              <text x="1840" y="735" textAnchor="middle" fontSize="16" fontWeight="bold" fill="#22c55e">四期最终收益</text>
              <text x="1680" y="755" fontSize="12" fill="white">✅ SAP完全替换：彻底摆脱传统系统束缚</text>
              <text x="1680" y="775" fontSize="12" fill="white">✅ 智能化运营：数享分析驱动的业务决策支持</text>
              <text x="1680" y="795" fontSize="12" fill="white">✅ 云原生架构：弹性扩容、高可用、低成本</text>
              <text x="1680" y="815" fontSize="12" fill="white">✅ 生态化集成：与所有外部系统无缝对接</text>

              {/* Phase 4 Strategy */}
              <rect x="1670" y="850" width="340" height="100" rx="8" fill="rgba(59, 130, 246, 0.2)" stroke="#3b82f6" strokeWidth="2"/>
              <text x="1840" y="875" textAnchor="middle" fontSize="16" fontWeight="bold" fill="#3b82f6">四期核心策略</text>
              <text x="1680" y="895" fontSize="12" fill="white">🚀 完全替换：新返利引擎100%替换SAP功能</text>
              <text x="1680" y="915" fontSize="12" fill="white">🚀 数享分析：数享辅助分析,驱动业务创新</text>
              <text x="1680" y="935" fontSize="12" fill="white">🚀 生态整合：打通所有业务系统数据流</text>


            </g>

            {/* Bottom Summary */}
            <g id="bottomSummary">
              <rect x="50" y="980" width="2000" height="65" rx="12" fill="rgba(55, 65, 81, 0.8)" stroke="#6b7280" strokeWidth="2"/>
              <text x="1200" y="1000" textAnchor="middle" fontSize="16" fontWeight="bold" fill="white">
                重构返利系统的核心价值-"开着飞机换引擎"
              </text>

              {/* First Row - Core Values */}
              <text x="150" y="1020" fontSize="11" fill="white">
                ✅ 零业务中断：7×24小时不间断服务
              </text>
              <text x="550" y="1020" fontSize="11" fill="white">
                ✅ 数据安全：100%数据完整性保障
              </text>
              <text x="950" y="1020" fontSize="11" fill="white">
                ✅ 平滑演进：渐进式改造，风险可控
              </text>
              <text x="1350" y="1020" fontSize="11" fill="white">
                ✅ 技术现代化：拥抱云原生生态
              </text>
              <text x="1750" y="1020" fontSize="11" fill="white">
                ✅ 替换SAP返利模块：智能返利引擎
              </text>

              {/* Second Row - Implementation Strategy */}
              <text x="200" y="1038" fontSize="11" fill="white">
                🔄 双写并行：SAP HANA + MySQL 实时同步
              </text>
              <text x="700" y="1038" fontSize="11" fill="white">
                🔄 快速回滚：应急预案，故障恢复
              </text>
              <text x="1200" y="1038" fontSize="11" fill="white">
                🔄 用户平滑切换：体验升级，操作习惯保持
              </text>
              <text x="1700" y="1038" fontSize="11" fill="white">
                🔄 智能化升级：辅助业务决策
              </text>
            </g>
          </svg>
        </div>
      </div>
    </div>
  )
}
