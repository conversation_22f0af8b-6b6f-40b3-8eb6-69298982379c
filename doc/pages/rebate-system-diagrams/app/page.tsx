import Link from "next/link"

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-500 rounded-lg"></div>
            <h1 className="text-4xl font-bold text-white">返利系统架构图集</h1>
            <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg"></div>
          </div>
          <p className="text-xl text-slate-300 max-w-4xl mx-auto">
            从现状分析到重构规划，涵盖功能架构、数据流程、算法设计、战略规划、技术演进、方案对比等10个维度，全面展示返利系统的架构设计与技术演进路径
          </p>
        </div>

        {/* Architecture Diagrams Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
          {/* Existing Diagrams */}
          <Link href="/diagram1" className="group block p-6 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-white">返利二级产品功能框架</h3>
              <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center group-hover:bg-white/30 transition-colors">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
            </div>
            <p className="text-blue-100 text-sm leading-relaxed">
              SAP系统返利功能的详细模块架构图，包含返利功能、返利维护、底层逻辑三大层级，展示合同管理、条款协议、计算分摊等完整功能体系。
            </p>
            <div className="mt-4 flex items-center text-blue-200 text-sm">
              <span className="mr-2">🏗️</span>
              <span>SAP功能架构 • 合同管理 • 返利计算</span>
            </div>
          </Link>

          <Link href="/diagram2" className="group block p-6 bg-gradient-to-br from-green-600 to-emerald-600 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-white">返利数据流程</h3>
              <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center group-hover:bg-white/30 transition-colors">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              </div>
            </div>
            <p className="text-green-100 text-sm leading-relaxed">
              BDP、SAP、其他系统间的数据流转架构图，展示采购配送销售促销数据的抽取处理、返利分摊计算、兑换方式处理的完整数据链路。
            </p>
            <div className="mt-4 flex items-center text-green-200 text-sm">
              <span className="mr-2">🔄</span>
              <span>数据流转 • 系统集成 • 返利分摊</span>
            </div>
          </Link>

          <Link href="/diagram3" className="group block p-6 bg-gradient-to-br from-orange-600 to-red-600 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-white">返利一级产品框架</h3>
              <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center group-hover:bg-white/30 transition-colors">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
                </svg>
              </div>
            </div>
            <p className="text-orange-100 text-sm leading-relaxed">
              返利系统的高层次产品架构图，展示丰货、智店通、BDP、SAP、金蝶、SRM、客商结算平台等系统间的集成关系和数据流转。
            </p>
            <div className="mt-4 flex items-center text-orange-200 text-sm">
              <span className="mr-2">🎯</span>
              <span>产品架构 • 系统集成 • 业务流程</span>
            </div>
          </Link>

          <Link href="/diagram4" className="group block p-6 bg-gradient-to-br from-purple-600 to-pink-600 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-white">返利应用算法初步梳理</h3>
              <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center group-hover:bg-white/30 transition-colors">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
            </div>
            <p className="text-purple-100 text-sm leading-relaxed">
              返利计算算法的详细梳理图，包含返利形式、核算基准、价格维度、计算方法、阶梯类型、分摊规则等六大算法要素的完整配置体系。
            </p>
            <div className="mt-4 flex items-center text-purple-200 text-sm">
              <span className="mr-2">🧮</span>
              <span>算法梳理 • 计算规则 • 配置体系</span>
            </div>
          </Link>

          {/* New Architecture Diagrams */}
          <Link href="/diagram5" className="group block p-6 bg-gradient-to-br from-emerald-600 to-teal-600 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-white">返利引擎四期改造战略</h3>
              <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center group-hover:bg-white/30 transition-colors">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
            </div>
            <p className="text-emerald-100 text-sm leading-relaxed">
              返利引擎分阶段重构架构图，展示2025-2026年四期渐进式改造战略，包含核心数据源、技术基础设施、各阶段交付目标的完整规划。
            </p>
            <div className="mt-4 flex items-center text-emerald-200 text-sm">
              <span className="mr-2">🚀</span>
              <span>四期改造 • 渐进重构 • 技术演进</span>
            </div>
          </Link>

          {/* New Technical Architecture Diagrams */}
          <Link href="/diagram6" className="group block p-6 bg-gradient-to-br from-cyan-600 to-blue-600 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-white">"开着飞机换引擎"技术架构</h3>
              <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center group-hover:bg-white/30 transition-colors">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
            </div>
            <p className="text-cyan-100 text-sm leading-relaxed">
              "开着飞机换引擎"一期技术架构演进图，展示零停机迁移策略，包含SAP现有架构、数据同步层、新架构演进的完整技术方案。
            </p>
            <div className="mt-4 flex items-center text-cyan-200 text-sm">
              <span className="mr-2">✈️</span>
              <span>零停机迁移 • 架构演进 • 双写同步</span>
            </div>
          </Link>

          <Link href="/diagram7" className="group block p-6 bg-gradient-to-br from-teal-600 to-emerald-600 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-white">返利系统宏观技术架构演进</h3>
              <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center group-hover:bg-white/30 transition-colors">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
              </div>
            </div>
            <p className="text-teal-100 text-sm leading-relaxed">
              返利系统宏观技术架构演进图，展示从现状到目标架构的完整演进路径，包含当前架构、三个演进阶段、目标云原生架构的全景视图。
            </p>
            <div className="mt-4 flex items-center text-teal-200 text-sm">
              <span className="mr-2">🌐</span>
              <span>宏观架构 • 演进路径 • 云原生</span>
            </div>
          </Link>
          <Link href="/diagram8" className="group block p-6 bg-gradient-to-br from-indigo-600 to-purple-600 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-white">返利系统技术方案决策对比</h3>
              <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center group-hover:bg-white/30 transition-colors">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
              </div>
            </div>
            <p className="text-indigo-100 text-sm leading-relaxed">
              返利系统技术方案决策对比图，专为领导决策设计，对比HANA+MySQL混合架构与纯MySQL+微服务架构两种技术方案。
            </p>
            <div className="mt-4 flex items-center text-indigo-200 text-sm">
              <span className="mr-2">⚖️</span>
              <span>方案对比 • 领导决策 • 技术选型</span>
            </div>
          </Link>




        </div>

        {/* Footer */}
        <div className="text-center text-slate-400 text-sm mt-12 p-6 border-t border-slate-700">
          <div className="flex items-center justify-center gap-2 mb-2">
            <div className="w-4 h-4 bg-gradient-to-r from-red-500 to-pink-500 rounded-full"></div>
            <span className="text-red-400 font-bold">商济健康</span>
            <span className="text-red-300">COWELL HEALTH</span>
          </div>
          <p>返利系统完整架构图集 (10图) | 从现状到重构的全景技术方案 | 企业数字化转型核心项目 | 内部资料，严格保密</p>
        </div>
      </div>
    </div>
  )
}
